{"name": "g_veg", "version": "0.0.1", "author": "Ionic Framework", "homepage": "https://ionicframework.com/", "private": true, "description": "An Ionic project", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "app-build": "ionic build --prod && npm run cap:sync:android && npx cap open android", "cap:sync": "npx cap sync && node scripts/permissions-manager.js", "cap:sync:android": "npx cap sync android && node scripts/permissions-manager.js android", "cap:sync:ios": "npx cap sync ios && node scripts/permissions-manager.js ios", "cap:sync:full": "node scripts/permissions-manager.js sync", "add-bluetooth-permissions": "node scripts/add-bluetooth-permissions.js", "add-ios-permissions": "node scripts/add-ios-permissions.js", "setup-permissions": "node scripts/permissions-manager.js", "permissions-status": "node scripts/permissions-manager.js status"}, "dependencies": {"@angular/common": "~15.2.10", "@angular/core": "~15.2.10", "@angular/forms": "~15.2.10", "@angular/platform-browser": "~15.2.10", "@angular/platform-browser-dynamic": "~15.2.10", "@angular/router": "~15.2.10", "@awesome-cordova-plugins/android-permissions": "^6.6.0", "@awesome-cordova-plugins/bluetooth-serial": "^6.6.0", "@awesome-cordova-plugins/core": "^6.6.0", "@awesome-cordova-plugins/social-sharing": "^6.6.0", "@bcyesil/capacitor-plugin-printer": "^0.0.5", "@capacitor/android": "^6.0.0", "@capacitor/app": "^6.0.0", "@capacitor/core": "^6.0.0", "@capacitor/device": "^7.0.1", "@capacitor/filesystem": "^6.0.0", "@capacitor/haptics": "^6.0.0", "@capacitor/ios": "^6.0.0", "@capacitor/keyboard": "^6.0.0", "@capacitor/status-bar": "^6.0.0", "@ionic-selectable/angular": "^5.0.0-alpha.19", "@ionic/angular": "^6.5.5", "@ionic/angular-server": "^6.5.5", "@ionic/core": "^6.5.5", "@ionic/storage-angular": "^4.0.0", "chart.js": "^4.4.0", "cordova-plugin-android-permissions": "^1.1.5", "cordova-plugin-androidx": "^3.0.0", "cordova-plugin-androidx-adapter": "^1.1.3", "cordova-plugin-bluetooth-serial": "^0.4.7", "cordova-plugin-device": "^2.0.2", "cordova-plugin-file": "^8.1.3", "cordova-plugin-ionic-keyboard": "^2.0.5", "cordova-plugin-ionic-webview": "^5.0.0", "cordova-plugin-splashscreen": "^5.0.2", "cordova-plugin-statusbar": "^2.4.2", "cordova-plugin-x-socialsharing": "^6.0.4", "es6-promise-plugin": "^4.2.2", "esc-pos-encoder-ionic": "^1.1.3", "export-from-json": "^1.7.4", "ionic-selectable": "^4.9.0", "ionicons": "^6.0.3", "jetifier": "^2.0.0", "moment": "^2.29.4", "ngx-select-dropdown": "^3.3.2", "papaparse": "^5.4.1", "rxjs": "~7.5.0", "tslib": "^2.3.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "~15.2.11", "@angular-eslint/builder": "~14.0.0", "@angular-eslint/eslint-plugin": "~14.0.0", "@angular-eslint/eslint-plugin-template": "~14.0.0", "@angular-eslint/template-parser": "~14.0.0", "@angular/cli": "~15.2.11", "@angular/compiler": "~15.2.10", "@angular/compiler-cli": "~15.2.10", "@angular/language-service": "~15.2.10", "@capacitor/cli": "^6.0.0", "@ionic/angular-toolkit": "^9.0.0", "@types/jasmine": "~3.10.0", "@types/jasminewd2": "~2.0.10", "@types/node": "^14.18.36", "@types/web-bluetooth": "^0.0.20", "@typescript-eslint/eslint-plugin": "^5.3.0", "@typescript-eslint/parser": "^5.3.0", "eslint": "^7.32.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsdoc": "^30.7.6", "eslint-plugin-prefer-arrow": "^1.2.2", "jasmine-core": "~3.10.1", "jasmine-spec-reporter": "~7.0.0", "karma": "~6.4.1", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-coverage-istanbul-reporter": "~3.0.3", "karma-jasmine": "~4.0.1", "karma-jasmine-html-reporter": "^1.7.0", "protractor": "~7.0.0", "ts-node": "~10.9.1", "typescript": "~4.8.4"}, "cordova": {"plugins": {"cordova-plugin-statusbar": {}, "cordova-plugin-device": {}, "cordova-plugin-splashscreen": {}, "cordova-plugin-ionic-webview": {}, "cordova-plugin-ionic-keyboard": {}, "cordova-plugin-bluetooth-serial": {}, "cordova-plugin-android-permissions": {}}}}