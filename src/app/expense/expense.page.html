<app-header [title]="'Expense Management'" [returnUrl]="'tabs/home'"></app-header>

<ion-content class="expense-content">
  <!-- Collapsible Summary Section -->
  <div class="summary-section" *ngIf="data && data.length > 0">
    <div class="summary-header" (click)="toggleSummary()">
      <h3 class="section-title">
        <ion-icon name="analytics-outline" class="section-icon"></ion-icon>
        Expense Summary
      </h3>
      <ion-button fill="clear" class="toggle-button">
        <ion-icon [name]="showSummary ? 'chevron-up' : 'chevron-down'" class="toggle-icon"></ion-icon>
      </ion-button>
    </div>
    <div class="summary-content" [class.expanded]="showSummary">

    <ion-row>
      <ion-col size="6">
        <ion-card class="summary-card total-card">
          <ion-card-content>
            <div class="summary-card-content">
              <div class="summary-info">
                <h3>Total Expenses</h3>
                <h1>{{data.length}}</h1>
                <p>Expense entries</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="receipt" class="summary-icon total"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6">
        <ion-card class="summary-card amount-card">
          <ion-card-content>
            <div class="summary-card-content">
              <div class="summary-info">
                <h3>Total Amount</h3>
                <h1>{{expense_amount_total | currency: 'INR':'symbol':'1.0-0'}}</h1>
                <p>Total spent</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="wallet" class="summary-icon amount"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
    </div>
  </div>

  <!-- Filter Section -->
  <div class="filter-section">
    <h3 class="section-title">
      <ion-icon name="filter-outline" class="section-icon"></ion-icon>
      Filter & Actions
    </h3>

    <ion-card class="filter-card">
      <ion-card-content>
        <ion-row>
          <ion-col size="5">
            <ion-item lines="none" class="date-item">
              <ion-label position="stacked">From Date</ion-label>
              <ion-input
                [(ngModel)]="from_date"
                [value]="from_date | date : 'YYYY-MM-dd'"
                placeholder="Select start date"
                type="date"
                class="date-input">
              </ion-input>
            </ion-item>
          </ion-col>

          <ion-col size="5">
            <ion-item lines="none" class="date-item">
              <ion-label position="stacked">To Date</ion-label>
              <ion-input
                [(ngModel)]="to_date"
                [value]="to_date | date : 'YYYY-MM-dd'"
                placeholder="Select end date"
                type="date"
                class="date-input">
              </ion-input>
            </ion-item>
          </ion-col>

          <ion-col size="2">
            <ion-button
              (click)="filterList()"
              fill="solid"
              expand="block"
              class="filter-button">
              <ion-icon name="search" slot="icon-only"></ion-icon>
            </ion-button>
          </ion-col>
        </ion-row>

        <ion-row>
          <ion-col size="6">
            <ion-button
              (click)="openModal(false,null)"
              fill="solid"
              expand="block"
              class="add-button">
              <ion-icon name="add-circle-outline" slot="start"></ion-icon>
              Add Expense
            </ion-button>
          </ion-col>

          <ion-col size="6">
            <ion-button
              (click)="printStatement()"
              fill="outline"
              expand="block"
              class="print-button">
              <ion-icon name="print-outline" slot="start"></ion-icon>
              Print Report
            </ion-button>
          </ion-col>
        </ion-row>
      </ion-card-content>
    </ion-card>
  </div>
  <!-- Expense List Section -->
  <div class="expense-list-section" *ngIf="data && data.length > 0">
    <h3 class="section-title">
      <ion-icon name="list-outline" class="section-icon"></ion-icon>
      Expense Records
    </h3>

    <div class="expense-cards">
      <ion-card
        *ngFor="let expense of data"
        class="expense-card"
        (click)="view(expense)">
        <ion-card-content>
          <div class="expense-header">
            <div class="expense-info">
              <div class="category-badge">
                <ion-icon name="pricetag" class="category-icon"></ion-icon>
                <span class="category-name">{{expense.category_name}}</span>
              </div>
              <div class="subcategory">{{expense.subcategory_name}}</div>
            </div>
            <div class="expense-amount">
              <span class="amount">{{expense.amount | currency: 'INR':'symbol':'1.0-0'}}</span>
            </div>
          </div>

          <div class="expense-details">
            <div class="expense-notes" *ngIf="expense.notes">
              <ion-icon name="document-text-outline" class="notes-icon"></ion-icon>
              <span class="notes-text">{{expense.notes}}</span>
            </div>
            <div class="expense-date">
              <ion-icon name="calendar-outline" class="date-icon"></ion-icon>
              <span class="date-text">{{expense.created_at | date:'dd/MM/yyyy'}}</span>
            </div>
          </div>

          <div class="expense-actions" (click)="$event.stopPropagation()">
            <ion-button
              (click)="view(expense)"
              fill="clear"
              size="small"
              class="action-button view-button">
              <ion-icon name="eye-outline" slot="icon-only"></ion-icon>
            </ion-button>
            <ion-button
              (click)="openModal(true, expense)"
              fill="clear"
              size="small"
              class="action-button edit-button">
              <ion-icon name="create-outline" slot="icon-only"></ion-icon>
            </ion-button>
            <ion-button
              (click)="delete(expense.id)"
              fill="clear"
              size="small"
              class="action-button delete-button">
              <ion-icon name="trash-outline" slot="icon-only"></ion-icon>
            </ion-button>
          </div>
        </ion-card-content>
      </ion-card>
    </div>
  </div>

  <!-- No Data Message -->
  <div class="no-data-section" *ngIf="!data || data.length === 0">
    <ion-card class="no-data-card">
      <ion-card-content>
        <div class="no-data-content">
          <ion-icon name="receipt-outline" class="no-data-icon"></ion-icon>
          <h3>No Expenses Found</h3>
          <p>Start by adding your first expense record.</p>
          <ion-button
            (click)="openModal(false, null)"
            fill="solid"
            class="add-first-button">
            <ion-icon name="add-circle-outline" slot="start"></ion-icon>
            Add First Expense
          </ion-button>
        </div>
      </ion-card-content>
    </ion-card>
  </div>
  <ion-modal [isOpen]="isViewModalOpen">
    <ng-template>
      <ion-header translucent>
        <ion-toolbar>
          <ion-title>View Expense</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="setViewOpen(false)">Close</ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <ion-content class="ion-padding">
        <ion-item>
          <ion-label>Category:</ion-label>
          <ion-label>{{viewData.category_name}}</ion-label>
        </ion-item>
        <ion-item>
          <ion-label>Sub Category:</ion-label>
          <ion-label>{{viewData.subcategory_name}}</ion-label>
        </ion-item>
        <ion-item>
          <ion-label>Mode Of Payment:</ion-label>
          <ion-label>{{getModeOfPayment(viewData.mode_of_payment)}}</ion-label>
        </ion-item>
        <ion-item>
          <ion-label>Amount:</ion-label>
          <ion-label>{{viewData.amount}}</ion-label>
        </ion-item>
        <ion-item>
          <ion-label>Notes:</ion-label>
          <ion-label>{{viewData.notes}}</ion-label>
        </ion-item>
        <ion-item>
          <ion-label>Created At:</ion-label>
          <ion-text>{{viewData.created_at | date}}</ion-text>
        </ion-item>
        <img (click)="openImg()" *ngIf="viewData.expense_file" src="{{apiUrlClean}}{{viewData.expense_file}}" alt="">
      </ion-content>
    </ng-template>
  </ion-modal>

  <!-- Ends : Main  -->
  <!-- Expense Modal -->
  <ion-modal [isOpen]="isAddModalOpen || isEditModalOpen" class="expense-modal">
    <ng-template>
      <ion-header translucent>
        <ion-toolbar [color]="isEditMode ? 'warning' : 'primary'">
          <ion-title>{{ isEditMode ? 'Edit Expense' : 'Add New Expense' }}</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="closeModal()" fill="clear" color="light">
              <ion-icon name="close-outline"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <ion-content class="ion-padding modal-content">
        <form [formGroup]="expenseForm" (ngSubmit)="onSubmit()">
          <!-- Category Information Section -->
          <div class="form-section">
            <h3 class="section-title">
              <ion-icon name="folder-outline" class="section-icon"></ion-icon>
              Category Information
            </h3>

            <ion-item class="form-item">
              <ion-label for="amount">Category</ion-label>
              <ionic-selectable itemValueField="id" itemTextField="name" [items]="categories" formControlName="category"
                [canSearch]="true" [canAddItem]="true" (onAddItem)="addCategory($event)"
                (onSelect)="onSelectCategory($event)" placeholder="Select or add a category">
              </ionic-selectable>
            </ion-item>

            <ion-item class="form-item">
              <ion-label for="amount">SubCategory</ion-label>
              <ionic-selectable itemValueField="id" itemTextField="name" [items]="subcategories"
                formControlName="subcategory" [canSearch]="true" [canAddItem]="true" (onAddItem)="addSubcategory($event)"
                placeholder="Select or add a subcategory">
              </ionic-selectable>
            </ion-item>
          </div>

          <!-- Payment Information Section -->
          <div class="form-section">
            <h3 class="section-title">
              <ion-icon name="card-outline" class="section-icon"></ion-icon>
              Payment Information
            </h3>

            <div class="form-item">
              <ion-label>Mode of Payment</ion-label>
              <ngx-select-dropdown [config]="modeOfPaymentConfig" [options]="modeOfPaymentOptions" [ngModelOptions]="{standalone: true}"
                [(ngModel)]="modeOfPaymentSelectedOption" (change)="onModeOfPaymentOptionChange($event)">
              </ngx-select-dropdown>
            </div>

            <ion-item class="form-item">
              <ion-label position="stacked">Amount
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input type="number" formControlName="amount" placeholder="0.00"></ion-input>
            </ion-item>
          </div>

          <!-- Additional Information Section -->
          <div class="form-section">
            <h3 class="section-title">
              <ion-icon name="document-text-outline" class="section-icon"></ion-icon>
              Additional Information
            </h3>

            <ion-item class="form-item">
              <ion-label position="stacked">Notes</ion-label>
              <ion-textarea formControlName="notes" placeholder="Enter any additional notes..."></ion-textarea>
            </ion-item>

            <ion-item class="form-item">
              <ion-label position="stacked">Receipt Image</ion-label>
              
              <!-- Image Preview -->
              <div class="image-preview" *ngIf="previewUrl">
                <img [src]="previewUrl" alt="Receipt preview" class="preview-image">
                <ion-button 
                  fill="clear" 
                  size="small" 
                  (click)="clearImageSelection()"
                  class="remove-preview-btn">
                  <ion-icon name="close-circle" slot="icon-only"></ion-icon>
                </ion-button>
              </div>

              <!-- Upload Controls -->
              <div class="upload-controls" *ngIf="!previewUrl">
                <ion-button 
                  expand="block" 
                  fill="outline" 
                  class="select-image-btn"
                  [disabled]="isEditMode"
                  (click)="presentImageOptions()">
                  <ion-icon name="camera-outline" slot="start"></ion-icon>
                  Add Receipt Image
                </ion-button>
              </div>
            </ion-item>

            <!-- Hidden File Inputs -->
            <input 
              #fileInput 
              type="file" 
              accept="image/*" 
              (change)="onFileSelected($event)" 
              style="display: none;">
            
            <input 
              #cameraInput 
              type="file" 
              accept="image/*" 
              capture="environment" 
              (change)="onFileSelected($event)" 
              style="display: none;">
          </div>

          <!-- Form Actions -->
          <div class="form-actions">
            <ion-button
              type="submit"
              [disabled]="expenseForm.invalid"
              expand="block"
              fill="solid"
              shape="round"
              [color]="isEditMode ? 'warning' : 'primary'"
              class="submit-button">
              <ion-icon [name]="isEditMode ? 'create-outline' : 'add-outline'" slot="start"></ion-icon>
              {{ isEditMode ? 'Update Expense' : 'Add Expense' }}
            </ion-button>
          </div>
        </form>
      </ion-content>
    </ng-template>
  </ion-modal>
</ion-content>
<ion-footer>
  <app-floating-menu></app-floating-menu>
</ion-footer>