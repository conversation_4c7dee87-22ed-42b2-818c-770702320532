import { Component, OnInit } from '@angular/core';
import { ModalController, AlertController } from '@ionic/angular';
import { ChecklistService, Checklist } from '../shared/services/checklist.service';
import { ToastService } from '../shared/services/toast.service';
import { IonLoaderService } from '../shared/services/ion-loader.service';
import { BuyerService } from '../shared/services/buyer.service';

@Component({
  selector: 'app-checklist',
  templateUrl: './checklist.page.html',
  styleUrls: ['./checklist.page.scss'],
})
export class ChecklistPage implements OnInit {
  checklists: Checklist[] = [];
  filteredChecklists: Checklist[] = [];

  // Filter options
  statusFilter: string = 'all';
  routeFilter: number | null = null;
  buyerFilter: number | null = null;

  // Data for dropdowns
  routes: any[] = [];
  buyers: any[] = [];

  // Modal states
  isCreateModalOpen = false;
  isDetailModalOpen = false;
  selectedChecklist: Checklist | null = null;

  // Form data for creating checklist
  newChecklist: Partial<Checklist> = {
    title: '',
    description: '',
    priority: 'medium',
    status: 'pending'
  };

  // Summary data
  summary = {
    total: 0,
    pending: 0,
    in_progress: 0,
    completed: 0,
    overdue: 0
  };

  constructor(
    private checklistService: ChecklistService,
    private toastService: ToastService,
    private ionLoaderService: IonLoaderService,
    private modalController: ModalController,
    private alertController: AlertController,
    private buyerService: BuyerService
  ) { }

  ngOnInit() {
    this.loadData();
  }

  async loadData() {
    await this.ionLoaderService.startLoader();
    try {
      await Promise.all([
        this.loadChecklists(),
        this.loadRoutes(),
        this.loadBuyers()
      ]);
      this.updateSummary();
    } catch (error) {
      console.error('Error loading data:', error);
      this.toastService.toastServices('Error loading data', 'danger', 'top');
    } finally {
      this.ionLoaderService.dismissLoader();
    }
  }

  async loadChecklists() {
    try {
      const response = await this.checklistService.getChecklists();
      if (response.success) {
        this.checklists = response.data;
        this.applyFilters();
      } else {
        this.toastService.toastServices(response.message, 'danger', 'top');
      }
    } catch (error) {
      console.error('Error loading checklists:', error);
      this.toastService.toastServices('Error loading checklists', 'danger', 'top');
    }
  }

  async loadRoutes() {
    try {
      const response: any = await this.buyerService.getRoute();
      if (response.success) {
        this.routes = response.data;
      }
    } catch (error) {
      console.error('Error loading routes:', error);
    }
  }

  async loadBuyers() {
    try {
      const response: any = await this.buyerService.getBuyer();
      if (response.success) {
        this.buyers = response.data;
      }
    } catch (error) {
      console.error('Error loading buyers:', error);
    }
  }

  updateSummary() {
    this.summary = {
      total: this.checklists.length,
      pending: this.checklists.filter(c => c.status === 'pending').length,
      in_progress: this.checklists.filter(c => c.status === 'in_progress').length,
      completed: this.checklists.filter(c => c.status === 'completed').length,
      overdue: this.checklists.filter(c => c.is_overdue).length
    };
  }

  applyFilters() {
    this.filteredChecklists = this.checklists.filter(checklist => {
      let matches = true;

      if (this.statusFilter !== 'all') {
        matches = matches && checklist.status === this.statusFilter;
      }

      if (this.routeFilter) {
        matches = matches && checklist.route === this.routeFilter;
      }

      if (this.buyerFilter) {
        matches = matches && checklist.buyer === this.buyerFilter;
      }

      return matches;
    });
  }

  onFilterChange() {
    this.applyFilters();
  }

  openCreateModal() {
    this.newChecklist = {
      title: '',
      description: '',
      priority: 'medium',
      status: 'pending'
    };
    this.isCreateModalOpen = true;
  }

  closeCreateModal() {
    this.isCreateModalOpen = false;
  }

  async createChecklist() {
    if (!this.newChecklist.title?.trim()) {
      this.toastService.toastServices('Please enter a checklist title', 'warning', 'top');
      return;
    }

    await this.ionLoaderService.startLoader();
    try {
      const response = await this.checklistService.createChecklist(this.newChecklist as Checklist);
      if (response.success) {
        this.toastService.toastServices('Checklist created successfully', 'success', 'top');
        this.closeCreateModal();
        await this.loadChecklists();
        this.updateSummary();
      } else {
        this.toastService.toastServices(response.message, 'danger', 'top');
      }
    } catch (error) {
      console.error('Error creating checklist:', error);
      this.toastService.toastServices('Error creating checklist', 'danger', 'top');
    } finally {
      this.ionLoaderService.dismissLoader();
    }
  }

  openDetailModal(checklist: Checklist) {
    this.selectedChecklist = checklist;
    this.isDetailModalOpen = true;
  }

  closeDetailModal() {
    this.isDetailModalOpen = false;
    this.selectedChecklist = null;
  }

  async startChecklist(checklist: Checklist) {
    const alert = await this.alertController.create({
      header: 'Start Checklist',
      message: `Are you sure you want to start "${checklist.title}"?`,
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Start',
          handler: async () => {
            await this.ionLoaderService.startLoader();
            try {
              const response = await this.checklistService.startChecklist(checklist.id!);
              if (response.success) {
                this.toastService.toastServices('Checklist started successfully', 'success', 'top');
                await this.loadChecklists();
                this.updateSummary();
              } else {
                this.toastService.toastServices(response.message, 'danger', 'top');
              }
            } catch (error) {
              console.error('Error starting checklist:', error);
              this.toastService.toastServices('Error starting checklist', 'danger', 'top');
            } finally {
              this.ionLoaderService.dismissLoader();
            }
          }
        }
      ]
    });
    await alert.present();
  }

  async submitChecklist(checklist: Checklist) {
    // Check if all mandatory items are completed
    const mandatoryItems = checklist.items?.filter(item => item.is_mandatory) || [];
    const completedMandatory = mandatoryItems.filter(item => item.is_completed);

    if (mandatoryItems.length > 0 && completedMandatory.length < mandatoryItems.length) {
      this.toastService.toastServices(
        `Please complete all mandatory items before submitting (${completedMandatory.length}/${mandatoryItems.length} completed)`,
        'warning',
        'top'
      );
      return;
    }

    const alert = await this.alertController.create({
      header: 'Submit Checklist',
      message: `Are you sure you want to submit "${checklist.title}"? This action cannot be undone.`,
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Submit',
          handler: async () => {
            await this.ionLoaderService.startLoader();
            try {
              const response = await this.checklistService.submitChecklist(checklist.id!);
              if (response.success) {
                this.toastService.toastServices('Checklist submitted successfully', 'success', 'top');
                await this.loadChecklists();
                this.updateSummary();
                this.closeDetailModal();
              } else {
                this.toastService.toastServices(response.message, 'danger', 'top');
              }
            } catch (error) {
              console.error('Error submitting checklist:', error);
              this.toastService.toastServices('Error submitting checklist', 'danger', 'top');
            } finally {
              this.ionLoaderService.dismissLoader();
            }
          }
        }
      ]
    });
    await alert.present();
  }

  async deleteChecklist(checklist: Checklist) {
    const alert = await this.alertController.create({
      header: 'Delete Checklist',
      message: `Are you sure you want to delete "${checklist.title}"? This action cannot be undone.`,
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Delete',
          cssClass: 'danger',
          handler: async () => {
            await this.ionLoaderService.startLoader();
            try {
              const response = await this.checklistService.deleteChecklist(checklist.id!);
              if (response.success) {
                this.toastService.toastServices('Checklist deleted successfully', 'success', 'top');
                await this.loadChecklists();
                this.updateSummary();
              } else {
                this.toastService.toastServices(response.message, 'danger', 'top');
              }
            } catch (error) {
              console.error('Error deleting checklist:', error);
              this.toastService.toastServices('Error deleting checklist', 'danger', 'top');
            } finally {
              this.ionLoaderService.dismissLoader();
            }
          }
        }
      ]
    });
    await alert.present();
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'pending': return 'warning';
      case 'in_progress': return 'primary';
      case 'completed': return 'success';
      case 'overdue': return 'danger';
      default: return 'medium';
    }
  }

  getPriorityColor(priority: string): string {
    switch (priority) {
      case 'high': return 'danger';
      case 'medium': return 'warning';
      case 'low': return 'success';
      default: return 'medium';
    }
  }

  formatDate(dateString: string): string {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }
}
