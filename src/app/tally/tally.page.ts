import { ToastService } from './../shared/services/toast.service';
import { IonLoaderService } from './../shared/services/ion-loader.service';
import { TallyService } from './../shared/services/tally.service';
import { Component, OnInit } from '@angular/core';
import * as moment from 'moment';
import { Platform } from '@ionic/angular';
import { AlertService } from '../shared/services/alert.service';
import { PrintServiceService } from '../shared/services/print-service.service';
import { UtilService } from '../shared/services/util.service';

@Component({
  selector: 'app-tally',
  templateUrl: './tally.page.html',
  styleUrls: ['./tally.page.scss'],
})
export class TallyPage implements OnInit {
  from_date: any;
  to_date: any;
  data: any;
  display_data: any[];
  view: any;
  pr_total: any;
  enablePR : boolean = JSON.parse(localStorage.getItem('metadata')).enablePR;
  billing_field_settings : any = JSON.parse(localStorage.getItem('metadata')).billing_field_settings;
  showFilters: boolean = false;
  showSummary: boolean = false;
  isFullScreenModalOpen: boolean = false;

  constructor(
    private api: TallyService,
    private loader: IonLoaderService,
    private toast: ToastService,
    public platform: Platform,
    public alertService: AlertService,
    private printService: PrintServiceService,
    private util: UtilService,
  ) {
    this.from_date = moment().format("YYYY-MM-DD");
    this.to_date = moment().format("YYYY-MM-DD");
  }

  ngOnInit() {
  }
  async filterData() {
    if (!this.from_date || !this.to_date) {
      this.toast.toastServices("Select the dates", "danger", "top");
      return
    }
    await this.loader.startLoader().then(async () => {
      await this.api.getTally(this.from_date, this.to_date).then(async (res: any) => {
        if (res.success) {
          console.log(res);
          this.toast.toastServices(res.message, "success", "top");
          this.data = res;
          this.display_data = this.structureData()
          this.view = 'product'
        } else {
          this.toast.toastServices(res.message, "danger", "top");
        }
        this.loader.dismissLoader();
      })
        .catch(async (err) => {
          this.toast.toastServices(err, "danger", "top");
          this.loader.dismissLoader();
          // console.log(err);
        });
    });
  }
  segmentChanged(ev) {
    this.view = ev.detail.value
  }
  getFieldName(slug){
    return this.billing_field_settings.find(item=>item.slug === slug).displayName;
  }
  structureData() {
    let displayData = [];
    this.data?.product.forEach(element => {
      let foundPurchase = this.data.purchaseInvoiceItem.find(e => e.product == element.id)
      let foundSales = this.data.salesInvoiceItem.find(e => e.product == element.id)
      let purchase_no = 0, purchase_weight = 0, purchase_amount = 0;
      let sales_no = 0, sales_weight = 0, sales_amount = 0, pr_amount=0;
      if (foundPurchase) {
        purchase_no = foundPurchase.no__sum;
        purchase_weight = foundPurchase.weight__sum;
        purchase_amount = foundPurchase.line_total__sum;
      }
      if (foundSales) {
        sales_no = foundSales.no__sum;
        sales_weight = foundSales.weight__sum;
        sales_amount = foundSales.line_total__sum;
        console.log("sds=>",sales_amount,(foundSales.product__pr_rate * foundSales.no__sum * foundSales.product__unit_contains) +(foundSales.product__pr_rate * foundSales.weight__sum ));
        this.enablePR ? pr_amount = (foundSales.line_total__sum) - (+(foundSales.product__pr_rate * foundSales.no__sum * foundSales.product__unit_contains) + +(foundSales.product__pr_rate * foundSales.weight__sum )) : '';
        console.log(pr_amount);
        
      }
      let data = {
        name: element.name,
        purchase_no: purchase_no,
        purchase_weight: purchase_weight,
        purchase_amount: purchase_amount,
        sales_no: sales_no,
        sales_weight: sales_weight,
        sales_amount: sales_amount,
        closing_no: purchase_no - sales_no,
        closing_weight: purchase_weight - sales_weight
      }
      this.enablePR ?   data['pr_amount']=pr_amount : '';
      displayData.push(data)
    });
    this.enablePR ? this.pr_total = displayData.reduce((acc,cur)=> acc + cur.pr_amount,0) : '';
    return displayData
  }

  toggleFilters() {
    this.showFilters = !this.showFilters;
  }

  toggleSummary() {
    this.showSummary = !this.showSummary;
  }

  // Full Screen Modal Methods
  openFullScreenModal() {
    this.isFullScreenModalOpen = true;
  }

  closeFullScreenModal() {
    this.isFullScreenModalOpen = false;
  }

  // Helper methods for modal
  getSegmentIcon(view: string): string {
    switch (view) {
      case 'product':
        return 'cube-outline';
      case 'purchase':
        return 'arrow-down-circle-outline';
      case 'sales':
        return 'arrow-up-circle-outline';
      default:
        return 'list-outline';
    }
  }

  getSegmentTitle(view: string): string {
    switch (view) {
      case 'product':
        return 'Product Analysis';
      case 'purchase':
        return 'Purchase';
      case 'sales':
        return 'Sales';
      default:
        return 'Detailed';
    }
  }

  // Print functionality
  printCurrentReport() {
    const reportTitle = this.getSegmentTitle(this.view);
    const dateRange = `${this.from_date} to ${this.to_date}`;

    switch (this.view) {
      case 'product':
        this.printProductReport(reportTitle, dateRange);
        break;
      case 'purchase':
        this.printPurchaseReport(reportTitle, dateRange);
        break;
      case 'sales':
        this.printSalesReport(reportTitle, dateRange);
        break;
    }
  }

  private printProductReport(title: string, dateRange: string) {
    const items = this.display_data.map((d, index) => {
      return `
        <tr>
          <td class="name-cell">${d.name}</td>
          <td class="number-cell">${d.sales_no}</td>
          <td class="number-cell">${d.sales_weight}</td>
          <td class="amount-cell">₹${d.sales_amount.toFixed(2)}</td>
          ${this.enablePR ? `<td class="amount-cell">₹${d.pr_amount.toFixed(2)}</td>` : ''}
          <td class="number-cell">${d.purchase_no}</td>
          <td class="number-cell">${d.purchase_weight}</td>
          <td class="amount-cell">₹${d.purchase_amount.toFixed(2)}</td>
          <td class="number-cell">${d.closing_no}</td>
          <td class="number-cell">${d.closing_weight}</td>
        </tr>
      `;
    }).join("");

    const tableContent = `
      <table>
        <thead>
          <tr>
            <th>Product Name</th>
            <th>Sales ${this.getFieldName('box')}</th>
            <th>Sales ${this.getFieldName('pcs')}</th>
            <th>Sales Amount</th>
            ${this.enablePR ? '<th>PR Amount</th>' : ''}
            <th>Purchase ${this.getFieldName('box')}</th>
            <th>Purchase ${this.getFieldName('pcs')}</th>
            <th>Purchase Amount</th>
            <th>Closing ${this.getFieldName('box')}</th>
            <th>Closing ${this.getFieldName('pcs')}</th>
          </tr>
        </thead>
        <tbody>
          ${items}
        </tbody>
      </table>
    `;

    const htmlContent = this.printService.generateEnhancedReportHTML(
      title,
      dateRange,
      tableContent,
      `Total Products: ${this.display_data.length}`
    );

    this.printService.printEnhancedReport(htmlContent, `${title} Report - ${dateRange}`);
  }

  private printPurchaseReport(title: string, dateRange: string) {
    const items = this.data.purchaseInvoice.map((p, index) => {
      return `
        <tr>
          <td class="name-cell">${p.suplier__name}</td>
          <td class="amount-cell">₹${p.bill_amount.toFixed(2)}</td>
        </tr>
      `;
    }).join("");

    const total = this.data.purchaseInvoice.reduce((acc, p) => acc + p.bill_amount, 0);

    const tableContent = `
      <table>
        <thead>
          <tr>
            <th>Supplier Name</th>
            <th>Bill Amount</th>
          </tr>
        </thead>
        <tbody>
          ${items}
          <tr class="total-row">
            <td><strong>Total</strong></td>
            <td><strong>₹${total.toFixed(2)}</strong></td>
          </tr>
        </tbody>
      </table>
    `;

    const htmlContent = this.printService.generateEnhancedReportHTML(
      title,
      dateRange,
      tableContent,
      `Total Amount: ₹${total.toFixed(2)} | Records: ${this.data.purchaseInvoice.length}`
    );

    this.printService.printEnhancedReport(htmlContent, `${title} Report - ${dateRange}`);
  }

  private printSalesReport(title: string, dateRange: string) {
    const items = this.data.salesInvoice.map((s, index) => {
      return `
        <tr>
          <td class="name-cell">${s.buyer__name}</td>
          <td class="amount-cell">₹${s.bill_amount.toFixed(2)}</td>
        </tr>
      `;
    }).join("");

    const total = this.data.salesInvoice.reduce((acc, s) => acc + s.bill_amount, 0);

    const tableContent = `
      <table>
        <thead>
          <tr>
            <th>Buyer Name</th>
            <th>Bill Amount</th>
          </tr>
        </thead>
        <tbody>
          ${items}
          <tr class="total-row">
            <td><strong>Total</strong></td>
            <td><strong>₹${total.toFixed(2)}</strong></td>
          </tr>
        </tbody>
      </table>
    `;

    const htmlContent = this.printService.generateEnhancedReportHTML(
      title,
      dateRange,
      tableContent,
      `Total Amount: ₹${total.toFixed(2)} | Records: ${this.data.salesInvoice.length}`
    );

    this.printService.printEnhancedReport(htmlContent, `${title} Report - ${dateRange}`);
  }


}
