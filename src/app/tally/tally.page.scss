/* <PERSON>y Page Styling */
.tally-content {
  --background: var(--ion-background-color);
  --padding-start: 0;
  --padding-end: 0;
  --padding-top: 16px;
  --padding-bottom: 16px;
}

/* Section Styling */
.filter-section,
.segment-section,
.table-section {
  margin-bottom: 24px;
}

/* Collapsible Summary Section */
.summary-section {
  background: var(--card-background, white);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin: 0 0 24px 0;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: linear-gradient(135deg, var(--card-background, #ffffff) 0%, var(--content-background, #f8f9fa) 100%);
  border-bottom: 1px solid var(--ion-color-step-200, #e0e0e0);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.summary-header:hover {
  background: var(--ion-color-step-50, #f8f9fa);
}

.summary-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  padding: 0 16px;
}

.summary-content.expanded {
  max-height: 600px;
  padding: 16px;
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.section-icon {
  font-size: 24px;
  margin-right: 8px;
  color: var(--ion-color-primary);
}

/* Summary Cards */
.summary-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.summary-card-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-info h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
}

.summary-info h1 {
  margin: 0 0 4px 0;
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
}

.summary-info p {
  margin: 0;
  font-size: 12px;
  color: #6c757d;
}

.summary-icon {
  font-size: 48px;
  opacity: 0.8;
}

.summary-icon.purchase {
  color: #dc3545;
}

.summary-icon.sales {
  color: #28a745;
}

.summary-icon.expense {
  color: #fd7e14;
}

.summary-icon.difference {
  color: #6f42c1;
}

.summary-icon.pr {
  color: #007bff;
}

.purchase-card {
  background: linear-gradient(135deg, #ffebee 0%, #fef5f5 100%);
}

.sales-card {
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
}

.expense-card {
  background: linear-gradient(135deg, #fff3e0 0%, #fef8f0 100%);
}

.difference-card {
  background: linear-gradient(135deg, #f3e5f5 0%, #faf2fb 100%);
}

.pr-card {
  background: linear-gradient(135deg, #e3f2fd 0%, #f1f8ff 100%);
}

/* Collapsible Filter Section */
.filter-section {
  background: var(--card-background, white);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: linear-gradient(135deg, var(--card-background, #ffffff) 0%, var(--content-background, #f8f9fa) 100%);
  border-bottom: 1px solid var(--ion-color-step-200, #e0e0e0);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.filter-header:hover {
  background: var(--ion-color-step-50, #f8f9fa);
}

.toggle-button {
  --color: var(--ion-color-primary);
  --background: transparent;
  --padding-start: 8px;
  --padding-end: 8px;
  margin: 0;
}

.toggle-icon {
  font-size: 20px;
  transition: transform 0.3s ease;
}

.filter-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  padding: 0 16px;
}

.filter-content.expanded {
  max-height: 200px;
  padding: 16px;
}

/* Filter Card */
.filter-card {
  border-radius: 12px;
  box-shadow: none;
  --background: #f8f9fa;
  margin: 0;
}

.date-item {
  --background: transparent;
  --padding-start: 0;
  --padding-end: 0;
  margin-bottom: 8px;
}

.date-input {
  --background: var(--item-background);
  --border-radius: 8px;
  --padding-start: 12px;
  --padding-end: 12px;
}

.filter-button {
  --border-radius: 12px;
  height: 44px;
  margin-top: 20px;
}

/* Segment Section */
.segment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.segment-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  --color: var(--ion-color-primary);
  --background: rgba(var(--ion-color-primary-rgb), 0.1);
  --border-radius: 8px;
  --padding-start: 8px;
  --padding-end: 8px;
  height: 36px;
  width: 36px;
  transition: all 0.3s ease;
}

.action-button:hover {
  --background: rgba(var(--ion-color-primary-rgb), 0.2);
  transform: scale(1.05);
}

.print-button {
  --color: #28a745;
  --background: rgba(40, 167, 69, 0.1);
}

.print-button:hover {
  --background: rgba(40, 167, 69, 0.2);
}

.fullscreen-button {
  --color: #6f42c1;
  --background: rgba(111, 66, 193, 0.1);
}

.fullscreen-button:hover {
  --background: rgba(111, 66, 193, 0.2);
}

.custom-segment {
  --background: var(--section-background);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.segment-button {
  --indicator-color: var(--ion-color-primary);
  --color: #6c757d;
  --color-checked: var(--ion-color-primary);
}

.segment-button ion-icon {
  margin-bottom: 4px;
}

/* Data Cards and Tables */
.data-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --background: var(--card-background);
  margin-bottom: 16px;
}

.table-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.table-icon {
  font-size: 20px;
  margin-right: 8px;
  color: var(--ion-color-primary);
}

.table-container {
  overflow-x: auto;
  border-radius: 8px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.table-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 2px solid #dee2e6;
}

.table-header th {
  padding: 12px 8px;
  text-align: left;
  font-weight: 600;
  color: #495057;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
}

.table-row {
  border-bottom: 1px solid #dee2e6;
  transition: background-color 0.3s ease;
}

.table-row:hover {
  background-color: #f8f9fa;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row td {
  padding: 12px 8px;
  vertical-align: middle;
}

.product-name,
.supplier-name,
.buyer-name {
  font-weight: 600;
  color: #2c3e50;
  min-width: 150px;
}

.number-cell {
  text-align: center;
  font-weight: 500;
  color: #495057;
  min-width: 80px;
}

.amount-cell {
  text-align: right;
  font-weight: 600;
  color: var(--ion-color-primary);
  min-width: 100px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .tally-content {
    --padding-start: 12px;
    --padding-end: 12px;
  }

  .section-title {
    font-size: 18px;
  }

  .section-icon {
    font-size: 20px;
  }

  .summary-info h1 {
    font-size: 24px;
  }

  .summary-info h3 {
    font-size: 12px;
  }

  .summary-info p {
    font-size: 11px;
  }

  .summary-icon {
    font-size: 36px;
  }

  .filter-header {
    padding: 12px;
  }

  .filter-content.expanded {
    padding: 12px;
  }

  .data-table {
    font-size: 12px;
  }

  .table-header th {
    padding: 8px 6px;
    font-size: 10px;
  }

  .table-row td {
    padding: 8px 6px;
  }

  .product-name,
  .supplier-name,
  .buyer-name {
    min-width: 120px;
  }

  .number-cell {
    min-width: 60px;
  }

  .amount-cell {
    min-width: 80px;
  }
}

/* Full Screen Modal Styling */
.fullscreen-modal {
  --width: 100%;
  --height: 100%;
  --max-width: 100%;
  --max-height: 100%;
  --border-radius: 0;
}

.fullscreen-content {
  --background: #f8f9fa;
  --padding-start: 20px;
  --padding-end: 20px;
  --padding-top: 20px;
  --padding-bottom: 20px;
}

.modal-title-icon {
  font-size: 20px;
  margin-right: 8px;
  vertical-align: middle;
}

.modal-action-button {
  --color: var(--ion-text-color, white);
  --background: rgba(255, 255, 255, 0.1);
  --border-radius: 8px;
  margin-left: 8px;
  transition: all 0.3s ease;
}

.modal-action-button:hover {
  --background: rgba(255, 255, 255, 0.2);
}

/* Enhanced Segment in Full Screen */
.fullscreen-segment-section {
  margin-bottom: 24px;
  background: var(--section-background);
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.fullscreen-segment {
  --background: #f8f9fa;
  border-radius: 12px;
  padding: 4px;
}

.fullscreen-segment-button {
  --indicator-color: var(--ion-color-primary);
  --color: #6c757d;
  --color-checked: var(--ion-color-primary);
  --background-checked: var(--item-background);
  --border-radius: 8px;
  margin: 0 2px;
  min-height: 48px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.fullscreen-segment-button ion-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.fullscreen-segment-button ion-label {
  font-size: 14px;
  font-weight: 500;
}

/* Enhanced Table in Full Screen */
.fullscreen-table-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.fullscreen-table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.fullscreen-data-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --background: var(--card-background);
  margin: 0;
}

.fullscreen-data-card ion-card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0;
}

.fullscreen-table-title {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.record-count {
  margin-left: 8px;
  font-size: 14px;
  font-weight: 400;
  color: #6c757d;
  background: #e9ecef;
  padding: 2px 8px;
  border-radius: 12px;
}

.fullscreen-table-wrapper {
  flex: 1;
  overflow: auto;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.fullscreen-data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  min-width: 800px;
}

.fullscreen-data-table .table-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: sticky;
  top: 0;
  z-index: 10;
}

.fullscreen-data-table .table-header th {
  padding: 16px 12px;
  text-align: left;
  font-weight: 600;
  color: #495057;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
  border-bottom: 2px solid #dee2e6;
}

.fullscreen-data-table .table-row {
  border-bottom: 1px solid #dee2e6;
  transition: background-color 0.3s ease;
}

.fullscreen-data-table .table-row:hover {
  background-color: #f8f9fa;
}

.fullscreen-data-table .table-row td {
  padding: 16px 12px;
  vertical-align: middle;
  border-right: 1px solid #f0f0f0;
}

.fullscreen-data-table .table-row td:last-child {
  border-right: none;
}

.fullscreen-data-table .product-name,
.fullscreen-data-table .supplier-name,
.fullscreen-data-table .buyer-name {
  font-weight: 600;
  color: #2c3e50;
  min-width: 200px;
}

.fullscreen-data-table .number-cell {
  text-align: center;
  font-weight: 500;
  color: #495057;
  min-width: 100px;
}

.fullscreen-data-table .amount-cell {
  text-align: right;
  font-weight: 600;
  color: var(--ion-color-primary);
  min-width: 120px;
}

/* Responsive Design for Full Screen Modal */
@media (max-width: 768px) {
  .fullscreen-content {
    --padding-start: 12px;
    --padding-end: 12px;
    --padding-top: 12px;
  }

  .fullscreen-segment-section {
    padding: 12px;
    margin-bottom: 16px;
  }

  .fullscreen-segment-button {
    min-height: 44px;
  }

  .fullscreen-segment-button ion-icon {
    font-size: 18px;
  }

  .fullscreen-segment-button ion-label {
    font-size: 12px;
  }

  .fullscreen-table-title {
    font-size: 18px;
  }

  .record-count {
    font-size: 12px;
  }

  .fullscreen-data-table {
    font-size: 12px;
    min-width: 600px;
  }

  .fullscreen-data-table .table-header th {
    padding: 12px 8px;
    font-size: 11px;
  }

  .fullscreen-data-table .table-row td {
    padding: 12px 8px;
  }

  .fullscreen-data-table .product-name,
  .fullscreen-data-table .supplier-name,
  .fullscreen-data-table .buyer-name {
    min-width: 150px;
  }

  .fullscreen-data-table .number-cell {
    min-width: 80px;
  }

  .fullscreen-data-table .amount-cell {
    min-width: 100px;
  }
}

/* Animation for Full Screen Modal */
.fullscreen-modal {
  animation: slideInFromBottom 0.4s ease-out;
}

@keyframes slideInFromBottom {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.fullscreen-segment-section,
.fullscreen-table-container {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.container {
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, var(--app-background, #ffffff) 0%, var(--content-background, #f8f9fa) 100%);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;

  .stat-card {
    background: linear-gradient(135deg, var(--modal-background, #fff3e0) 0%, var(--form-background, #fef8f0) 100%);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--ion-color-step-200, #e3e3e3);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
  }
}

.main-content {
  background: linear-gradient(135deg, var(--card-background, #ffffff) 0%, var(--section-background, #f8f9fa) 100%);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--ion-color-step-200, #e3e3e3);
}

.tally-title {
  color: var(--ion-text-color);
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 4px;
}

.tally-subtitle {
  color: var(--ion-text-color-step-600);
  font-size: 14px;
  margin-bottom: 4px;
}

.status-success {
  color: var(--ion-color-success);
}

.status-primary {
  color: var(--ion-color-primary);
}

.status-secondary {
  color: var(--ion-text-color-step-600);
}