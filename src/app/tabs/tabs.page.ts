import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { TranslationService } from '../shared/services/translation.service';

@Component({
  selector: 'app-tabs',
  templateUrl: './tabs.page.html',
  styleUrls: ['./tabs.page.scss'],
})
export class TabsPage implements OnInit {

  constructor(
    private router: Router,
    public translate: TranslationService
  ) { }

  ngOnInit() {
    console.log('TabsPage ngOnInit - Current URL:', this.router.url);
    // Set default tab if no specific tab is selected
    if (this.router.url === '/tabs') {
      console.log('TabsPage ngOnInit - Redirecting to /tabs/home');
      this.router.navigate(['/tabs/home'], { replaceUrl: true });
    } else {
      console.log('TabsPage ngOnInit - No redirect needed, current URL:', this.router.url);
    }
  }

  ionViewWillEnter() {
    console.log('TabsPage ionViewWillEnter - Tabs page entering');
  }

  ionViewDidEnter() {
    console.log('TabsPage ionViewDidEnter - Tabs page entered');
  }

}
