<app-header [title]="'Purchase Payment Report'" [returnUrl]="'tabs/home'"></app-header>

<ion-content class="purchase-payment-content">
  <!-- Filter Section -->
  <div class="filter-section">
    <h3 class="section-title">
      <ion-icon name="filter-outline" class="section-icon"></ion-icon>
      Filter Options
    </h3>

    <ion-card class="filter-card">
      <ion-card-content>
        <ion-row>
          <ion-col size="6">
            <ion-item lines="none" class="date-item">
              <ion-label position="stacked">From Date</ion-label>
              <ion-input
                [(ngModel)]="from_date"
                [value]="from_date | date : 'YYYY-MM-dd'"
                placeholder="Select start date"
                type="date"
                class="date-input">
              </ion-input>
            </ion-item>
          </ion-col>

          <ion-col size="6">
            <ion-item lines="none" class="date-item">
              <ion-label position="stacked">To Date</ion-label>
              <ion-input
                [(ngModel)]="to_date"
                [value]="to_date | date : 'YYYY-MM-dd'"
                placeholder="Select end date"
                type="date"
                class="date-input">
              </ion-input>
            </ion-item>
          </ion-col>
        </ion-row>

        <ion-row>
          <ion-col size="9">
            <ion-item lines="none" class="supplier-item">
              <ion-label position="stacked">Select Suppliers</ion-label>
              <ionic-selectable
                name="agent"
                [shouldFocusSearchbar]="true"
                [(ngModel)]="selectedSupplier"
                placeholder="Select suppliers"
                [isMultiple]="true"
                [items]="supplier_data"
                itemValueField="id"
                itemTextField="name"
                [canSearch]="true"
                class="supplier-selector">
              </ionic-selectable>
            </ion-item>
          </ion-col>

          <ion-col size="3">
            <ion-button
              (click)="filterList()"
              fill="solid"
              expand="block"
              class="filter-button">
              <ion-icon name="search" slot="start"></ion-icon>
              Filter
            </ion-button>
          </ion-col>
        </ion-row>
      </ion-card-content>
    </ion-card>
  </div>
  <!-- Payment Data Section -->
  <div class="payment-data-section" *ngIf="purchasePaymentData && purchasePaymentData.length > 0">
    <h3 class="section-title">
      <ion-icon name="receipt-outline" class="section-icon"></ion-icon>
      Payment Records
    </h3>

    <!-- Summary Cards -->
    <ion-row class="summary-row">
      <ion-col size="4">
        <ion-card class="summary-card total-bills">
          <ion-card-content>
            <div class="summary-content">
              <div class="summary-info">
                <h3>Total Bills</h3>
                <h1>{{purchasePaymentData.length}}</h1>
              </div>
              <ion-icon name="document-text" class="summary-icon"></ion-icon>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="4">
        <ion-card class="summary-card bill-amount">
          <ion-card-content>
            <div class="summary-content">
              <div class="summary-info">
                <h3>Bill Amount</h3>
                <h1>{{bill_amount_total | currency: 'INR':'symbol':'1.0-0'}}</h1>
              </div>
              <ion-icon name="card" class="summary-icon"></ion-icon>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="4">
        <ion-card class="summary-card paid-amount">
          <ion-card-content>
            <div class="summary-content">
              <div class="summary-info">
                <h3>Paid</h3>
                <h1>{{received_amount_total | currency: 'INR':'symbol':'1.0-0'}}</h1>
              </div>
              <ion-icon name="checkmark-done" class="summary-icon"></ion-icon>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>

    <!-- Payment Records Table -->
    <ion-card class="data-table-card">
      <ion-card-header>
        <div class="table-header-content">
          <ion-card-title class="table-title">
            <ion-icon name="list-outline" class="table-icon"></ion-icon>
            Detailed Payment Records
          </ion-card-title>
          <ion-button
            (click)="printStatement()"
            fill="outline"
            size="small"
            class="print-button">
            <ion-icon name="print-outline" slot="start"></ion-icon>
            Print
          </ion-button>
        </div>
      </ion-card-header>

      <ion-card-content>
        <div class="table-container">
          <table class="payment-table">
            <thead>
              <tr class="table-header">
                <th>Bill ID</th>
                <th>Date</th>
                <th>Supplier</th>
                <th>Old Balance</th>
                <th>Bill Amount</th>
                <th>Paid Amount</th>
                <th>Current Balance</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let d of purchasePaymentData" class="table-row">
                <td class="bill-id">{{d.id}}</td>
                <td class="bill-date">{{d.date | date:'dd/MM/yyyy'}}</td>
                <td class="supplier-name">{{d.name}}</td>
                <td class="old-balance">{{d.previous_balance | currency: 'INR':'symbol':'1.2-2'}}</td>
                <td class="bill-amount">{{d.bill_amount | currency: 'INR':'symbol':'1.2-2'}}</td>
                <td class="paid-amount">{{d.received_amount | currency: 'INR':'symbol':'1.2-2'}}</td>
                <td class="current-balance">{{d.current_balance | currency: 'INR':'symbol':'1.2-2'}}</td>
              </tr>
            </tbody>
            <tfoot>
              <tr class="table-footer">
                <td colspan="4" class="total-label">Total</td>
                <td class="total-bill">{{bill_amount_total | currency: 'INR':'symbol':'1.2-2'}}</td>
                <td class="total-paid">{{received_amount_total | currency: 'INR':'symbol':'1.2-2'}}</td>
                <td class="total-balance">{{current_balance_total | currency: 'INR':'symbol':'1.2-2'}}</td>
              </tr>
            </tfoot>
          </table>
        </div>
      </ion-card-content>
    </ion-card>
  </div>

  <!-- No Data Message -->
  <div class="no-data-section" *ngIf="!purchasePaymentData || purchasePaymentData.length === 0">
    <ion-card class="no-data-card">
      <ion-card-content>
        <div class="no-data-content">
          <ion-icon name="document-text-outline" class="no-data-icon"></ion-icon>
          <h3>No Payment Records Found</h3>
          <p>Try adjusting your filter criteria to find payment records.</p>
        </div>
      </ion-card-content>
    </ion-card>
  </div>
</ion-content>
<ion-footer>
  <app-floating-menu></app-floating-menu>
</ion-footer>