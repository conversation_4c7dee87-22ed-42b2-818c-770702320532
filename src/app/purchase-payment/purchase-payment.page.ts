import { SuppliersService } from './../shared/services/supplier.service';
import { Component, OnInit } from '@angular/core';
import { Platform } from '@ionic/angular';
import { AlertService } from '../shared/services/alert.service';
import { IonLoaderService } from '../shared/services/ion-loader.service';
import { PurchasePaymentService } from '../shared/services/purchase-payment.service';
import { ToastService } from '../shared/services/toast.service';
import { PrintServiceService } from '../shared/services/print-service.service';
import { UtilService } from '../shared/services/util.service';

@Component({
  selector: 'app-purchase-payment',
  templateUrl: './purchase-payment.page.html',
  styleUrls: ['./purchase-payment.page.scss'],
})
export class PurchasePaymentPage implements OnInit {
  supplier_data: any;
  selectedSupplier = null;
  from_date: any;
  to_date: any;
  purchasePaymentData: any;
  previous_balance_total: number;
  bill_amount_total: number;

  received_amount_total: number;
  current_balance_total: number;
  lineAccountData: any;
  constructor(
    private api: PurchasePaymentService,
    private toast: ToastService,
    public ionLoaderService: IonLoaderService,
    public platform: Platform,
    public alertService: AlertService,
    private supplier_api: SuppliersService,
    private printService: PrintServiceService,
    private util:UtilService
    ) { }

  ngOnInit() {
  }
  ionViewWillEnter() {
    this.getBuyer()
  }
  async getBuyer() {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.supplier_api.getSupplier().then(async (res: any) => {
        if (res.success) {
          this.toast.toastServices(res.message, 'success', 'top');
          this.supplier_data = res.data;
        }
        else {
          this.toast.toastServices(res.message, 'danger', 'top')
        }
        this.ionLoaderService.dismissLoader();

      }).catch((err) => {
        this.toast.toastServices(err, 'danger', 'top')
        this.ionLoaderService.dismissLoader();
      });
    });
  }
  async filterList() {
    await this.ionLoaderService.startLoader().then(async () => {
    let requiredSupplierList = this.selectedSupplier?.map((e) => e.id) || [];
      await this.api.getPurchasePayment({ supplier: requiredSupplierList, from_date: this.from_date, to_date: this.to_date }).then(async (res: any) => {
        if (res.success) {
          this.purchasePaymentData = res.data
          this.getTotal()
          this.toast.toastServices(res.message, 'success', 'top')
        }
        else {
          this.toast.toastServices(res.message, 'danger', 'top')
        }
        this.ionLoaderService.dismissLoader();
      }).catch((err) => {
        this.toast.toastServices(err, 'danger', 'top')
        this.ionLoaderService.dismissLoader();
      })
    })

  }
  async getTotal() {

    this.bill_amount_total = 0;

    this.received_amount_total = 0;
    this.current_balance_total = 0;
    this.purchasePaymentData.forEach((element) => {

      this.bill_amount_total += element.bill_amount;

      this.received_amount_total += element.received_amount;

    });
    this.current_balance_total = this.purchasePaymentData[this.purchasePaymentData.length - 1].current_balance

  }

  printStatement() {
    const items = this.purchasePaymentData.map((d, index) => {
      return `
        <tr>
          <td class="number-cell">${d.id}</td>
          <td class="name-cell">${d.date}</td>
          <td class="name-cell">${d.name}</td>
          <td class="amount-cell">₹${d.previous_balance.toFixed(2)}</td>
          <td class="amount-cell">₹${d.bill_amount.toFixed(2)}</td>
          <td class="amount-cell">₹${d.received_amount.toFixed(2)}</td>
          <td class="amount-cell">₹${d.current_balance.toFixed(2)}</td>
        </tr>
      `;
    }).join("");

    const dateRange = `${this.from_date} to ${this.to_date}`;

    const tableContent = `
      <table>
        <thead>
          <tr>
            <th>Bill ID</th>
            <th>Bill Date</th>
            <th>Supplier</th>
            <th>Previous Balance</th>
            <th>Bill Amount</th>
            <th>Paid Amount</th>
            <th>Current Balance</th>
          </tr>
        </thead>
        <tbody>
          ${items}
          <tr class="total-row">
            <td><strong>Total</strong></td>
            <td></td>
            <td></td>
            <td></td>
            <td><strong>₹${this.bill_amount_total.toFixed(2)}</strong></td>
            <td><strong>₹${this.received_amount_total.toFixed(2)}</strong></td>
            <td><strong>₹${this.current_balance_total.toFixed(2)}</strong></td>
          </tr>
        </tbody>
      </table>
    `;

    const htmlContent = this.printService.generateEnhancedReportHTML(
      'Purchase Payment Statement',
      dateRange,
      tableContent,
      `Total Bill Amount: ₹${this.bill_amount_total.toFixed(2)} | Total Paid: ₹${this.received_amount_total.toFixed(2)} | Outstanding: ₹${this.current_balance_total.toFixed(2)}`
    );

    this.printService.printEnhancedReport(htmlContent, `Purchase Statement - ${dateRange}`);
  }
}
