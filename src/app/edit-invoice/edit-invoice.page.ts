import {
  <PERSON>mpo<PERSON>,
  <PERSON>ement<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>t,
  <PERSON><PERSON><PERSON>ist,
  ViewChild,
  ViewChildren,
  OnDestroy
} from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { AlertController, Platform } from "@ionic/angular";
import { AlertService } from "../shared/services/alert.service";
import { IonLoaderService } from "../shared/services/ion-loader.service";
import { RouteService } from "../shared/services/route.service";
import { SalesInvoiceService } from "../shared/services/sales-invoice.service";
import { ToastService } from "../shared/services/toast.service";
import * as moment from "moment";
import { PurchaseInvoiceService } from "../shared/services/purchase-invoice.service";
import { ReportsService } from "../shared/services/report.service";
import { RoundingService, RoundingConfig } from "../shared/services/rounding.service";

@Component({
  selector: "app-edit-invoice",
  templateUrl: "./edit-invoice.page.html",
  styleUrls: ["./edit-invoice.page.scss"],
})
export class EditInvoicePage implements OnInit {
  @ViewChildren("inputCrates") inputCrates: QueryList<ElementRef>;
  @ViewChildren("weightInput") weightInput;
  @ViewChild("popover") popover;
  editId: any;
  date: any;
  // date = moment().format("YYYY-MM-DD");
  data: any;
  isWeightOpen = false;
  product_data: any;
  selectedIndex: any;
  totalAmount: Number = 0;
  totalNo: Number = 0;
  totalWeight: Number = 0;
  receivable_amount: Number = 0;
  received_amount: Number = 0;
  current_balance: Number = 0;
  closing_balance: Number = 0;
  filterEmpty: boolean = false;
  displayProducts: any;
  changeRateRequestFlag: boolean;
  message: string;

  // Product creation modal properties
  isProductFormOpen: boolean = false;
  lastClickTime: number = 0;
  lock: boolean = true;
  isProductDetailsOpen = false;
  invoice_remarks: string;

  billingAdditionalFieldsTotal: Number = 0;
  state: any = '';

  modeOfPayment: string = '';
  modeOfPayments: any = JSON.parse(localStorage.getItem('metadata')).modeOfPayment;
  dropdownConfig = {
    displayKey: 'name', // if objects are passed
    search: true,
    height: '300px',
    placeholder: 'Select a Payment mode',
    customComparator: () => { }, // custom function to sort options
    limitTo: 5, // options to be displayed in the dropdown
    moreText: 'more', // text to be displayed when more than one option is selected
    noResultsFound: 'No results found!',
    searchPlaceholder: 'Search...',
  };

  // Staff information fields
  billedBy: string = '';
  deliveryBy: string = '';
  collectedBy: string = '';
  selectedOption = '';
  options = this.modeOfPayments;
  billingHeaderData: any = JSON.parse(localStorage.getItem('metadata')).billingHeaderData || [];
  billing_settings: any = JSON.parse(localStorage.getItem('metadata')).billing_settings
  billing_field_settings: any = JSON.parse(localStorage.getItem('metadata')).billing_field_settings.filter(item => item.active).sort((a, b) => a.sort_order - b.sort_order);
  billingAdditionalFields: any = JSON.parse(localStorage.getItem('metadata')).billingAdditionalFields.filter(item => item.active).sort((a, b) => a.sort_order - b.sort_order);
  brand: any;
  selectedBrand: string;
  salesPerson: any;
  selectedSalesPerson: any;
  isPaymentModalOpen = false;
  showMoreActions = false;
  showPaymentDetails = false;

  // Rounding properties
  roundingConfig: RoundingConfig = {
    enabled: true
  };

  // Invoice calculation breakdown
  invoiceSubtotal: number = 0;
  invoiceTaxAmount: number = 0;
  invoiceGrossTotal: number = 0;
  roundingAdjustment: number = 0;
  invoiceNetAmount: number = 0;

  // Purchase flag
  purchase: boolean = false;

  constructor(
    private api: SalesInvoiceService,
    private report_api:ReportsService,
    private purchase_api: PurchaseInvoiceService,
    private toast: ToastService,
    public ionLoaderService: IonLoaderService,
    public platform: Platform,
    public alertService: AlertService,
    public routerService: RouteService,
    private route: ActivatedRoute,
    public alertController: AlertController,
    private roundingService: RoundingService
  ) {
    this.route.queryParams.subscribe((params) => {
      if (params && params.id) {
        this.editId = JSON.parse(params.id);
      }
    });
    this.state = localStorage.getItem('state');
  }

  ngOnInit() {
    // Initialize component
  }

  ionViewWillEnter() {
    console.log(this.state);

    switch (this.state) {
      case 'purchase':
        this.purchase = true;
        this.getPurchaseData();
        break;
      default:
        this.purchase = false;
        this.getData();
        break;
    }
  }
  onOptionChange(selected: any) {
    console.log('Selected Option:', selected);
    this.modeOfPayment = selected.value.slug;
  }
  async getPurchaseData() {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.purchase_api.getPurchaseInvoiceById(this.editId).then(async (res: any) => {
        if (res.success) {
          console.log(res)
          this.toast.toastServices(res.message, "success", "top");
          this.data = res.data;
          this.date = res.data.date;
          this.closing_balance = res.data.current_balance;
          this.brand = res.brand
          this.product_data = res.product_data;
          this.displayProducts = this.product_data;
          this.current_balance = res.data.previous_balance;
          this.totalAmount = res.data.bill_amount;
          this.receivable_amount = res.data.receivable_amount;
          this.received_amount = res.data.received_amount;
          this.invoice_remarks = res.data.remarks;
          if (res.data.metadata) {
            this.billingAdditionalFields.forEach(element => {
              let foundData = res.data.metadata.find(item => item.slug == element.slug);
              if (foundData) {
                element.value = foundData.value;
                if (element.fieldType == "discount") {
                  element.percentage = foundData.percentage;
                }
              }
            });
          }
          this.billingHeaderData = res.data.headerdata;
          res.data.purchase_invoice_items.forEach((element) => {
            let foundData = this.product_data.find(
              (e) => element.product == e.id
            );
            if (foundData) {
              foundData.itemId = element.id;
              foundData.mrp = element.mrp;
              foundData.remarks = element.remarks;
              foundData.rate = element.rate;
              foundData.weights = element.weights;
              foundData.weight = element.weight;
              foundData.no = element.no;
              foundData.total = element.line_total;
            }
            console.log(foundData);
            
          });
          this.product_data = this.product_data.map(product => ({
            ...product,
            rate: product.pr_rate, // Assigning pr_rate to rate
            margin: product.pr_margin // Assigning pr_rate to rate
          }));
          this.getTotal();
        } else {
          this.toast.toastServices(res.message, "danger", "top");
        }
        this.ionLoaderService.dismissLoader();
      })
        .catch(async (err) => {
          this.toast.toastServices(err.error.message, "danger", "top");
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }

  filterByBrand(ev) {
    console.log(ev);
    this.selectedBrand = ev.detail.value
    if (this.selectedBrand == 'all') {
      this.displayProducts = this.product_data;
      return;
    };
    this.displayProducts = this.product_data.filter((e) => e.brand_name == this.selectedBrand)
  }
  async getData() {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .getSalesInvoiceById(this.editId)
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, "success", "top");
            this.data = res.data;
            this.date = res.data.date;
            this.closing_balance = res.data.current_balance;
            this.brand = res.brand;
            this.product_data = res.product_data;
            this.displayProducts = this.product_data;
            this.selectedSalesPerson = res.data.sales_person;
            this.current_balance = res.data.previous_balance;
            this.totalAmount = res.data.bill_amount;
            this.receivable_amount = res.data.receivable_amount;
            this.received_amount = res.data.received_amount;
            this.invoice_remarks = res.data.remarks;

            // Load staff information
            this.billedBy = res.data.billed_by || '';
            this.deliveryBy = res.data.delivery_by || '';
            this.collectedBy = res.data.collected_by || '';

            // Load rounding configuration (default to true for new behavior)
            this.roundingConfig.enabled = res.data.rounding_enabled !== undefined ? res.data.rounding_enabled : true;
            this.roundingAdjustment = res.data.rounding_adjustment || 0;
            this.invoiceGrossTotal = res.data.gross_total || 0;

            if (res.data.metadata) {
              this.billingAdditionalFields.forEach(element => {
                let foundData = res.data.metadata.find(item => item.slug == element.slug);
                if (foundData) {
                  element.value = foundData.value;
                  if (element.fieldType == "discount") {
                    element.percentage = foundData.percentage;
                  }
                }
              });
            }
            this.billingHeaderData = res.data.headerdata;
            res.data.sales_invoice_items.forEach((element) => {
              let foundData = this.product_data.find(
                (e) => element.product == e.id
              );
              if (foundData) {
                foundData.itemId = element.id;
                foundData.mrp = element.mrp;
                foundData.remarks = element.remarks;
                foundData.rate = element.rate;
                foundData.weights = element.weights;
                foundData.weight = element.weight;
                foundData.no = element.no;
                foundData.total = element.line_total;
              }
            });
            this.getTotal();
            this.updateMargin()
          } else {
            this.toast.toastServices(res.message, "danger", "top");
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err.error.message, "danger", "top");
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }
  updateMargin() {
    this.product_data.forEach(element => {
      element.rate = element.mrp - (element.mrp * element.final_margin) / 100;
      element.margin = element.final_margin;
    });
  }
  numberOnlyValidation(event: any) {
    const pattern = /[0-9]/;
    let inputChar = String.fromCharCode(event.charCode);

    if (!pattern.test(inputChar)) {
      // invalid character, prevent input
      event.preventDefault();
    }
  }

  presentWeightPopover(e: Event, i) {
    this.popover.event = e;
    this.isWeightOpen = true;
    this.selectedIndex = this.product_data.findIndex((e) => e.id == i);
    // this.weightInput["QueryList"].setFocus();
    console.log(this.weightInput);

    // this.weightInput.setFocus();
  }
  changeFocusOnCrate(i) {
    let idxNext = i + 1; // Get index by class name
    if (idxNext) {
      let idx = idxNext == this.product_data.length ? 0 : idxNext;
      this.inputCrates.toArray()[idx]["el"].setFocus();
    }
  }
  weightButton(data) {
    console.log(this.selectedIndex);
    let weights = data.weights.split("+");
    let pickedData = this.product_data[this.selectedIndex];
    pickedData.no = this.product_data[this.selectedIndex].add ? weights.length : 0;
    pickedData.weights = data.weights;
    console.log(weights);
    let temp = 0;
    weights.forEach((element) => {
      console.log(element);
      temp += Number(element);
    });
    console.log(temp);

    pickedData.weight = temp;
    pickedData.total = pickedData.rate
      ? pickedData?.weight * pickedData?.rate
      : 0;
    this.getTotal();
    this.isWeightOpen = false;
    this.selectedIndex = null;
  }
  modifyRate(data, pd) {
    pd.rate = data;
    pd.total = pd.weight ? pd?.weight * pd?.rate : 0;
    this.getTotal();
  }
  modifyWeight(data, pd) {
    pd.weight = data
    pd.total = (pd.no ? ((pd?.no * pd?.unit_contains) * pd?.rate) : 0) + (pd.weight ? pd?.weight * pd?.rate : 0);
    this.getTotal();
  }
  modifyNo(data, pd) {
    pd.no = data;
    pd.total = (pd.no ? ((pd?.no * pd?.unit_contains) * pd?.rate) : 0) + (pd?.weight ? pd?.weight * pd?.rate : 0);
    this.getTotal();
  }
  modifyDesc(data, pd) {
    pd.desc = data;
  }

  modifyReceivedAmount(amount: number) {
    this.received_amount = amount || 0;
    this.modifyTotal();
  }

  modifyTotal() {
    // Use net amount (after rounding) for balance calculations
    const effectiveTotal = this.invoiceNetAmount;

    switch (this.state) {
      case 'purchase':
        this.closing_balance = +this.current_balance + effectiveTotal - (+this.received_amount + +this.billingAdditionalFieldsTotal);
        break;
      default:
        this.closing_balance = +this.current_balance + +this.billingAdditionalFieldsTotal + effectiveTotal - +this.received_amount;
        break;
    }
  }
  assignSO(){
    let salesperson : any;
    this.report_api.getSalesPerson().subscribe(async (res: any) => {
      salesperson = res.data
      console.log(res);
      if (res.data.length < 1) {
        this.toast.toastServices("No sales person to be selected","danger","bottom")
      }
      this.salesPerson = res.data
      const options = salesperson.map(item => ({
        name: item.first_name,
        type: 'radio',
        label: item.first_name,
        value: item.id,
        checked: false
      }));
      const alert = await this.alertController.create({
        header: 'Select Sales Person',
        inputs: options,
        buttons: [
          {
            text: 'Cancel',
            role: 'cancel',
            cssClass: 'secondary',
            handler: () => {
              console.log('Confirm Cancel');
            },
          },
          {
            text: 'Ok',
            handler: (data) => {
              console.log(data);
              this.selectedSalesPerson = data
            },
          },
        ],
      });
  
      await alert.present();
      
    });
  }
  getTotal() {
    (this.totalWeight = 0), (this.totalNo = 0), (this.totalAmount = 0);
    this.product_data.forEach((element) => {
      if (element.weight) {
        this.totalWeight += element.weight;
      }
      if (element.no) {
        this.totalNo += element.no;
      }
      if (element.total) {
        this.totalAmount += element.total;
      }
    });
    this.calculateInvoiceTotals();
    this.modifyTotal();
  }
  onSearchChange(ev) {
    console.log(ev);

    const val = ev.detail.value;
    if (val.length >= 1) {
      this.displayProducts = this.product_data.filter((item: any) => {
        return item.name.toLowerCase().indexOf(val.toLowerCase()) > -1;
      });
      this.displayProducts.length <= 0 ? (this.filterEmpty = true) : (this.filterEmpty = false);
    } else {
      this.displayProducts = this.product_data;
    }

  }

  // Product creation methods
  openProductForm() {
    this.isProductFormOpen = true;
  }

  // Image upload functionality removed - moved to sales bill page

  closeProductForm() {
    this.isProductFormOpen = false;
  }

  async onProductCreated(newProduct: any) {
    // Refresh the product data to include the newly created product
    await this.getData();
    this.toast.toastServices('Product created successfully and is now available for selection', 'success', 'top');
  }

  // New methods for improved UI
  openPaymentModal() {
    this.isPaymentModalOpen = true;
  }

  closePaymentModal() {
    this.isPaymentModalOpen = false;
  }

  toggleMoreActions() {
    this.showMoreActions = !this.showMoreActions;
  }

  togglePaymentDetails() {
    this.showPaymentDetails = !this.showPaymentDetails;
  }

  async selectPaymentMode() {
    const options = this.modeOfPayments.map(mode => ({
      name: mode.slug,
      type: 'radio',
      label: mode.name,
      value: mode.slug,
      checked: this.modeOfPayment === mode.slug
    }));

    const alert = await this.alertController.create({
      header: 'Select Payment Mode',
      inputs: options,
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'OK',
          handler: (selectedMode) => {
            if (selectedMode) {
              this.modeOfPayment = selectedMode;
            }
          }
        }
      ]
    });

    await alert.present();
  }

  getSelectedPaymentModeName(): string {
    if (!this.modeOfPayment) return '';
    const selectedMode = this.modeOfPayments.find(mode => mode.slug === this.modeOfPayment);
    return selectedMode ? selectedMode.name : '';
  }
  async editInvoice() {
    if (this.received_amount && !this.modeOfPayment) {
      this.toast.toastServices("Please select the mode of payment", "danger", "top");
      return
    }

    // Validate invoice calculations before saving
    if (!this.validateInvoiceCalculations()) {
      this.toast.toastServices("Invoice calculation error. Please check the invoice totals.", "danger", "top");
      return;
    }

    let invoice = {
      id: this.editId,
      previous_balance: this.current_balance,
      bill_amount: this.getEffectiveTotal(),
      receivable_amount: this.receivable_amount,
      received_amount: this.received_amount,
      current_balance: this.closing_balance,
      remarks: this.invoice_remarks,
      metadata: this.billingAdditionalFields,
      headerdata: this.billingHeaderData,
      sales_person_id: this.selectedSalesPerson,
      date: moment(this.date).format("YYYY-MM-DD"),
      // Rounding fields
      rounding_enabled: this.roundingConfig.enabled,
      rounding_adjustment: this.roundingAdjustment,
      gross_total: this.invoiceGrossTotal,
      // Staff information fields
      billed_by: this.billedBy,
      delivery_by: this.deliveryBy,
      collected_by: this.collectedBy
    };
    let invoice_item = [];
    this.product_data.forEach((element) => {
      if (element.weight > 0 || element.no > 0) {
        let item = element.itemId
          ? {
            id: element.itemId,
            mrp: Number(element.mrp),
            rate: element.rate,
            weight: element.weight ? element.weight : 0,
            weights: element.weights ? element.weights : 0,
            no: element.no ? element.no : 0,
            line_total: element.total,
            remarks: element.remarks
          }
          : {
            product_id: element.id,
            mrp: Number(element.mrp),
            rate: element.rate,
            weight: element.weight ? element.weight : 0,
            weights: element.weights ? element.weights : 0,
            no: element.no ? element.no : 0,
            line_total: element.total,
            remarks: element.remarks
          };
        invoice_item.push(item);
      }
    });
    

    let deletable_products = this.product_data.filter(e => e.itemId && (!e.weight || e.weight == 0) && (!e.no || e.no == 0)).map(e => e.itemId)
  
    
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .editSalesInvoice({ invoice: invoice, invoice_item: invoice_item, deletable_products: deletable_products, mode_of_payment: this.modeOfPayment })
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, "success", "top");
            this.routerService.navigateBackAndRefresh(this.getReturnUrl());
          } else {
            this.toast.toastServices(res.message, "danger", "top");
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err.error.message, "danger", "top");
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }
  async savePurchaseInvoice() {
    if (this.received_amount && !this.modeOfPayment) {
      this.toast.toastServices("Please select the mode of payment", "danger", "top");
      return
    }
    let invoice = {
      id: this.editId,
      previous_balance: this.current_balance,
      bill_amount: +this.totalAmount + +this.billingAdditionalFieldsTotal,
      receivable_amount: this.receivable_amount,
      received_amount: this.received_amount,
      current_balance: this.closing_balance,
      remarks: this.invoice_remarks,
      headerdata: this.billingHeaderData,
      metadata: this.billingAdditionalFields,
      date: moment(this.date).format("YYYY-MM-DD"),
    };
    let invoice_item = [];
    this.product_data.forEach((element) => {
      if (element.weight > 0 || element.no > 0) {
        let item = element.itemId
          ? {
            id: element.itemId,
            mrp: Number(element.mrp),
            rate: element.rate,
            weight: element.weight ? element.weight : 0,
            weights: element.weights ? element.weights : 0,
            no: element.no ? element.no : 0,
            line_total: element.total,
            remarks: element.remarks
          }
          : {
            product_id: element.id,
            mrp: Number(element.mrp),
            rate: element.rate,
            weight: element.weight ? element.weight : 0,
            weights: element.weights ? element.weights : 0,
            no: element.no ? element.no : 0,
            line_total: element.total,
            remarks: element.remarks
          };
        invoice_item.push(item);
      }
    });

    let deletable_products = this.product_data.filter(e => e.itemId && (!e.weight || e.weight == 0) && (!e.no || e.no == 0)).map(e => e.itemId)
    await this.ionLoaderService.startLoader().then(async () => {
      await this.purchase_api
        .editPurchaseInvoice({ invoice, invoice_item: invoice_item, mode_of_payment: this.modeOfPayment,deletable_products:deletable_products })
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, "success", "top");
            this.routerService.navigateBackAndRefresh(this.getReturnUrl());
          } else {
            this.toast.toastServices(res.message, "danger", "top");
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err.error.message, "danger", "top");
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });

  }

  async addBillingHeaderData() {
    const options = this.billingHeaderData.map(item => ({
      name: item.slug,
      type: 'text',
      label: item.name,
      placeholder: "Enter " + item.name,
      value: item.value || '', // Set initial value if it exists
    }));

    const alert = await this.alertController.create({
      header: 'Enter Billing Header Data',
      inputs: options,
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {
            console.log('Confirm Cancel');
          },
        },
        {
          text: 'Ok',
          handler: (mode) => {
            console.log(mode);

            // Update billingHeaderData with the entered values
            this.billingHeaderData = this.billingHeaderData.map(item => ({
              ...item,
              value: mode[item.slug] || '', // Set the value from the input fields
            }));

            console.log('Updated billingHeaderData:', this.billingHeaderData);
          },
        },
      ],
    });

    await alert.present();
  }
  handleClick(e: Event, i) {
    const currentTime = new Date().getTime();
    const elapsedTime = currentTime - this.lastClickTime;
    if (elapsedTime < 300) { // Adjust this value as per your requirement for detecting double click
      this.presentProductDetailsPopover(e, i);
    } else {
      // Perform single click action here
    }
    this.lastClickTime = currentTime;
  }

  presentProductDetailsPopover(e: Event, i) {
    this.popover.event = e;
    this.isProductDetailsOpen = true;
    this.selectedIndex = this.product_data.findIndex((e) => e.id == i);
  }
  changeRateRequest() {
    this.changeRateRequestFlag = true;
  }
  changeRateRequestFunction(value) {
    if (value.password == "Abinesh*1998") {
      this.changeRateRequestFlag = false;
      this.lock = false;
      this.message = null;
      return true;
    } else {
      this.message = "Password not matching"
      return false;
    }

  }
  updateProductData(modifiedField: string) {
    let mrp = this.product_data[this.selectedIndex].mrp;
    let rate = this.product_data[this.selectedIndex].rate;
    let margin = this.product_data[this.selectedIndex].margin;
    switch (modifiedField) {
      case 'mrp':
        this.product_data[this.selectedIndex].rate = mrp - (mrp * margin) / 100;
        break;
      case 'rate':
        this.product_data[this.selectedIndex].margin = ((mrp - rate) / mrp) * 100;
        break;
      case 'margin':
        this.product_data[this.selectedIndex].rate = mrp - (mrp * margin) / 100;
        break;
      // case 'remarks':
      //   this.product_data[this.selectedIndex].remarks = mrp - (mrp * margin) / 100;
      //   break;
      default:
        break;
    }
  }
  parseStyle(style: any): { [key: string]: string } {
    if (!style || typeof style !== 'object') {
      return {};
    }
    return Object.keys(style).reduce((acc, key) => {
      acc[key] = style[key];
      return acc;
    }, {});
  }

  async editRemarks() {

    const alert = await this.alertController.create({
      header: 'Enter Remarks',
      inputs: [
        {
          name: 'remarks',
          type: 'textarea',
          placeholder: 'Enter your remarks here...',
          value: this.invoice_remarks,
          attributes: {
            rows: 5, // Optional: Defines the visible height of the textarea
            maxlength: 500, // Optional: Limits the input characters
          },
        },
      ],
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel',
          handler: () => {
            console.log('User canceled');
          },
        },
        {
          text: 'Submit',
          handler: (data) => {
            console.log('Remarks:', data.remarks);
            this.invoice_remarks = data.remarks; // Optional: Pass the remarks to another method
          },
        },
      ],
    });

    await alert.present();

  }
  handleDiscountChange(field: any, changedField: string) {
    if (field.fieldType === 'discount') {
      if (changedField === 'percentage') {
        // Calculate amount based on percentage
        field.value = (+this.totalAmount * field.percentage) / 100;
      } else if (changedField === 'value') {
        // Calculate percentage based on amount
        field.percentage = (field.value / +this.totalAmount) * 100;
      }
      this.handleCalculations();
    }
  }
  handleCalculations() {
    this.billingAdditionalFieldsTotal = 0;
    this.billingAdditionalFields.filter(item => item.value).forEach(f => {
      if (f.operation === 'add') {
        this.billingAdditionalFieldsTotal = +this.billingAdditionalFieldsTotal + +f.value || 0;
      } else if (f.operation === 'minus') {
        this.billingAdditionalFieldsTotal = +this.billingAdditionalFieldsTotal - +f.value || 0;
      }
    });
    this.calculateInvoiceTotals();
    this.modifyTotal();
  }

  handleDynamicFieldChange(fieldSlug: string, value: any): void {
    const field = this.billingAdditionalFields.find(f => f.slug === fieldSlug);

    if (field) {
      // Parse the value to a number to ensure calculations work correctly
      const numericValue = parseFloat(value) || 0;

      field.value = numericValue;

      // Recalculate the total amount based on all active fields
      this.handleCalculations();
    }
  }

  // Invoice calculation methods with proper order
  calculateInvoiceTotals() {
    // Step 1: Calculate subtotal from original item amounts
    this.invoiceSubtotal = +this.totalAmount;

    // Step 2: Calculate tax amount (this should be calculated from original subtotal)
    // For now, we'll extract tax from billingAdditionalFieldsTotal
    // In a real implementation, this should be calculated properly from tax rates
    this.invoiceTaxAmount = +this.billingAdditionalFieldsTotal;

    // Step 3: Calculate gross total (subtotal + tax)
    this.invoiceGrossTotal = this.invoiceSubtotal + this.invoiceTaxAmount;

    // Step 4: Apply rounding to gross total and calculate net amount
    const invoiceTotals = this.roundingService.calculateInvoiceTotals(
      this.invoiceSubtotal,
      this.invoiceTaxAmount,
      this.roundingConfig
    );

    this.roundingAdjustment = invoiceTotals.roundingAdjustment;
    this.invoiceNetAmount = invoiceTotals.netAmount;
  }

  onRoundingToggle() {
    this.calculateInvoiceTotals();
    this.modifyTotal();
  }

  formatRoundingAdjustment(): string {
    return this.roundingService.formatRoundingAdjustment(this.roundingAdjustment);
  }

  getEffectiveTotal(): number {
    return this.invoiceNetAmount;
  }

  // Validation method to ensure invoice calculations are correct
  validateInvoiceCalculations(): boolean {
    const expectedGrossTotal = this.invoiceSubtotal + this.invoiceTaxAmount;

    // Validate gross total calculation
    if (Math.abs(this.invoiceGrossTotal - expectedGrossTotal) > 0.01) {
      console.warn('Gross total calculation validation failed:', {
        expected: expectedGrossTotal,
        actual: this.invoiceGrossTotal
      });
      return false;
    }

    // Validate rounding calculation if enabled
    if (this.roundingConfig.enabled) {
      const roundingResult = this.roundingService.applyRoundingToNearest(this.invoiceGrossTotal);
      const expectedNetAmount = roundingResult.roundedAmount;
      const expectedAdjustment = roundingResult.adjustment;

      if (Math.abs(this.invoiceNetAmount - expectedNetAmount) > 0.01 ||
          Math.abs(this.roundingAdjustment - expectedAdjustment) > 0.01) {
        console.warn('Rounding calculation validation failed:', {
          expected: { netAmount: expectedNetAmount, adjustment: expectedAdjustment },
          actual: { netAmount: this.invoiceNetAmount, adjustment: this.roundingAdjustment }
        });
        return false;
      }
    }

    return true;
  }

  save() {
    switch (this.state) {
      case 'purchase':
        this.savePurchaseInvoice();
        break;
      // case 'sales-order':
      //   this.editInvoice('order');
      //   break;
      default:
        this.editInvoice();
        break;
    }
  }
  getReturnUrl() {
    switch (this.state) {
      case 'purchase':
        return 'purchase-bill';
      case 'sales-order':
        return 'sales-order';
      default:
        return 'tabs/sales-bill';
    }
  }
  ngOnDestroy(): void {
    localStorage.setItem('state', '');
  }
}
