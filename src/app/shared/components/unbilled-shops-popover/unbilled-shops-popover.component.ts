import { Component, Input, Output, EventEmitter } from '@angular/core';
import { PopoverController } from '@ionic/angular';

export interface UnbilledShop {
  id: number;
  name: string;
  phone_no?: string;
  place?: string;
  route_name?: string;
  expected_amount?: number;
  last_billed_date?: string;
}

@Component({
  selector: 'app-unbilled-shops-popover',
  templateUrl: './unbilled-shops-popover.component.html',
  styleUrls: ['./unbilled-shops-popover.component.scss'],
})
export class UnbilledShopsPopoverComponent {
  @Input() shops: UnbilledShop[] = [];
  @Input() routeName: string = '';
  @Input() date: string = '';
  @Output() shopSelected = new EventEmitter<UnbilledShop>();
  @Output() callShop = new EventEmitter<UnbilledShop>();

  constructor(private popoverController: PopoverController) { }

  onShopClick(shop: UnbilledShop) {
    this.popoverController.dismiss({
      action: 'shop_selected',
      shop: shop
    });
  }

  onCallShop(event: Event, shop: UnbilledShop) {
    event.stopPropagation();
    this.popoverController.dismiss({
      action: 'call_shop',
      shop: shop
    });
  }

  async closePopover() {
    await this.popoverController.dismiss();
  }

  getShopStatusColor(shop: UnbilledShop): string {
    if (!shop.last_billed_date) {
      return 'danger'; // Never billed
    }
    
    const lastBilled = new Date(shop.last_billed_date);
    const today = new Date();
    const daysDiff = Math.floor((today.getTime() - lastBilled.getTime()) / (1000 * 3600 * 24));
    
    if (daysDiff > 7) {
      return 'danger'; // More than a week
    } else if (daysDiff > 3) {
      return 'warning'; // More than 3 days
    } else {
      return 'medium'; // Recent
    }
  }

  getLastBilledText(shop: UnbilledShop): string {
    if (!shop.last_billed_date) {
      return 'Never billed';
    }
    
    const lastBilled = new Date(shop.last_billed_date);
    const today = new Date();
    const daysDiff = Math.floor((today.getTime() - lastBilled.getTime()) / (1000 * 3600 * 24));
    
    if (daysDiff === 0) {
      return 'Today';
    } else if (daysDiff === 1) {
      return 'Yesterday';
    } else if (daysDiff < 7) {
      return `${daysDiff} days ago`;
    } else if (daysDiff < 30) {
      const weeks = Math.floor(daysDiff / 7);
      return `${weeks} week${weeks > 1 ? 's' : ''} ago`;
    } else {
      const months = Math.floor(daysDiff / 30);
      return `${months} month${months > 1 ? 's' : ''} ago`;
    }
  }

  formatAmount(amount: number): string {
    if (!amount) return '₹0';
    return `₹${amount.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  }

  trackByShopId(index: number, shop: UnbilledShop): number {
    return shop.id;
  }

  // Helper methods for template
  hasShopsWithExpectedAmount(): boolean {
    return this.shops.some(shop => shop.expected_amount && shop.expected_amount > 0);
  }

  getTotalExpectedAmount(): number {
    return this.shops.reduce((sum, shop) => sum + (shop.expected_amount || 0), 0);
  }
}
