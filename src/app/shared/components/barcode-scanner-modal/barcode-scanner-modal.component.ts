import { Component, OnInit, OnD<PERSON>roy, ViewChild, ElementRef } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { BarcodeScannerService } from '../../services/barcode-scanner.service';
import { ToastService } from '../../services/toast.service';

@Component({
  selector: 'app-barcode-scanner-modal',
  templateUrl: './barcode-scanner-modal.component.html',
  styleUrls: ['./barcode-scanner-modal.component.scss']
})
export class BarcodeScannerModalComponent implements OnInit, OnDestroy {
  @ViewChild('video', { static: false }) videoElement!: ElementRef<HTMLVideoElement>;
  @ViewChild('canvas', { static: false }) canvasElement!: ElementRef<HTMLCanvasElement>;

  isScanning = false;
  hasPermission = false;
  stream: MediaStream | null = null;
  scanningText = 'Position barcode within the frame';

  constructor(
    private modalController: <PERSON>dal<PERSON><PERSON>roller,
    private barcodeScanner: BarcodeScannerService,
    private toast: ToastService
  ) {}

  async ngOnInit() {
    await this.initializeCamera();
  }

  ngOnDestroy() {
    this.stopScanning();
  }

  async initializeCamera() {
    try {
      // Check camera permission
      this.hasPermission = await this.barcodeScanner.checkCameraPermission();
      
      if (!this.hasPermission) {
        this.hasPermission = await this.barcodeScanner.requestCameraPermission();
      }

      if (this.hasPermission) {
        await this.startCamera();
      } else {
        this.toast.toastServices('Camera permission is required for barcode scanning', 'danger', 'top');
      }
    } catch (error) {
      console.error('Error initializing camera:', error);
      this.toast.toastServices('Failed to access camera', 'danger', 'top');
    }
  }

  async startCamera() {
    try {
      this.stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'environment',
          width: { ideal: 1280 },
          height: { ideal: 720 }
        }
      });

      if (this.videoElement) {
        this.videoElement.nativeElement.srcObject = this.stream;
        this.videoElement.nativeElement.play();
        this.isScanning = true;
        this.startBarcodeDetection();
      }
    } catch (error) {
      console.error('Error starting camera:', error);
      this.toast.toastServices('Failed to start camera', 'danger', 'top');
    }
  }

  startBarcodeDetection() {
    // Simulate barcode detection
    // In a real implementation, you would use a barcode detection library
    // like ZXing or QuaggaJS to detect barcodes from the video stream
    
    const detectionInterval = setInterval(() => {
      if (!this.isScanning) {
        clearInterval(detectionInterval);
        return;
      }

      // Simulate barcode detection after 3 seconds
      // This is where you would implement actual barcode detection
      setTimeout(() => {
        if (this.isScanning) {
          this.onBarcodeDetected('ABC000001'); // Mock barcode data
          clearInterval(detectionInterval);
        }
      }, 3000);
    }, 100);
  }

  onBarcodeDetected(barcodeData: string) {
    this.scanningText = `Barcode detected: ${barcodeData}`;
    this.stopScanning();
    
    // Parse barcode data
    const parsedData = this.barcodeScanner.parseBarcodeData(barcodeData);
    
    if (parsedData) {
      this.modalController.dismiss({
        success: true,
        data: parsedData,
        rawData: barcodeData
      });
    } else {
      this.toast.toastServices('Invalid barcode format', 'danger', 'top');
      this.modalController.dismiss({
        success: false,
        error: 'Invalid barcode format'
      });
    }
  }

  stopScanning() {
    this.isScanning = false;
    
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.stream = null;
    }
  }

  async closeModal() {
    this.stopScanning();
    await this.modalController.dismiss({
      success: false,
      cancelled: true
    });
  }

  // Manual barcode input for testing
  async manualInput() {
    // For testing purposes, allow manual barcode input
    const mockBarcodes = [
      'ABC000001',
      'ABC000002', 
      'ABC000003',
      'INV000001',
      'INV000002'
    ];
    
    const randomBarcode = mockBarcodes[Math.floor(Math.random() * mockBarcodes.length)];
    this.onBarcodeDetected(randomBarcode);
  }
}
