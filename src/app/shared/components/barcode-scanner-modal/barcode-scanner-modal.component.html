<ion-header>
  <ion-toolbar>
    <ion-title>Scan Invoice Barcode</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="closeModal()">
        <ion-icon name="close" slot="icon-only"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="scanner-content">
  <!-- Camera Permission Request -->
  <div *ngIf="!hasPermission" class="permission-container">
    <ion-card class="permission-card">
      <ion-card-content>
        <div class="permission-content">
          <ion-icon name="camera-outline" class="permission-icon"></ion-icon>
          <h2>Camera Access Required</h2>
          <p>Please allow camera access to scan invoice barcodes</p>
          <ion-button (click)="initializeCamera()" expand="block" fill="solid">
            <ion-icon name="camera" slot="start"></ion-icon>
            Enable Camera
          </ion-button>
        </div>
      </ion-card-content>
    </ion-card>
  </div>

  <!-- Scanner Interface -->
  <div *ngIf="hasPermission" class="scanner-container">
    <!-- Video Preview -->
    <div class="video-container">
      <video #video class="scanner-video" autoplay playsinline></video>
      <canvas #canvas class="scanner-canvas" style="display: none;"></canvas>
      
      <!-- Scanner Overlay -->
      <div class="scanner-overlay">
        <div class="scanner-frame">
          <div class="corner top-left"></div>
          <div class="corner top-right"></div>
          <div class="corner bottom-left"></div>
          <div class="corner bottom-right"></div>
        </div>
      </div>
    </div>

    <!-- Scanner Status -->
    <div class="scanner-status">
      <div class="status-content">
        <ion-icon 
          name="scan-outline" 
          class="scan-icon"
          [class.scanning]="isScanning">
        </ion-icon>
        <p class="scanning-text">{{ scanningText }}</p>
        
        <!-- Scanning Animation -->
        <div *ngIf="isScanning" class="scanning-line"></div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
      <ion-button 
        (click)="manualInput()" 
        fill="outline" 
        expand="block"
        class="manual-button">
        <ion-icon name="create-outline" slot="start"></ion-icon>
        Test with Sample Barcode
      </ion-button>
      
      <ion-button 
        (click)="closeModal()" 
        fill="clear" 
        expand="block"
        class="cancel-button">
        <ion-icon name="close-outline" slot="start"></ion-icon>
        Cancel
      </ion-button>
    </div>
  </div>

  <!-- Instructions -->
  <div class="instructions">
    <ion-card class="instructions-card">
      <ion-card-content>
        <h3>How to scan:</h3>
        <ul>
          <li>Point your camera at the invoice barcode</li>
          <li>Keep the barcode within the frame</li>
          <li>Hold steady until the barcode is detected</li>
          <li>The invoice details will appear automatically</li>
        </ul>
      </ion-card-content>
    </ion-card>
  </div>
</ion-content>
