<ion-card class="import-export-card">
  <ion-card-header>
    <ion-card-title class="card-title">
      <ion-icon name="swap-horizontal-outline" class="title-icon"></ion-icon>
      Import/Export Margins
    </ion-card-title>
    <ion-card-subtitle class="card-subtitle">
      Manage your retailer class margins with CSV files
    </ion-card-subtitle>
  </ion-card-header>

  <ion-card-content class="card-content">
    <ion-grid class="action-grid">
      <ion-row class="action-row">
        <!-- File Upload Section -->
        <ion-col size="12" size-md="6" class="upload-section">
          <div class="section-header">
            <ion-icon name="cloud-upload-outline" class="section-icon"></ion-icon>
            <h3>Import Margins</h3>
          </div>
          <p class="section-description">
            Upload a CSV file to update margin values for multiple products at once.
          </p>
          
          <div class="file-input-wrapper">
            <input
              #fileInput
              type="file"
              (change)="onFileSelected($event)"
              accept=".csv"
              class="file-input"
              id="csvFileInput">
            <ion-button
              fill="outline"
              expand="block"
              class="file-select-btn"
              (click)="fileInput.click()"
              [disabled]="isUploading">
              <ion-icon name="document-outline" slot="start"></ion-icon>
              {{selectedFile ? selectedFile.name : 'Choose CSV File'}}
            </ion-button>
          </div>
          
          <ion-button
            expand="block"
            fill="solid"
            color="success"
            class="action-btn upload-btn"
            [disabled]="!selectedFile || isUploading"
            (click)="uploadCsv()">
            <ion-icon name="cloud-upload" slot="start"></ion-icon>
            <span *ngIf="!isUploading">Upload CSV</span>
            <span *ngIf="isUploading">Uploading...</span>
            <ion-spinner *ngIf="isUploading" name="crescent" slot="end"></ion-spinner>
          </ion-button>
        </ion-col>

        <!-- File Download Section -->
        <ion-col size="12" size-md="6" class="download-section">
          <div class="section-header">
            <ion-icon name="cloud-download-outline" class="section-icon"></ion-icon>
            <h3>Export Margins</h3>
          </div>
          <p class="section-description">
            Download current margin data as a CSV file for editing or backup.
          </p>
          
          <div class="download-info">
            <div class="info-item">
              <ion-icon name="information-circle-outline" class="info-icon"></ion-icon>
              <span>Includes all products with current margins</span>
            </div>
            <div class="info-item">
              <ion-icon name="create-outline" class="info-icon"></ion-icon>
              <span>Edit margins and re-import</span>
            </div>
          </div>
          
          <ion-button
            expand="block"
            fill="solid"
            color="primary"
            class="action-btn download-btn"
            [disabled]="isDownloading"
            (click)="downloadCsv()">
            <ion-icon name="cloud-download" slot="start"></ion-icon>
            <span *ngIf="!isDownloading">Download CSV</span>
            <span *ngIf="isDownloading">Preparing...</span>
            <ion-spinner *ngIf="isDownloading" name="crescent" slot="end"></ion-spinner>
          </ion-button>
        </ion-col>
      </ion-row>
    </ion-grid>

    <!-- Instructions Section -->
    <div class="instructions-section">
      <ion-item lines="none" class="instructions-header">
        <ion-icon name="help-circle-outline" slot="start" class="help-icon"></ion-icon>
        <ion-label>
          <h3>CSV Format Instructions</h3>
        </ion-label>
      </ion-item>
      
      <div class="instructions-content">
        <div class="instruction-item">
          <strong>Required Columns:</strong>
          <ul>
            <li><code>product_id</code> - Product identifier</li>
            <li><code>margin</code> - Margin percentage value</li>
          </ul>
        </div>
        
        <div class="instruction-item">
          <strong>Optional Columns:</strong>
          <ul>
            <li><code>id</code> - For updating existing margins</li>
            <li><code>action</code> - Use "delete" to remove margins</li>
          </ul>
        </div>
        
        <div class="instruction-item">
          <strong>Tips:</strong>
          <ul>
            <li>Download current data first to see the format</li>
            <li>Leave <code>id</code> empty for new margin entries</li>
            <li>Use decimal values for margins (e.g., 15.5)</li>
          </ul>
        </div>
      </div>
    </div>
  </ion-card-content>
</ion-card>
