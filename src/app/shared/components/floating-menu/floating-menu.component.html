<ion-fab vertical="bottom" horizontal="end">
  <ion-fab-button [size]="size">
    <ion-icon name="arrow-undo-outline"></ion-icon>
  </ion-fab-button>
  <ion-fab-list side="start">
    <ion-fab-button *ngIf="!isOnTabsRoute()" (click)="routeService.routerFunction('tabs/home')">
      <ion-icon name="home"></ion-icon>
    </ion-fab-button>
    <ion-fab-button *ngIf="currentLocation != '/tabs/sales-bill'" (click)="routeService.routerFunction('tabs/sales-bill')">
      <ion-icon name="bag-add-outline"></ion-icon>
    </ion-fab-button>
    <ion-fab-button *ngIf="currentLocation != '/purchase-bill'" (click)="routeService.routerFunction('purchase-bill')">
      <ion-icon name="bag-remove-outline"></ion-icon>
    </ion-fab-button>
    <ion-fab-button *ngIf="currentLocation != '/line-accounts'" (click)="routeService.routerFunction('line-accounts')">
      <ion-icon name="trending-down-outline"></ion-icon>
    </ion-fab-button>
    <ion-fab-button *ngIf="currentLocation != '/tally'" (click)="routeService.routerFunction('tally')">
      <ion-icon name="trending-up-outline"></ion-icon>
    </ion-fab-button>
    <ion-fab-button *ngIf="currentLocation != '/report'" (click)="routeService.routerFunction('report')">
      <ion-icon name="document-text-outline"></ion-icon>
    </ion-fab-button>
  </ion-fab-list>
</ion-fab>