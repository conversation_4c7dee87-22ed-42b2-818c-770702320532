import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { SelectDropDownModule } from 'ngx-select-dropdown';

// Standardized Components
import { StandardModalComponent } from './standard-modal/standard-modal.component';
import { StandardFormComponent } from './standard-form/standard-form.component';
import { LedgerCreateModalComponent } from './ledger-create-modal/ledger-create-modal.component';

// Existing Components (for reference)
import { PaymentReceiptModalComponent } from './payment-receipt-modal/payment-receipt-modal.component';
import { ProductFormComponent } from './product-form/product-form.component';

@NgModule({
  declarations: [
    StandardModalComponent,
    StandardFormComponent,
    LedgerCreateModalComponent,
    PaymentReceiptModalComponent,
    ProductFormComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    SelectDropDownModule
  ],
  exports: [
    StandardModalComponent,
    StandardFormComponent,
    LedgerCreateModalComponent,
    PaymentReceiptModalComponent,
    ProductFormComponent
  ]
})
export class SharedComponentsModule { }

// Export interfaces for use in other modules
export { ModalConfig, ModalButton } from './standard-modal/standard-modal.component';
export { FormSection, FormField, FormFieldOption } from './standard-form/standard-form.component'; 