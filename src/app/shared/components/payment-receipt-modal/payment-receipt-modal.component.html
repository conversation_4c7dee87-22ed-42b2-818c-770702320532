<ion-modal [isOpen]="isOpen" (didDismiss)="closeModal()">
  <ng-template>
    <ion-header translucent>
      <ion-toolbar>
        <ion-title>Payment Receipt</ion-title>
        <ion-buttons slot="end">
          <ion-button (click)="closeModal()">
            <ion-icon name="close" slot="icon-only"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding">
      <!-- Invoice Information -->
      <div class="invoice-info-section" *ngIf="invoiceData">
        <h3 class="section-title">
          <ion-icon name="document-text-outline" class="section-icon"></ion-icon>
          Invoice Details
        </h3>
        
        <ion-card class="invoice-info-card">
          <ion-card-content>
            <ion-row>
              <ion-col size="6">
                <div class="info-item">
                  <span class="info-label">Invoice ID:</span>
                  <span class="info-value">INV-{{invoiceData.id}}</span>
                </div>
              </ion-col>
              <ion-col size="6">
                <div class="info-item">
                  <span class="info-label">Customer:</span>
                  <span class="info-value">{{invoiceData.name || invoiceData.buyer?.name}}</span>
                </div>
              </ion-col>
            </ion-row>
            
            <ion-row>
              <ion-col size="6">
                <div class="info-item">
                  <span class="info-label">Bill Amount:</span>
                  <span class="info-value amount">{{invoiceData.bill_amount | currency: 'INR':'symbol':'1.0-0'}}</span>
                </div>
              </ion-col>
              <ion-col size="6">
                <div class="info-item">
                  <span class="info-label">Outstanding:</span>
                  <span class="info-value amount outstanding">{{outstandingBalance | currency: 'INR':'symbol':'1.0-0'}}</span>
                  <small class="info-note">(suggested payment amount)</small>
                </div>
              </ion-col>
            </ion-row>
          </ion-card-content>
        </ion-card>
      </div>

      <!-- Payment Form -->
      <div class="payment-form-section">
        <h3 class="section-title">
          <ion-icon name="card-outline" class="section-icon"></ion-icon>
          Payment Details
        </h3>

        <form #paymentFormRef="ngForm">
          <!-- Payment Amount -->
          <ion-item class="form-item">
            <ion-label position="stacked">
              Payment Amount
              <ion-text color="danger">*</ion-text>
            </ion-label>
            <ion-input
              type="number"
              [(ngModel)]="paymentForm.amount"
              name="amount"
              required
              [min]="0.01"
              step="0.01"
              placeholder="Enter payment amount"
              (keypress)="numberOnlyValidation($event)">
            </ion-input>
            <ion-button 
              slot="end" 
              fill="clear" 
              size="small"
              (click)="setMaxAmount()"
              class="max-amount-btn"
              title="Set to outstanding balance">
              <ion-icon name="arrow-up-circle-outline" slot="icon-only"></ion-icon>
            </ion-button>
          </ion-item>

          <!-- Payment Mode -->
          <ion-item class="form-item">
            <ion-label position="stacked">
              Payment Mode
              <ion-text color="danger">*</ion-text>
            </ion-label>
            <ion-button
              fill="outline"
              expand="block"
              (click)="selectPaymentMode()"
              class="payment-mode-btn">
              {{getSelectedPaymentModeName()}}
              <ion-icon name="chevron-down" slot="end"></ion-icon>
            </ion-button>
          </ion-item>

          <!-- Reference Number -->
          <ion-item class="form-item">
            <ion-label position="stacked">Reference Number</ion-label>
            <ion-input
              type="text"
              [(ngModel)]="paymentForm.referenceNumber"
              name="referenceNumber"
              placeholder="Enter reference number (optional)">
            </ion-input>
          </ion-item>

          <!-- Payment Date -->
          <ion-item class="form-item">
            <ion-label position="stacked">Payment Date</ion-label>
            <ion-input
              type="date"
              [(ngModel)]="paymentForm.paymentDate"
              name="paymentDate">
            </ion-input>
          </ion-item>

          <!-- Notes -->
          <ion-item class="form-item">
            <ion-label position="stacked">Notes</ion-label>
            <ion-textarea
              [(ngModel)]="paymentForm.notes"
              name="notes"
              placeholder="Enter any additional notes (optional)"
              rows="3">
            </ion-textarea>
          </ion-item>
        </form>
      </div>

      <!-- Action Buttons -->
      <div class="action-buttons">
        <ion-row>
          <ion-col size="6">
            <ion-button
              expand="block"
              fill="outline"
              (click)="closeModal()"
              class="cancel-btn">
              Cancel
            </ion-button>
          </ion-col>
          <ion-col size="6">
            <ion-button
              expand="block"
              (click)="submitPayment()"
              [disabled]="!paymentForm.amount || !paymentForm.modeOfPayment"
              class="submit-btn">
              <ion-icon name="checkmark-circle-outline" slot="start"></ion-icon>
              Create Receipt
            </ion-button>
          </ion-col>
        </ion-row>
      </div>
    </ion-content>
  </ng-template>
</ion-modal>
