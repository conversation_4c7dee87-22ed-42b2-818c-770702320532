/* Import Export Modal Styling */
.import-export-modal {
  --background: var(--ion-background-color);
  --border-radius: 16px;
  --width: 95%;
  --max-width: 600px;
  --height: 90%;
  --max-height: 800px;
}

.modal-content {
  --background: #f8f9fa;
}

/* Modal Header */
.modal-header {
  text-align: center;
  margin-bottom: 24px;
  padding: 16px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-icon {
  font-size: 48px;
  color: var(--ion-color-primary);
  margin-bottom: 8px;
}

.modal-title {
  color: var(--ion-text-color);
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 4px;
}

.modal-subtitle {
  color: var(--ion-text-color-step-600);
  font-size: 14px;
  margin-bottom: 4px;
}

/* Action Sections */
.action-sections {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.action-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

/* Section Headers */
.section-header {
  text-align: center;
  margin-bottom: 20px;
}

.section-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.import-icon {
  color: var(--ion-color-success);
}

.export-icon {
  color: var(--ion-color-primary);
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 8px 0 4px 0;
}

.section-description {
  font-size: 14px;
  color: #666;
  margin: 0;
}

/* File Input */
.file-input-wrapper {
  margin-bottom: 16px;
}

.file-input {
  display: none;
}

.file-select-btn {
  --border-radius: 8px;
  --border-width: 2px;
  --border-style: dashed;
  --border-color: #ddd;
  --color: #666;
  --background: #f9f9f9;
  margin-bottom: 12px;
  height: 48px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.file-select-btn:hover {
  --border-color: var(--ion-color-primary);
  --color: var(--ion-color-primary);
  --background: #f0f8ff;
}

/* Action Buttons */
.action-btn {
  --border-radius: 8px;
  height: 48px;
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
}

.upload-btn {
  --background: var(--ion-color-success);
  --background-hover: var(--ion-color-success-shade);
  --box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.upload-btn:hover {
  transform: translateY(-2px);
  --box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4);
}

.download-btn {
  --background: var(--ion-color-primary);
  --background-hover: var(--ion-color-primary-shade);
  --box-shadow: 0 4px 12px rgba(63, 81, 181, 0.3);
}

.download-btn:hover {
  transform: translateY(-2px);
  --box-shadow: 0 6px 16px rgba(63, 81, 181, 0.4);
}

/* Info Text */
.file-requirements,
.export-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  margin-top: 8px;
}

.file-requirements ion-icon,
.export-info ion-icon {
  font-size: 14px;
  color: var(--ion-color-medium);
}

.file-requirements small,
.export-info small {
  color: var(--ion-color-medium);
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .import-export-modal {
    --width: 95%;
    --max-width: none;
    --height: auto;
    --max-height: 85%;
  }

  .modal-header {
    padding: 12px;
    margin-bottom: 16px;
  }

  .header-icon {
    font-size: 40px;
  }

  .modal-title {
    font-size: 20px;
  }

  .action-section {
    padding: 16px;
  }

  .section-icon {
    font-size: 28px;
  }

  .section-title {
    font-size: 16px;
  }

  .action-btn {
    height: 44px;
    font-size: 14px;
  }

  .file-select-btn {
    height: 44px;
    font-size: 13px;
  }
}

/* Animation */
.action-section {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Disabled State */
.action-btn[disabled] {
  opacity: 0.5;
  transform: none !important;
  --box-shadow: none !important;
}
