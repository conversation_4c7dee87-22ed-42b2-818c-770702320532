import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { IonicModule } from '@ionic/angular';

import { UnbilledShopsModalComponent } from './unbilled-shops-modal.component';

describe('UnbilledShopsModalComponent', () => {
  let component: UnbilledShopsModalComponent;
  let fixture: ComponentFixture<UnbilledShopsModalComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ UnbilledShopsModalComponent ],
      imports: [IonicModule.forRoot()]
    }).compileComponents();

    fixture = TestBed.createComponent(UnbilledShopsModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
