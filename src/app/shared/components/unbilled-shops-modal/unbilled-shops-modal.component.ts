import { Component, Input, OnInit } from '@angular/core';
import { ModalController, NavController } from '@ionic/angular';

export interface UnbilledShop {
  id: number;
  name: string;
  phone_no?: string;
  place?: string;
  route_name?: string;
  route_id?: number;
  expected_amount?: number;
  last_billed_date?: string;
}

@Component({
  selector: 'app-unbilled-shops-modal',
  templateUrl: './unbilled-shops-modal.component.html',
  styleUrls: ['./unbilled-shops-modal.component.scss'],
})
export class UnbilledShopsModalComponent implements OnInit {
  @Input() shops: UnbilledShop[] = [];
  @Input() routeName: string = '';
  @Input() date: string = '';

  searchTerm: string = '';
  filteredShops: UnbilledShop[] = [];

  constructor(
    private modalController: ModalController,
    private navController: NavController
  ) { }

  ngOnInit() {
    this.filteredShops = [...this.shops];
  }

  async closeModal() {
    await this.modalController.dismiss();
  }

  onSearchChange(event: any) {
    const searchTerm = event.target.value.toLowerCase();
    this.searchTerm = searchTerm;
    
    if (searchTerm.trim() === '') {
      this.filteredShops = [...this.shops];
    } else {
      this.filteredShops = this.shops.filter(shop => 
        shop.name.toLowerCase().includes(searchTerm) ||
        (shop.place && shop.place.toLowerCase().includes(searchTerm)) ||
        (shop.phone_no && shop.phone_no.includes(searchTerm))
      );
    }
  }

  async onShopClick(shop: UnbilledShop) {
    await this.modalController.dismiss({
      action: 'shop_selected',
      shop: shop
    });
  }

  async onCallShop(event: Event, shop: UnbilledShop) {
    event.stopPropagation();
    if (shop.phone_no) {
      window.open(`tel:${shop.phone_no}`, '_system');
    }
  }

  async onCreateInvoice(event: Event, shop: UnbilledShop) {
    event.stopPropagation();
    await this.modalController.dismiss({
      action: 'create_invoice',
      shop: shop
    });
  }



  getShopStatusColor(shop: UnbilledShop): string {
    if (!shop.last_billed_date) {
      return 'danger'; // Never billed
    }
    
    const lastBilled = new Date(shop.last_billed_date);
    const today = new Date();
    const daysDiff = Math.floor((today.getTime() - lastBilled.getTime()) / (1000 * 3600 * 24));
    
    if (daysDiff > 7) {
      return 'danger'; // More than a week
    } else if (daysDiff > 3) {
      return 'warning'; // More than 3 days
    } else {
      return 'medium'; // Recent
    }
  }

  getLastBilledText(shop: UnbilledShop): string {
    if (!shop.last_billed_date) {
      return 'Never billed';
    }
    
    const lastBilled = new Date(shop.last_billed_date);
    const today = new Date();
    const daysDiff = Math.floor((today.getTime() - lastBilled.getTime()) / (1000 * 3600 * 24));
    
    if (daysDiff === 0) {
      return 'Today';
    } else if (daysDiff === 1) {
      return 'Yesterday';
    } else if (daysDiff < 7) {
      return `${daysDiff} days ago`;
    } else if (daysDiff < 30) {
      const weeks = Math.floor(daysDiff / 7);
      return `${weeks} week${weeks > 1 ? 's' : ''} ago`;
    } else {
      const months = Math.floor(daysDiff / 30);
      return `${months} month${months > 1 ? 's' : ''} ago`;
    }
  }

  formatAmount(amount: number): string {
    if (!amount) return '₹0';
    return `₹${amount.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  }

  trackByShopId(index: number, shop: UnbilledShop): number {
    return shop.id;
  }

  // Helper methods for template
  hasShopsWithExpectedAmount(): boolean {
    return this.filteredShops.some(shop => shop.expected_amount && shop.expected_amount > 0);
  }

  getTotalExpectedAmount(): number {
    return this.filteredShops.reduce((sum, shop) => sum + (shop.expected_amount || 0), 0);
  }

  getFilteredShopsCount(): number {
    return this.filteredShops.length;
  }

  async viewAllInSalesBill() {
    await this.modalController.dismiss({
      action: 'view_all_sales_bill'
    });
  }
}
