// Modal Header
.modal-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  
  .title-icon {
    font-size: 20px;
    color: var(--ion-color-primary);
  }
}

.modal-subtitle {
  font-size: 12px;
  font-weight: 400;
  opacity: 0.7;
  margin-top: 2px;
}

// Content
.modal-content {
  --padding-top: 0;
  --padding-bottom: 0;
}

// Search Section
.search-section {
  padding: 16px;
  background: var(--ion-color-light);
  border-bottom: 1px solid var(--ion-color-light-shade);
}

// Summary Section
.summary-section {
  padding: 16px;
}

.summary-card {
  margin: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
}

.summary-stats {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 140px;
  
  .summary-icon {
    font-size: 24px;
    color: var(--ion-color-primary);
    background: var(--ion-color-primary-tint);
    padding: 8px;
    border-radius: 8px;
  }
  
  .summary-info {
    display: flex;
    flex-direction: column;
    
    .summary-label {
      font-size: 12px;
      color: var(--ion-color-medium);
      font-weight: 500;
    }
    
    .summary-value {
      font-size: 16px;
      font-weight: 600;
      color: var(--ion-color-dark);
    }
  }
}

// Shops Section
.shops-section {
  padding: 0 16px;
}

.shop-item {
  --padding-start: 0;
  --padding-end: 0;
  --inner-padding-end: 0;
  margin-bottom: 8px;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  
  &:hover {
    --background: var(--ion-color-light);
  }
}

.shop-content {
  flex: 1;
  padding: 16px;
}

.shop-header {
  margin-bottom: 12px;
}

.shop-main-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  
  .shop-name {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--ion-color-dark);
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.shop-details {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.detail-row {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .detail-icon {
    font-size: 14px;
    color: var(--ion-color-medium);
    flex-shrink: 0;
    width: 16px;
  }
  
  .detail-text {
    font-size: 13px;
    color: var(--ion-color-medium);
    flex: 1;
  }
}

.shop-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 16px 8px;

  .action-button {
    --padding-start: 8px;
    --padding-end: 8px;
    height: 36px;
    width: 36px;

    ion-icon {
      font-size: 18px;
    }

    &.call-button {
      --color: var(--ion-color-success);
    }

    &.invoice-button {
      --color: var(--ion-color-primary);
    }
  }
}

// Empty State
.empty-state {
  text-align: center;
  padding: 48px 24px;
  
  .empty-icon {
    font-size: 64px;
    color: var(--ion-color-medium);
    margin-bottom: 16px;
    
    &.success {
      color: var(--ion-color-success);
    }
  }
  
  h4 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--ion-color-dark);
  }
  
  p {
    margin: 0;
    font-size: 14px;
    color: var(--ion-color-medium);
  }
}

// Footer
.footer-actions {
  padding: 16px;
  
  ion-button {
    --border-radius: 12px;
    height: 44px;
    font-weight: 600;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .summary-stats {
    flex-direction: column;
    gap: 16px;
  }
  
  .summary-item {
    min-width: auto;
  }
  
  .shop-main-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .shop-actions {
    flex-direction: row;
    padding: 8px;
    gap: 2px;

    .action-button {
      height: 32px;
      width: 32px;

      ion-icon {
        font-size: 16px;
      }
    }
  }
}


