import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { ModalConfig, ModalButton } from '../standard-modal/standard-modal.component';
import { FormSection, FormField } from '../standard-form/standard-form.component';

@Component({
  selector: 'app-ledger-create-modal',
  templateUrl: './ledger-create-modal.component.html',
  styleUrls: ['./ledger-create-modal.component.scss']
})
export class LedgerCreateModalComponent implements OnInit {
  @Input() isOpen: boolean = false;
  @Input() loading: boolean = false;
  @Input() buyerOptions: any[] = [];
  @Input() supplierOptions: any[] = [];
  @Input() partyOptions: any[] = [];
  @Input() modeOfPaymentOptions: any[] = [];
  @Input() ledgerFieldName: any = {};

  @Output() modalClosed = new EventEmitter<void>();
  @Output() formSubmit = new EventEmitter<any>();

  modalConfig: ModalConfig = {
    title: 'Add Ledger Entry',
    subtitle: 'Create a new ledger transaction',
    size: 'large',
    color: 'primary',
    showCloseButton: true,
    showBackdrop: true,
    backdropDismiss: true,
    buttons: [
      {
        text: 'Cancel',
        color: 'medium',
        fill: 'outline',
        icon: 'close-outline',
        handler: () => this.closeModal()
      },
      {
        text: 'Create Entry',
        color: 'primary',
        fill: 'solid',
        icon: 'checkmark-outline',
        type: 'submit',
        disabled: false
      }
    ]
  };

  formSections: FormSection[] = [];
  activeSegment: string = 'pay-in';
  selectedBuyerOption: any = null;
  selectedSupplierOption: any = null;
  selectedFromOption: any = null;
  selectedToOption: any = null;
  modeOfPaymentSelectedOption: any = null;
  selectedFile: File | null = null;
  previewUrl: string | null = null;

  ngOnInit() {
    this.buildFormSections();
  }

  buildFormSections() {
    this.formSections = [
      {
        title: 'Transaction Type',
        icon: 'swap-horizontal-outline',
        fields: [
          {
            name: 'transactionType',
            label: 'Transaction Type',
            type: 'select',
            required: true,
            placeholder: 'Select transaction type',
            options: [
              { value: 'pay-in', label: 'Pay-In' },
              { value: 'pay-out', label: 'Pay-Out' },
              { value: 'party-transfer', label: 'Party Transfer' }
            ]
          }
        ]
      },
      {
        title: 'Party Selection',
        icon: 'people-outline',
        fields: this.getPartyFields()
      },
      {
        title: 'Transaction Details',
        icon: 'card-outline',
        fields: [
          {
            name: 'amount',
            label: 'Amount',
            type: 'number',
            required: true,
            placeholder: 'Enter amount',
            min: 0.01,
            step: 0.01,
            helpText: 'Enter the transaction amount'
          },
          {
            name: 'discount_amount',
            label: 'Discount Amount',
            type: 'number',
            required: true,
            placeholder: 'Enter discount amount',
            min: 0,
            step: 0.01,
            helpText: 'Enter any discount applied to this transaction'
          },
          {
            name: 'remarks',
            label: 'Remarks',
            type: 'textarea',
            required: true,
            placeholder: 'Enter transaction remarks',
            rows: 3,
            helpText: 'Provide details about this transaction'
          },
          {
            name: 'entry_type',
            label: 'Entry Type',
            type: 'select',
            required: true,
            placeholder: 'Select entry type',
            options: [
              { value: 'debit', label: 'Debit' },
              { value: 'credit', label: 'Credit' }
            ]
          }
        ]
      },
      {
        title: 'Payment Information',
        icon: 'card-outline',
        fields: [
          {
            name: 'mode_of_payment',
            label: 'Mode of Payment',
            type: 'select',
            required: true,
            placeholder: 'Select payment mode',
            options: this.modeOfPaymentOptions.map(option => ({
              value: option.slug,
              label: option.name
            }))
          }
        ]
      },
      {
        title: 'Transaction Image',
        icon: 'camera-outline',
        fields: [
          {
            name: 'transaction_image',
            label: 'Transaction Image',
            type: 'file',
            required: true,
            placeholder: 'Select transaction image',
            accept: 'image/*',
            helpText: 'Upload a photo or image of the transaction receipt'
          }
        ]
      }
    ];
  }

  getPartyFields(): FormField[] {
    switch (this.activeSegment) {
      case 'pay-in':
        return [
          {
            name: 'buyer',
            label: 'Select Buyer',
            type: 'select',
            required: true,
            placeholder: 'Choose a buyer',
            options: this.buyerOptions.map(option => ({
              value: option.id,
              label: option.name
            }))
          }
        ];
      case 'pay-out':
        return [
          {
            name: 'supplier',
            label: 'Select Supplier',
            type: 'select',
            required: true,
            placeholder: 'Choose a supplier',
            options: this.supplierOptions.map(option => ({
              value: option.id,
              label: option.name
            }))
          }
        ];
      case 'party-transfer':
        return [
          {
            name: 'from_party',
            label: 'From Party',
            type: 'select',
            required: true,
            placeholder: 'Select from party',
            options: this.partyOptions.map(option => ({
              value: option.id,
              label: option.name
            }))
          },
          {
            name: 'to_party',
            label: 'To Party',
            type: 'select',
            required: true,
            placeholder: 'Select to party',
            options: this.partyOptions.map(option => ({
              value: option.id,
              label: option.name
            }))
          }
        ];
      default:
        return [];
    }
  }

  onTransactionTypeChange(event: any) {
    this.activeSegment = event.detail.value;
    this.buildFormSections();
  }

  onFormSubmit(formData: any) {
    const submitData = {
      ...formData,
      activeSegment: this.activeSegment,
      selectedFile: this.selectedFile
    };
    this.formSubmit.emit(submitData);
  }

  onFormCancel() {
    this.closeModal();
  }

  onFileSelected(event: any) {
    const file = event.target.files[0];
    if (file) {
      this.selectedFile = file;
      this.createPreviewUrl(file);
    }
  }

  createPreviewUrl(file: File) {
    const reader = new FileReader();
    reader.onload = (e: any) => {
      this.previewUrl = e.target.result;
    };
    reader.readAsDataURL(file);
  }

  clearImageSelection() {
    this.selectedFile = null;
    this.previewUrl = null;
  }

  closeModal() {
    this.isOpen = false;
    this.modalClosed.emit();
    this.resetForm();
  }

  resetForm() {
    this.activeSegment = 'pay-in';
    this.selectedBuyerOption = null;
    this.selectedSupplierOption = null;
    this.selectedFromOption = null;
    this.selectedToOption = null;
    this.modeOfPaymentSelectedOption = null;
    this.selectedFile = null;
    this.previewUrl = null;
    this.buildFormSections();
  }

  getLedgerName(type: string): string {
    return this.ledgerFieldName[type] || type;
  }

  // Configuration objects for dropdowns
  buyerConfig = {
    displayKey: 'name',
    search: true,
    height: '300px',
    placeholder: 'Select a buyer',
    customComparator: () => 0,
    limitTo: 5,
    moreText: 'more',
    noResultsFound: 'No results found!',
    searchPlaceholder: 'Search...',
  };

  supplierConfig = {
    displayKey: 'name',
    search: true,
    height: '300px',
    placeholder: 'Select a supplier',
    customComparator: () => 0,
    limitTo: 5,
    moreText: 'more',
    noResultsFound: 'No results found!',
    searchPlaceholder: 'Search...',
  };

  fromPartyConfig = {
    displayKey: 'name',
    search: true,
    height: '300px',
    placeholder: 'Select a from party',
    customComparator: () => 0,
    limitTo: 5,
    moreText: 'more',
    noResultsFound: 'No results found!',
    searchPlaceholder: 'Search...',
  };

  toPartyConfig = {
    displayKey: 'name',
    search: true,
    height: '300px',
    placeholder: 'Select a to party',
    customComparator: () => 0,
    limitTo: 5,
    moreText: 'more',
    noResultsFound: 'No results found!',
    searchPlaceholder: 'Search...',
  };

  modeOfPaymentConfig = {
    displayKey: 'name',
    search: true,
    height: '300px',
    placeholder: 'Select a Mode of payment',
    customComparator: () => 0,
    limitTo: 5,
    moreText: 'more',
    noResultsFound: 'No results found!',
    searchPlaceholder: 'Search...',
  };

  // Event handlers for dropdowns
  onBuyerChange(event: any) {
    this.selectedBuyerOption = event;
  }

  onSupplierChange(event: any) {
    this.selectedSupplierOption = event;
  }

  onFromChange(event: any) {
    this.selectedFromOption = event;
  }

  onToChange(event: any) {
    this.selectedToOption = event;
  }

  onModeOfPaymentOptionChange(event: any) {
    this.modeOfPaymentSelectedOption = event;
  }

  onButtonClick(button: ModalButton) {
    if (button.type === 'submit') {
      // Handle form submission
      this.onFormSubmit({});
    } else if (button.handler) {
      button.handler();
    }
  }
} 