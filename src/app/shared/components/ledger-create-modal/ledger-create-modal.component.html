<app-standard-modal
  [config]="modalConfig"
  [isOpen]="isOpen"
  (modalClosed)="modalClosed.emit()"
  (buttonClicked)="onButtonClick($event)">
  
  <!-- Transaction Type Selection -->
  <div class="transaction-type-section">
    <ion-segment 
      [(ngModel)]="activeSegment" 
      (ionChange)="onTransactionTypeChange($event)"
      class="transaction-segment">
      <ion-segment-button value="pay-in">
        <ion-icon name="arrow-down-circle-outline" slot="start"></ion-icon>
        <ion-label>Pay-In</ion-label>
      </ion-segment-button>
      <ion-segment-button value="pay-out">
        <ion-icon name="arrow-up-circle-outline" slot="start"></ion-icon>
        <ion-label>Pay-Out</ion-label>
      </ion-segment-button>
      <ion-segment-button value="party-transfer">
        <ion-icon name="swap-horizontal-outline" slot="start"></ion-icon>
        <ion-label>Party Transfer</ion-label>
      </ion-segment-button>
    </ion-segment>
  </div>

  <!-- Party Selection Section -->
  <div class="party-selection-section">
    <div class="section-header">
      <h3 class="section-title">
        <ion-icon name="people-outline" class="section-icon"></ion-icon>
        Party Selection
      </h3>
    </div>

    <!-- Pay-In Party Selection -->
    <div *ngIf="activeSegment === 'pay-in'" class="party-options">
      <ion-item class="form-item">
        <ion-label position="stacked">
          Select Buyer
          <ion-text color="danger">*</ion-text>
        </ion-label>
        <ngx-select-dropdown 
          [config]="buyerConfig" 
          [options]="buyerOptions" 
          [(ngModel)]="selectedBuyerOption"
          (change)="onBuyerChange($event)"
          class="party-select">
        </ngx-select-dropdown>
      </ion-item>
    </div>

    <!-- Pay-Out Party Selection -->
    <div *ngIf="activeSegment === 'pay-out'" class="party-options">
      <ion-item class="form-item">
        <ion-label position="stacked">
          Select Supplier
          <ion-text color="danger">*</ion-text>
        </ion-label>
        <ngx-select-dropdown 
          [config]="supplierConfig" 
          [options]="supplierOptions"
          [(ngModel)]="selectedSupplierOption" 
          (change)="onSupplierChange($event)"
          class="party-select">
        </ngx-select-dropdown>
      </ion-item>
    </div>

    <!-- Party Transfer Selection -->
    <div *ngIf="activeSegment === 'party-transfer'" class="party-options">
      <ion-item class="form-item">
        <ion-label position="stacked">
          From Party
          <ion-text color="danger">*</ion-text>
        </ion-label>
        <ngx-select-dropdown 
          [config]="fromPartyConfig" 
          [options]="partyOptions" 
          [(ngModel)]="selectedFromOption"
          (change)="onFromChange($event)"
          class="party-select">
        </ngx-select-dropdown>
      </ion-item>

      <ion-item class="form-item">
        <ion-label position="stacked">
          To Party
          <ion-text color="danger">*</ion-text>
        </ion-label>
        <ngx-select-dropdown 
          [config]="toPartyConfig" 
          [options]="partyOptions" 
          [(ngModel)]="selectedToOption"
          (change)="onToChange($event)"
          class="party-select">
        </ngx-select-dropdown>
      </ion-item>
    </div>
  </div>

  <!-- Payment Mode Selection -->
  <div *ngIf="activeSegment !== 'party-transfer'" class="payment-mode-section">
    <div class="section-header">
      <h3 class="section-title">
        <ion-icon name="card-outline" class="section-icon"></ion-icon>
        Payment Mode
      </h3>
    </div>

    <ion-item class="form-item">
      <ion-label position="stacked">
        Mode of Payment
        <ion-text color="danger">*</ion-text>
      </ion-label>
      <ngx-select-dropdown 
        [config]="modeOfPaymentConfig" 
        [options]="modeOfPaymentOptions"
        [(ngModel)]="modeOfPaymentSelectedOption" 
        (change)="onModeOfPaymentOptionChange($event)"
        class="payment-select">
      </ngx-select-dropdown>
    </ion-item>
  </div>

  <!-- Transaction Form -->
  <app-standard-form
    [sections]="formSections"
    [loading]="loading"
    [disabled]="loading"
    submitButtonText="Create Entry"
    cancelButtonText="Cancel"
    submitButtonColor="primary"
    cancelButtonColor="medium"
    submitButtonIcon="checkmark-outline"
    cancelButtonIcon="close-outline"
    (formSubmit)="onFormSubmit($event)"
    (formCancel)="onFormCancel()">
  </app-standard-form>

  <!-- Image Preview -->
  <div *ngIf="previewUrl" class="image-preview-section">
    <div class="section-header">
      <h3 class="section-title">
        <ion-icon name="image-outline" class="section-icon"></ion-icon>
        Transaction Image Preview
      </h3>
    </div>

    <div class="image-preview">
      <img [src]="previewUrl" alt="Transaction preview" class="preview-image">
      <ion-button 
        fill="clear" 
        size="small" 
        (click)="clearImageSelection()"
        class="remove-preview-btn">
        <ion-icon name="close-circle" slot="icon-only"></ion-icon>
      </ion-button>
    </div>
  </div>
</app-standard-modal> 