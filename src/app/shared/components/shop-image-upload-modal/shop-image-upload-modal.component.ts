import { Component, Input, OnInit, ViewChild, ElementRef } from '@angular/core';
import { Modal<PERSON>ontroller, ActionSheetController, AlertController } from '@ionic/angular';
import { RouteBillingService } from '../../services/route-billing.service';
import { ToastService } from '../../services/toast.service';
import { IonLoaderService } from '../../services/ion-loader.service';
import { environment } from 'src/environments/environment';
import { FullScreenImageViewerComponent } from '../full-screen-image-viewer/full-screen-image-viewer.component';

export interface ShopImageData {
  id: number;
  name: string;
  route_id: number;
  route_name?: string;
  place?: string;
  phone_no?: string;
}

export interface ShopPhoto {
  id: number;
  photo: string;
  date_taken: string;
  time_taken: string;
  notes: string;
  uploaded_by_name: string;
  location_latitude?: number;
  location_longitude?: number;
}

@Component({
  selector: 'app-shop-image-upload-modal',
  templateUrl: './shop-image-upload-modal.component.html',
  styleUrls: ['./shop-image-upload-modal.component.scss'],
})
export class ShopImageUploadModalComponent implements OnInit {
  @Input() shop!: ShopImageData;
  @ViewChild('fileInput', { static: false }) fileInput!: ElementRef<HTMLInputElement>;
  @ViewChild('cameraInput', { static: false }) cameraInput!: ElementRef<HTMLInputElement>;

  photos: ShopPhoto[] = [];
  loading = true;
  uploading = false;
  selectedFile: File | null = null;
  previewUrl: string | null = null;
  notes = '';
  
  // Camera/file options
  captureMode: 'camera' | 'gallery' = 'camera';

  constructor(
    private modalController: ModalController,
    private actionSheetController: ActionSheetController,
    private alertController: AlertController,
    private routeBillingService: RouteBillingService,
    private toast: ToastService,
    private ionLoaderService: IonLoaderService
  ) { }

  async ngOnInit() {
    await this.loadShopPhotos();
  }

  async closeModal() {
    await this.modalController.dismiss();
  }

  async loadShopPhotos() {
    try {
      this.loading = true;
      const response = await this.routeBillingService.getFreezerPhotos({
        buyer_id: this.shop.id
      });

      if (response.success) {
        // Filter out photos older than 30 days
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

        this.photos = response.data.filter((photo: ShopPhoto) => {
          const photoDate = new Date(photo.date_taken);
          return photoDate >= thirtyDaysAgo;
        });
      }
    } catch (error) {
      console.error('Error loading shop photos:', error);
      this.toast.toastServices('Error loading photos', 'danger', 'top');
    } finally {
      this.loading = false;
    }
  }

  async presentImageOptions() {
    const actionSheet = await this.actionSheetController.create({
      header: 'Add Photo',
      cssClass: 'image-options-action-sheet',
      buttons: [
        {
          text: 'Take Photo',
          icon: 'camera-outline',
          handler: () => {
            this.openCamera();
          }
        },
        {
          text: 'Choose from Gallery',
          icon: 'images-outline',
          handler: () => {
            this.openGallery();
          }
        },
        {
          text: 'Cancel',
          icon: 'close-outline',
          role: 'cancel'
        }
      ]
    });

    await actionSheet.present();
  }

  openCamera() {
    this.captureMode = 'camera';
    if (this.cameraInput) {
      this.cameraInput.nativeElement.click();
    }
  }

  openGallery() {
    this.captureMode = 'gallery';
    if (this.fileInput) {
      this.fileInput.nativeElement.click();
    }
  }

  onFileSelected(event: any) {
    const file: File = event.target.files[0];
    if (file) {
      this.handleFileSelection(file);
    }
  }

  handleFileSelection(file: File) {
    // Validate file type
    if (!file.type.startsWith('image/')) {
      this.toast.toastServices('Please select a valid image file', 'danger', 'top');
      return;
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      this.toast.toastServices('Image size should not exceed 10MB', 'danger', 'top');
      return;
    }

    this.selectedFile = file;
    
    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      this.previewUrl = e.target?.result as string;
    };
    reader.readAsDataURL(file);

    this.toast.toastServices('Image selected successfully', 'success', 'top');
  }

  clearSelection() {
    this.selectedFile = null;
    this.previewUrl = null;
    this.notes = '';
    
    // Reset file inputs
    if (this.fileInput) {
      this.fileInput.nativeElement.value = '';
    }
    if (this.cameraInput) {
      this.cameraInput.nativeElement.value = '';
    }
  }

  async uploadImage() {
    if (!this.selectedFile) {
      this.toast.toastServices('Please select an image first', 'danger', 'top');
      return;
    }

    try {
      this.uploading = true;
      await this.ionLoaderService.startLoader();

      // Get current location if available
      let location = null;
      try {
        location = await this.routeBillingService.getCurrentLocation();
      } catch (error) {
        console.log('Location not available:', error);
      }

      const response = await this.routeBillingService.uploadFreezerPhoto(
        this.shop.id,
        this.shop.route_id,
        this.selectedFile,
        this.notes || `Photo for ${this.shop.name}`,
        location
      );

      if (response.success) {
        this.toast.toastServices('Image uploaded successfully', 'success', 'top');
        this.clearSelection();
        await this.loadShopPhotos();
      } else {
        this.toast.toastServices('Failed to upload image', 'danger', 'top');
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      this.toast.toastServices('Error uploading image', 'danger', 'top');
    } finally {
      this.uploading = false;
      this.ionLoaderService.dismissLoader();
    }
  }

  async deletePhoto(photo: ShopPhoto) {
    const alert = await this.alertController.create({
      header: 'Delete Photo',
      message: 'Are you sure you want to delete this photo?',
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Delete',
          role: 'destructive',
          handler: async () => {
            await this.confirmDeletePhoto(photo);
          }
        }
      ]
    });

    await alert.present();
  }

  async confirmDeletePhoto(photo: ShopPhoto) {
    try {
      await this.ionLoaderService.startLoader();
      
      const response = await this.routeBillingService.deleteFreezerPhoto(photo.id);
      
      if (response.success) {
        this.toast.toastServices('Photo deleted successfully', 'success', 'top');
        await this.loadShopPhotos();
      } else {
        this.toast.toastServices('Failed to delete photo', 'danger', 'top');
      }
    } catch (error) {
      console.error('Error deleting photo:', error);
      this.toast.toastServices('Error deleting photo', 'danger', 'top');
    } finally {
      this.ionLoaderService.dismissLoader();
    }
  }

  formatDate(dateStr: string): string {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    });
  }

  formatTime(timeStr: string): string {
    const time = new Date(`2000-01-01T${timeStr}`);
    return time.toLocaleTimeString('en-IN', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  }

  getImageUrl(photo: ShopPhoto): string {
    // Assuming the photo.photo contains the relative path
    return photo.photo.startsWith('http') ? photo.photo : `${environment.apiUrlClean}${photo.photo}`;
  }

  async viewFullImage(photo: ShopPhoto) {
    const modal = await this.modalController.create({
      component: FullScreenImageViewerComponent,
      componentProps: {
        image: photo
      },
      cssClass: 'full-screen-image-modal',
      backdropDismiss: true,
      showBackdrop: true
    });

    await modal.present();
  }

  openInBrowser(photo: ShopPhoto) {
    const url = this.getImageUrl(photo);
    window.open(url, '_blank');
  }

  trackByPhotoId(_index: number, photo: ShopPhoto): number {
    return photo.id;
  }
}
