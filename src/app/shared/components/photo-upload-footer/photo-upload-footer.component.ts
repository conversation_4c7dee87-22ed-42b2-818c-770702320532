import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { RouteBillingService } from '../../services/route-billing.service';
import { BuyerService } from '../../services/buyer.service';
import { ShopImageUploadModalComponent, ShopImageData } from '../shop-image-upload-modal/shop-image-upload-modal.component';
import { ToastService } from '../../services/toast.service';

export interface ShopPhotoStatus {
  id: number;
  name: string;
  route_id: number;
  route_name?: string;
  place?: string;
  phone_no?: string;
  has_photos: boolean;
  photo_count: number;
  last_photo_date?: string;
  has_today_photos: boolean;
  today_photo_count: number;
}

@Component({
  selector: 'app-photo-upload-footer',
  templateUrl: './photo-upload-footer.component.html',
  styleUrls: ['./photo-upload-footer.component.scss'],
})
export class PhotoUploadFooterComponent implements OnInit, OnChanges {
  @Input() date: string = '';
  @Input() routeSummaries: any[] = [];

  isExpanded = false;
  loading = false;
  
  // Photo statistics
  totalShops = 0;
  shopsWithPhotos = 0;
  shopsPending = 0;
  shopsWithTodayPhotos = 0;
  
  // Shops data
  allShops: ShopPhotoStatus[] = [];
  pendingShops: ShopPhotoStatus[] = [];
  
  constructor(
    private modalController: ModalController,
    private routeBillingService: RouteBillingService,
    private buyerService: BuyerService,
    private toast: ToastService
  ) { }

  ngOnInit() {
    this.loadPhotoStatistics();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['date'] || changes['routeSummaries']) {
      this.loadPhotoStatistics();
    }
  }

  async loadPhotoStatistics() {
    try {
      this.loading = true;

      // Get shops for today's routes only (from route summaries)
      const todaysShops = await this.getTodaysShops();
      this.totalShops = todaysShops.length;

      if (this.totalShops === 0) {
        this.allShops = [];
        this.pendingShops = [];
        this.shopsWithPhotos = 0;
        this.shopsWithTodayPhotos = 0;
        this.shopsPending = 0;
        return;
      }

      // Get photo data for today's shops using existing API
      const photoResponse: any = await this.routeBillingService.getFreezerPhotos({
        date: this.date
      });

      // Create a map of shop photos for quick lookup
      const shopPhotosMap = new Map();
      const today = new Date().toISOString().split('T')[0];

      if (photoResponse.success) {
        photoResponse.data.forEach((photo: any) => {
          const buyerId = photo.buyer || photo.buyer_id;
          const photoDate = photo.date_taken;

          // Skip photos older than 30 days
          const photoDateObj = new Date(photoDate);
          const thirtyDaysAgo = new Date();
          thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

          if (photoDateObj < thirtyDaysAgo) {
            return; // Skip old photos
          }

          if (!shopPhotosMap.has(buyerId)) {
            shopPhotosMap.set(buyerId, {
              count: 0,
              lastDate: null,
              todayCount: 0
            });
          }

          const existing = shopPhotosMap.get(buyerId);
          existing.count++;

          // Check if photo is from today
          if (photoDate === today) {
            existing.todayCount++;
          }

          if (!existing.lastDate || photoDate > existing.lastDate) {
            existing.lastDate = photoDate;
          }
        });
      }

      // Update shops with photo information
      this.allShops = todaysShops.map(shop => {
        const photoData = shopPhotosMap.get(shop.id);
        return {
          ...shop,
          has_photos: photoData ? photoData.count > 0 : false,
          photo_count: photoData ? photoData.count : 0,
          last_photo_date: photoData ? photoData.lastDate : null,
          has_today_photos: photoData ? photoData.todayCount > 0 : false,
          today_photo_count: photoData ? photoData.todayCount : 0
        } as ShopPhotoStatus;
      });

      // Calculate statistics
      this.shopsWithPhotos = this.allShops.filter(shop => shop.has_photos).length;
      this.shopsWithTodayPhotos = this.allShops.filter(shop => shop.has_today_photos).length;
      this.shopsPending = this.totalShops - this.shopsWithTodayPhotos; // Pending = shops without today's photos
      this.pendingShops = this.allShops.filter(shop => !shop.has_today_photos);

    } catch (error) {
      console.error('Error loading photo statistics:', error);
      this.toast.toastServices('Error loading photo statistics', 'danger', 'top');
    } finally {
      this.loading = false;
    }
  }

  async getTodaysShops(): Promise<ShopPhotoStatus[]> {
    try {
      const todaysShops: ShopPhotoStatus[] = [];

      // Get shops from today's route summaries using existing unbilled shops data
      for (const routeSummary of this.routeSummaries) {
        try {
          // Get unbilled shops for this route (this already includes all shops for the route)
          const unbilledResponse: any = await this.routeBillingService.getUnbilledShops(routeSummary.route, this.date);

          if (unbilledResponse.success) {
            unbilledResponse.data.forEach((shop: any) => {
              todaysShops.push({
                id: shop.id,
                name: shop.name,
                route_id: routeSummary.route,
                route_name: routeSummary.route_name,
                place: shop.place,
                phone_no: shop.phone_no,
                has_photos: false, // Will be updated later
                photo_count: 0,
                last_photo_date: null,
                has_today_photos: false, // Will be updated later
                today_photo_count: 0
              });
            });
          }

          // Also get billed shops by checking the route summary data
          // We can estimate billed shops from total_shops - unbilled_shops
          const billedCount = routeSummary.billed_shops;
          if (billedCount > 0) {
            // For now, we'll focus on unbilled shops as they are the primary target for photos
            // Billed shops can be added later if needed
          }
        } catch (error) {
          console.error(`Error loading shops for route ${routeSummary.route_name}:`, error);
        }
      }

      // Remove duplicates based on shop ID
      const uniqueShops = todaysShops.filter((shop, index, self) =>
        index === self.findIndex(s => s.id === shop.id)
      );

      return uniqueShops;
    } catch (error) {
      console.error('Error getting today\'s shops:', error);
      return [];
    }
  }

  toggleExpanded() {
    this.isExpanded = !this.isExpanded;
  }

  async openShopImageModal(shop: ShopPhotoStatus) {
    const shopImageData: ShopImageData = {
      id: shop.id,
      name: shop.name,
      route_id: shop.route_id,
      route_name: shop.route_name,
      place: shop.place,
      phone_no: shop.phone_no
    };

    const modal = await this.modalController.create({
      component: ShopImageUploadModalComponent,
      componentProps: {
        shop: shopImageData
      },
      cssClass: 'shop-image-upload-modal'
    });

    await modal.present();

    // Refresh statistics after modal closes
    const { data } = await modal.onWillDismiss();
    if (data) {
      await this.loadPhotoStatistics();
    }
  }

  getCompletionPercentage(): number {
    if (this.totalShops === 0) return 0;
    return Math.round((this.shopsWithTodayPhotos / this.totalShops) * 100);
  }

  getCompletionPercentageForCSS(): number {
    const percentage = this.getCompletionPercentage();
    return Math.round(percentage / 10) * 10; // Round to nearest 10
  }

  getCompletionColor(): string {
    const percentage = this.getCompletionPercentage();
    if (percentage >= 80) return 'success';
    if (percentage >= 50) return 'warning';
    return 'danger';
  }

  formatLastPhotoDate(dateStr: string | null): string {
    if (!dateStr) return 'No photos';
    
    const date = new Date(dateStr);
    const today = new Date();
    const diffTime = today.getTime() - date.getTime();
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
    return `${Math.floor(diffDays / 30)} months ago`;
  }

  trackByShopId(index: number, shop: ShopPhotoStatus): number {
    return shop.id;
  }

  async refreshStatistics() {
    await this.loadPhotoStatistics();
    this.toast.toastServices('Photo statistics refreshed', 'success', 'top');
  }
}
