import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';

export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'number' | 'email' | 'tel' | 'date' | 'time' | 'datetime-local' | 'password' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'file';
  required?: boolean;
  placeholder?: string;
  options?: FormFieldOption[];
  validation?: any[];
  disabled?: boolean;
  readonly?: boolean;
  min?: number;
  max?: number;
  step?: number;
  rows?: number;
  accept?: string;
  multiple?: boolean;
  icon?: string;
  helpText?: string;
  errorMessage?: string;
}

export interface FormFieldOption {
  value: any;
  label: string;
  disabled?: boolean;
}

export interface FormSection {
  title: string;
  subtitle?: string;
  icon?: string;
  fields: FormField[];
  collapsible?: boolean;
  collapsed?: boolean;
}

@Component({
  selector: 'app-standard-form',
  templateUrl: './standard-form.component.html',
  styleUrls: ['./standard-form.component.scss']
})
export class StandardFormComponent implements OnInit {
  @Input() sections: FormSection[] = [];
  @Input() loading: boolean = false;
  @Input() disabled: boolean = false;
  @Input() submitButtonText: string = 'Submit';
  @Input() cancelButtonText: string = 'Cancel';
  @Input() showCancelButton: boolean = true;
  @Input() submitButtonColor: string = 'primary';
  @Input() cancelButtonColor: string = 'medium';
  @Input() submitButtonIcon: string = 'checkmark-outline';
  @Input() cancelButtonIcon: string = 'close-outline';

  @Output() formSubmit = new EventEmitter<any>();
  @Output() formCancel = new EventEmitter<void>();
  @Output() formChange = new EventEmitter<any>();

  form: FormGroup;
  fieldErrors: { [key: string]: string } = {};

  constructor(private fb: FormBuilder) {
    this.form = this.fb.group({});
  }

  ngOnInit() {
    this.buildForm();
  }

  buildForm() {
    const formGroup: { [key: string]: any } = {};
    
    this.sections.forEach(section => {
      section.fields.forEach(field => {
        const validators = [];
        
        if (field.required) {
          validators.push(Validators.required);
        }
        
        if (field.validation) {
          validators.push(...field.validation);
        }
        
        formGroup[field.name] = [{
          value: '',
          disabled: field.disabled || this.disabled
        }, validators];
      });
    });
    
    this.form = this.fb.group(formGroup);
    
    // Listen for form changes
    this.form.valueChanges.subscribe(value => {
      this.formChange.emit(value);
      this.validateForm();
    });
  }

  validateForm() {
    this.fieldErrors = {};
    
    Object.keys(this.form.controls).forEach(fieldName => {
      const control = this.form.get(fieldName);
      if (control && control.errors) {
        this.fieldErrors[fieldName] = this.getErrorMessage(fieldName, control.errors);
      }
    });
  }

  getErrorMessage(fieldName: string, errors: any): string {
    const field = this.getFieldByName(fieldName);
    
    if (errors.required) {
      return field?.errorMessage || `${field?.label || fieldName} is required`;
    }
    
    if (errors.email) {
      return 'Please enter a valid email address';
    }
    
    if (errors.minlength) {
      return `Minimum length is ${errors.minlength.requiredLength} characters`;
    }
    
    if (errors.maxlength) {
      return `Maximum length is ${errors.maxlength.requiredLength} characters`;
    }
    
    if (errors.min) {
      return `Minimum value is ${errors.min.min}`;
    }
    
    if (errors.max) {
      return `Maximum value is ${errors.max.max}`;
    }
    
    if (errors.pattern) {
      return 'Please enter a valid value';
    }
    
    return 'Invalid value';
  }

  getFieldByName(fieldName: string): FormField | undefined {
    for (const section of this.sections) {
      const field = section.fields.find(f => f.name === fieldName);
      if (field) return field;
    }
    return undefined;
  }

  onSubmit() {
    if (this.form.valid && !this.loading && !this.disabled) {
      this.formSubmit.emit(this.form.value);
    } else {
      this.validateForm();
    }
  }

  onCancel() {
    this.formCancel.emit();
  }

  toggleSection(section: FormSection) {
    if (section.collapsible) {
      section.collapsed = !section.collapsed;
    }
  }

  isFieldInvalid(fieldName: string): boolean {
    const control = this.form.get(fieldName);
    return !!(control && control.invalid && (control.dirty || control.touched));
  }

  getFieldValue(fieldName: string): any {
    return this.form.get(fieldName)?.value;
  }

  setFieldValue(fieldName: string, value: any) {
    this.form.get(fieldName)?.setValue(value);
  }

  resetForm() {
    this.form.reset();
    this.fieldErrors = {};
  }

  trackBySection(index: number, section: FormSection): string {
    return section.title;
  }

  trackByField(index: number, field: FormField): string {
    return field.name;
  }

  onFileSelected(event: any, fieldName: string) {
    const files = event.target.files;
    if (files && files.length > 0) {
      this.setFieldValue(fieldName, files);
    }
  }
} 