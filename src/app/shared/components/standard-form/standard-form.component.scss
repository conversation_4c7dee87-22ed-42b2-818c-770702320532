// Standard Form Component Styles
.standard-form {
  --form-spacing: 20px;
  --field-spacing: 16px;
  --border-radius: 8px;
  --border-color: #e9ecef;
  --focus-color: var(--ion-color-primary);
  --error-color: var(--ion-color-danger);
  --success-color: var(--ion-color-success);
  --warning-color: var(--ion-color-warning);
  --text-color: #495057;
  --label-color: #6c757d;
  --background-color: #ffffff;
  --disabled-color: #adb5bd;
  --transition: all 0.2s ease;

  // Form Sections
  .form-sections {
    display: flex;
    flex-direction: column;
    gap: var(--form-spacing);
  }

  .form-section {
    background: var(--background-color);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    overflow: hidden;
    transition: var(--transition);

    &.collapsible {
      .section-header {
        cursor: pointer;
        
        &:hover {
          background: #f8f9fa;
        }
      }
    }

    &.collapsed {
      .section-content {
        display: none;
      }
    }
  }

  // Section Header
  .section-header {
    padding: 16px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid var(--border-color);

    .section-title {
      display: flex;
      align-items: center;
      gap: 12px;
      margin: 0;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--text-color);
        flex: 1;
      }

      .section-icon {
        font-size: 18px;
        color: var(--focus-color);
      }

      .collapse-icon {
        font-size: 16px;
        color: var(--label-color);
        transition: var(--transition);
      }
    }

    .section-subtitle {
      margin: 8px 0 0 0;
      font-size: 14px;
      color: var(--label-color);
      line-height: 1.4;
    }
  }

  // Section Content
  .section-content {
    padding: 20px;

    &.hidden {
      display: none;
    }
  }

  // Form Fields
  .form-field {
    margin-bottom: var(--field-spacing);
    position: relative;

    &:last-child {
      margin-bottom: 0;
    }

    &.invalid {
      .field-input,
      .field-textarea,
      .field-select {
        --border-color: var(--error-color);
        --highlight-color: var(--error-color);
      }
    }

    &.disabled {
      opacity: 0.6;
      pointer-events: none;
    }
  }

  // Field Labels
  .field-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--label-color);
    margin-bottom: 8px;
    display: block;

    ion-text {
      margin-left: 4px;
    }
  }

  // Input Fields
  .field-input,
  .field-textarea,
  .field-select {
    --border-radius: var(--border-radius);
    --border-width: 1px;
    --border-color: var(--border-color);
    --border-style: solid;
    --padding-start: 12px;
    --padding-end: 12px;
    --padding-top: 12px;
    --padding-bottom: 12px;
    --background: var(--background-color);
    --color: var(--text-color);
    --placeholder-color: #adb5bd;
    --highlight-color: var(--focus-color);
    font-size: 14px;
    transition: var(--transition);

    &:focus-within {
      --border-color: var(--focus-color);
      --highlight-color: var(--focus-color);
      box-shadow: 0 0 0 2px rgba(var(--ion-color-primary-rgb), 0.1);
    }

    &[disabled] {
      --background: #f8f9fa;
      --color: var(--disabled-color);
      cursor: not-allowed;
    }
  }

  .field-textarea {
    --padding-top: 16px;
    --padding-bottom: 16px;
    min-height: 80px;
  }

  .field-select {
    --padding-start: 16px;
    --padding-end: 16px;
  }

  // Checkbox and Radio
  .field-checkbox,
  .field-radio-group {
    margin-top: 8px;

    ion-label {
      font-size: 14px;
      color: var(--text-color);
      margin-left: 8px;
    }
  }

  .field-radio-group {
    ion-item {
      --padding-start: 0;
      --padding-end: 0;
      --inner-padding-end: 0;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  // File Input
  .file-input-container {
    .file-select-button {
      --border-radius: var(--border-radius);
      --border-width: 1px;
      --border-color: var(--border-color);
      --border-style: solid;
      --padding-start: 16px;
      --padding-end: 16px;
      --padding-top: 12px;
      --padding-bottom: 12px;
      font-size: 14px;
      font-weight: 500;
      height: 48px;

      &:hover {
        --border-color: var(--focus-color);
        --color: var(--focus-color);
      }
    }
  }

  // Help Text
  .field-help {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
    padding: 8px 12px;
    background: #e3f2fd;
    border-radius: 6px;
    border-left: 3px solid var(--focus-color);

    ion-icon {
      font-size: 16px;
      color: var(--focus-color);
      flex-shrink: 0;
    }

    span {
      font-size: 12px;
      color: #1976d2;
      line-height: 1.4;
    }
  }

  // Error Messages
  .field-error {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
    padding: 8px 12px;
    background: #ffebee;
    border-radius: 6px;
    border-left: 3px solid var(--error-color);

    ion-icon {
      font-size: 16px;
      color: var(--error-color);
      flex-shrink: 0;
    }

    span {
      font-size: 12px;
      color: #c62828;
      line-height: 1.4;
      font-weight: 500;
    }
  }

  // Form Actions
  .form-actions {
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);

    .cancel-button,
    .submit-button {
      --border-radius: var(--border-radius);
      --padding-start: 20px;
      --padding-end: 20px;
      --padding-top: 12px;
      --padding-bottom: 12px;
      font-size: 14px;
      font-weight: 500;
      height: 48px;
      text-transform: none;
      letter-spacing: 0.25px;

      ion-icon {
        font-size: 16px;
      }

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      &:active {
        transform: translateY(0);
      }

      &[disabled] {
        opacity: 0.6;
        transform: none;
        box-shadow: none;
      }
    }

    .submit-button {
      --box-shadow: 0 2px 8px rgba(var(--ion-color-primary-rgb), 0.3);

      &:hover {
        --box-shadow: 0 4px 16px rgba(var(--ion-color-primary-rgb), 0.4);
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .standard-form {
    --form-spacing: 16px;
    --field-spacing: 12px;

    .section-header {
      padding: 12px 16px;

      .section-title {
        h3 {
          font-size: 15px;
        }
      }
    }

    .section-content {
      padding: 16px;
    }

    .form-actions {
      margin-top: 20px;
      padding-top: 16px;

      .cancel-button,
      .submit-button {
        --padding-start: 16px;
        --padding-end: 16px;
        height: 44px;
        font-size: 13px;
      }
    }
  }
}

@media (max-width: 480px) {
  .standard-form {
    --form-spacing: 12px;
    --field-spacing: 10px;

    .section-header {
      padding: 10px 12px;

      .section-title {
        h3 {
          font-size: 14px;
        }

        .section-icon {
          font-size: 16px;
        }
      }
    }

    .section-content {
      padding: 12px;
    }

    .field-input,
    .field-textarea,
    .field-select {
      --padding-start: 10px;
      --padding-end: 10px;
      --padding-top: 10px;
      --padding-bottom: 10px;
      font-size: 13px;
    }

    .form-actions {
      margin-top: 16px;
      padding-top: 12px;

      .cancel-button,
      .submit-button {
        --padding-start: 12px;
        --padding-end: 12px;
        height: 40px;
        font-size: 12px;
      }
    }
  }
}

// Accessibility improvements
.standard-form {
  // Focus management
  .field-input:focus-within,
  .field-textarea:focus-within,
  .field-select:focus-within {
    outline: 2px solid var(--focus-color);
    outline-offset: 2px;
  }

  // High contrast mode support
  @media (prefers-contrast: high) {
    --border-color: #000000;
    --text-color: #000000;
    --label-color: #000000;

    .section-header {
      background: #ffffff;
      border-bottom: 2px solid #000000;
    }

    .form-actions {
      border-top: 2px solid #000000;
    }
  }

  // Reduced motion support
  @media (prefers-reduced-motion: reduce) {
    --transition: none;

    .cancel-button:hover,
    .submit-button:hover {
      transform: none;
    }
  }
} 