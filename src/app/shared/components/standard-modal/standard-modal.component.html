<ion-modal 
  [isOpen]="isOpen" 
  [backdropDismiss]="config.backdropDismiss"
  [cssClass]="'standard-modal ' + getModalSizeClass() + ' ' + getModalColorClass() + ' ' + (config.cssClass || '')"
  (didDismiss)="closeModal()">
  
  <ng-template>
    <!-- Modal Header -->
    <ion-header translucent>
      <ion-toolbar [color]="config.color">
        <ion-title class="modal-title">
          <div class="title-content">
            <span class="title-text">{{ config.title }}</span>
            <span class="subtitle-text" *ngIf="config.subtitle">{{ config.subtitle }}</span>
          </div>
        </ion-title>
        <ion-buttons slot="end" *ngIf="config.showCloseButton">
          <ion-button 
            (click)="closeModal()" 
            fill="clear" 
            color="light"
            class="close-button"
            aria-label="Close modal">
            <ion-icon name="close-outline" slot="icon-only"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <!-- Modal Content -->
    <ion-content class="modal-content">
      <!-- Loading State -->
      <div class="modal-loading" *ngIf="config.loading">
        <ion-spinner name="crescent"></ion-spinner>
        <p>Loading...</p>
      </div>

      <!-- Main Content -->
      <div class="modal-body" [class.loading]="config.loading">
        <ng-content></ng-content>
      </div>

      <!-- Modal Footer with Action Buttons -->
      <div class="modal-footer" *ngIf="config.buttons && config.buttons.length > 0">
        <div class="button-container">
          <ion-button
            *ngFor="let button of config.buttons"
            [color]="button.color || 'medium'"
            [fill]="button.fill || 'solid'"
            [size]="button.size || 'default'"
            [disabled]="button.disabled || config.disabled"
            [type]="button.type || 'button'"
            (click)="onButtonClick(button)"
            class="modal-button">
            <ion-icon 
              *ngIf="button.icon && button.iconPosition !== 'end'" 
              [name]="button.icon" 
              slot="start">
            </ion-icon>
            <span>{{ button.text }}</span>
            <ion-icon 
              *ngIf="button.icon && button.iconPosition === 'end'" 
              [name]="button.icon" 
              slot="end">
            </ion-icon>
          </ion-button>
        </div>
      </div>
    </ion-content>
  </ng-template>
</ion-modal> 