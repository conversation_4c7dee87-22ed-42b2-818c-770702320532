import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';

export interface ModalConfig {
  title: string;
  subtitle?: string;
  size?: 'small' | 'medium' | 'large' | 'fullscreen';
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'medium';
  showCloseButton?: boolean;
  showBackdrop?: boolean;
  backdropDismiss?: boolean;
  cssClass?: string;
  buttons?: ModalButton[];
  loading?: boolean;
  disabled?: boolean;
}

export interface ModalButton {
  text: string;
  color?: string;
  fill?: 'clear' | 'outline' | 'solid';
  size?: 'small' | 'default' | 'large';
  icon?: string;
  iconPosition?: 'start' | 'end';
  disabled?: boolean;
  handler?: () => void | Promise<void>;
  type?: 'button' | 'submit' | 'reset';
}

@Component({
  selector: 'app-standard-modal',
  templateUrl: './standard-modal.component.html',
  styleUrls: ['./standard-modal.component.scss']
})
export class StandardModalComponent implements OnInit {
  @Input() config: ModalConfig = {
    title: 'Modal',
    size: 'medium',
    color: 'primary',
    showCloseButton: true,
    showBackdrop: true,
    backdropDismiss: true
  };

  @Input() isOpen: boolean = false;
  @Output() modalClosed = new EventEmitter<void>();
  @Output() buttonClicked = new EventEmitter<ModalButton>();

  constructor(private modalController: ModalController) {}

  ngOnInit() {
    // Set default values if not provided
    if (!this.config.size) this.config.size = 'medium';
    if (!this.config.color) this.config.color = 'primary';
    if (this.config.showCloseButton === undefined) this.config.showCloseButton = true;
    if (this.config.showBackdrop === undefined) this.config.showBackdrop = true;
    if (this.config.backdropDismiss === undefined) this.config.backdropDismiss = true;
  }

  closeModal() {
    this.isOpen = false;
    this.modalClosed.emit();
  }

  onButtonClick(button: ModalButton) {
    if (button.handler) {
      button.handler();
    }
    this.buttonClicked.emit(button);
  }

  onBackdropClick() {
    if (this.config.backdropDismiss) {
      this.closeModal();
    }
  }

  getModalSizeClass(): string {
    return `modal-size-${this.config.size}`;
  }

  getModalColorClass(): string {
    return `modal-color-${this.config.color}`;
  }
} 