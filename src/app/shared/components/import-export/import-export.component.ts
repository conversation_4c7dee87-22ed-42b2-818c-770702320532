import { Component, Input, OnInit } from '@angular/core';
import { ToastService } from '../../services/toast.service';
import { ImportExportService } from '../../services/import-export.service';

@Component({
  selector: 'app-import-export',
  templateUrl: './import-export.component.html',
  styleUrls: ['./import-export.component.scss'],
})
export class ImportExportComponent implements OnInit {
  selectedFile: File;
  @Input() appLabel: string = '';
  @Input() modelName: string = '';
  
  constructor(
    private importExportService: ImportExportService,
    private toast: ToastService,

  ) { }

  ngOnInit() { }
  onFileSelected(event: any) {
    const file: File = event.target.files[0];
    if (file) {
      // Validate file type
      if (!file.name.toLowerCase().endsWith('.csv')) {
        this.toast.toastServices('Please select a valid CSV file.', "danger", "bottom");
        return;
      }

      // Validate file size (max 10MB)
      const maxSize = 10 * 1024 * 1024; // 10MB in bytes
      if (file.size > maxSize) {
        this.toast.toastServices('File size should not exceed 10MB.', "danger", "bottom");
        return;
      }

      this.selectedFile = file;
      this.toast.toastServices(`File "${file.name}" selected successfully.`, "success", "bottom");
    }
  }

  // Upload CSV file
  async uploadCsv() {
    if (!this.selectedFile) {
      this.toast.toastServices('Please select a CSV file.', "danger", "bottom");
      return;
    }

    this.toast.toastServices('Uploading CSV file...', "primary", "bottom");

    this.importExportService.importCsv(this.appLabel, this.modelName, this.selectedFile)
      .subscribe({
        next: (res) => {
          this.toast.toastServices(`CSV imported successfully! ${res.message || ''}`, "success", "bottom");
          this.selectedFile = null;
          // Reset file input
          const fileInput = document.getElementById('csvFileInput') as HTMLInputElement;
          if (fileInput) {
            fileInput.value = '';
          }
        },
        error: (err) => {
          const errorMessage = err.error?.message || err.message || 'Import failed';
          this.toast.toastServices(`Import Error: ${errorMessage}`, "danger", "bottom");
        }
      });
  }

  // Download CSV file
  async downloadCsv() {
    this.toast.toastServices('Preparing CSV download...', "primary", "bottom");

    this.importExportService.exportCsv(this.appLabel, this.modelName).subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        const timestamp = new Date().toISOString().slice(0, 10); // YYYY-MM-DD format
        a.download = `${this.modelName}_${timestamp}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url); // Clean up memory
        this.toast.toastServices(`CSV downloaded successfully as ${this.modelName}_${timestamp}.csv`, "success", "bottom");
      },
      error: (err) => {
        const errorMessage = err.error?.message || err.message || 'Download failed';
        this.toast.toastServices(`Download Error: ${errorMessage}`, "danger", "bottom");
      }
    });
  }
}
