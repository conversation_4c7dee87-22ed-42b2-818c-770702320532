.import-export-container {
  margin: 16px 0;
  padding: 0 8px;

  .import-export-card {
    margin: 0;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    background: var(--ion-color-light);
    border: 1px solid var(--ion-color-light-shade);

    .card-header {
      padding: 16px 20px 8px 20px;
      background: linear-gradient(135deg, var(--ion-color-primary) 0%, var(--ion-color-primary-shade) 100%);
      color: var(--ion-text-color, white);
      border-radius: 12px 12px 0 0;

      .card-title {
        font-size: 18px;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 8px;

        .title-icon {
          font-size: 20px;
        }
      }

      .card-subtitle {
        font-size: 14px;
        margin: 4px 0 0 0;
        opacity: 0.9;
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .card-content {
      padding: 20px;
      background: var(--card-background, white);
      border-radius: 0 0 12px 12px;

      .action-grid {
        padding: 0;

        .action-row {
          margin: 0;

          .upload-section, .download-section {
            padding: 8px;
            display: flex;
            flex-direction: column;
            gap: 12px;

            .file-input-wrapper {
              position: relative;

              .file-input {
                position: absolute;
                opacity: 0;
                width: 0;
                height: 0;
                pointer-events: none;
              }

              .file-select-btn {
                --border-radius: 8px;
                --border-width: 2px;
                --border-style: dashed;
                --border-color: var(--ion-color-medium);
                --color: var(--ion-color-medium-shade);
                --background: var(--ion-color-light-tint);
                height: 48px;
                font-weight: 500;
                transition: all 0.3s ease;

                &:hover {
                  --border-color: var(--ion-color-primary);
                  --color: var(--ion-color-primary);
                  --background: var(--ion-color-primary-tint);
                }

                ion-icon {
                  margin-right: 8px;
                }
              }
            }

            .action-btn {
              --border-radius: 8px;
              height: 48px;
              font-weight: 600;
              font-size: 16px;
              transition: all 0.3s ease;

              ion-icon {
                margin-right: 8px;
                font-size: 18px;
              }

              &.upload-btn {
                --background: var(--ion-color-success);
                --background-hover: var(--ion-color-success-shade);
                --box-shadow: 0 2px 6px rgba(var(--ion-color-success-rgb), 0.3);

                &:hover {
                  transform: translateY(-1px);
                  --box-shadow: 0 4px 12px rgba(var(--ion-color-success-rgb), 0.4);
                }

                &:disabled {
                  --background: var(--ion-color-medium);
                  --color: var(--ion-color-medium-contrast);
                  transform: none;
                  --box-shadow: none;
                }
              }

              &.download-btn {
                --background: var(--ion-color-primary);
                --background-hover: var(--ion-color-primary-shade);
                --box-shadow: 0 2px 6px rgba(var(--ion-color-primary-rgb), 0.3);

                &:hover {
                  transform: translateY(-1px);
                  --box-shadow: 0 4px 12px rgba(var(--ion-color-primary-rgb), 0.4);
                }
              }
            }

            .download-info {
              display: flex;
              align-items: center;
              gap: 12px;
              padding: 12px;
              background: var(--ion-color-light-tint);
              border-radius: 8px;
              border-left: 4px solid var(--ion-color-primary);

              .download-icon {
                font-size: 24px;
                color: var(--ion-color-primary);
                flex-shrink: 0;
              }

              .download-text {
                margin: 0;
                font-size: 14px;
                color: var(--ion-color-dark);
                line-height: 1.4;
              }
            }
          }
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .import-export-container {
    margin: 12px 0;
    padding: 0 4px;

    .import-export-card {
      .card-header {
        padding: 12px 16px 6px 16px;

        .card-title {
          font-size: 16px;
        }

        .card-subtitle {
          font-size: 13px;
        }
      }

      .card-content {
        padding: 16px;

        .action-grid .action-row {
          .upload-section, .download-section {
            padding: 4px;
            gap: 10px;

            .action-btn {
              height: 44px;
              font-size: 15px;
            }

            .download-info {
              padding: 10px;

              .download-icon {
                font-size: 20px;
              }

              .download-text {
                font-size: 13px;
              }
            }
          }
        }
      }
    }
  }
}