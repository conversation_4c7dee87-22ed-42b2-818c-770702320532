<div class="import-export-container">
  <ion-card class="import-export-card">
    <ion-card-header class="card-header">
      <ion-card-title class="card-title">
        <ion-icon name="cloud-upload-outline" class="title-icon"></ion-icon>
        Data Management
      </ion-card-title>
      <ion-card-subtitle class="card-subtitle">
        Import or export your data using CSV files
      </ion-card-subtitle>
    </ion-card-header>

    <ion-card-content class="card-content">
      <ion-grid class="action-grid">
        <ion-row class="action-row">
          <!-- File Upload Section -->
          <ion-col size="12" size-md="6" class="upload-section">
            <div class="file-input-wrapper">
              <input
                #fileInput
                type="file"
                (change)="onFileSelected($event)"
                accept=".csv"
                class="file-input"
                id="csvFileInput">
              <ion-button
                fill="outline"
                expand="block"
                class="file-select-btn"
                (click)="fileInput.click()">
                <ion-icon name="document-outline" slot="start"></ion-icon>
                {{selectedFile ? selectedFile.name : 'Choose CSV File'}}
              </ion-button>
            </div>
            <ion-button
              expand="block"
              fill="solid"
              color="success"
              class="action-btn upload-btn"
              [disabled]="!selectedFile"
              (click)="uploadCsv()">
              <ion-icon name="cloud-upload" slot="start"></ion-icon>
              Upload CSV
            </ion-button>
          </ion-col>

          <!-- Download Section -->
          <ion-col size="12" size-md="6" class="download-section">
            <div class="download-info">
              <ion-icon name="download-outline" class="download-icon"></ion-icon>
              <p class="download-text">Export current data to CSV format</p>
            </div>
            <ion-button
              expand="block"
              fill="solid"
              color="primary"
              class="action-btn download-btn"
              (click)="downloadCsv()">
              <ion-icon name="cloud-download" slot="start"></ion-icon>
              Download CSV
            </ion-button>
          </ion-col>
        </ion-row>
      </ion-grid>
    </ion-card-content>
  </ion-card>
</div>