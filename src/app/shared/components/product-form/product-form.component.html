<!-- Product Form Modal -->
<ion-modal [isOpen]="isOpen" class="product-form-modal">
  <ng-template>
    <ion-header translucent>
      <ion-toolbar [color]="mode === 'edit' ? 'warning' : 'primary'">
        <ion-title>{{ mode === 'create' ? 'Add New Product' : 'Edit Product' }}</ion-title>
        <ion-buttons slot="end">
          <ion-button (click)="closeModal()" fill="clear" color="light">
            <ion-icon name="close-outline"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>
    <ion-content class="ion-padding modal-content">
      <form #form="ngForm" (ngSubmit)="onSubmit(form.value)">
        <!-- Basic Information Section -->
        <div class="form-section">
          <h3 class="section-title">
            <ion-icon name="information-circle-outline" class="section-icon"></ion-icon>
            Basic Information
          </h3>

          <ion-item class="form-item">
            <ion-label position="stacked">Name
              <ion-text color="danger">*</ion-text>
            </ion-label>
            <ion-input
              [ngModel]="mode === 'edit' ? productData?.name : ''"
              type="text"
              name="name"
              required="true"
              placeholder="Enter product name">
            </ion-input>
          </ion-item>

          <ion-item class="form-item">
            <ion-label position="stacked">Short Code
              <ion-text color="danger">*</ion-text>
            </ion-label>
            <ion-input
              [ngModel]="mode === 'edit' ? productData?.short_code : ''"
              type="text"
              name="short_code"
              required="true"
              placeholder="Enter short code">
            </ion-input>
          </ion-item>

          <ion-item class="form-item">
            <ion-label for="amount">Brand</ion-label>
            <ionic-selectable
              itemValueField="id"
              itemTextField="name"
              [items]="brandData"
              [ngModel]="mode === 'edit' ? productData?.brand : null"
              name="brand"
              [canSearch]="true"
              [canAddItem]="true"
              (onAddItem)="addBrand($event)"
              placeholder="Select or add a brand">
            </ionic-selectable>
          </ion-item>

          <ion-item class="form-item">
            <ion-label position="stacked">HSN Code</ion-label>
            <ion-input
              [ngModel]="mode === 'edit' ? productData?.hsn_code : ''"
              type="text"
              name="hsn_code"
              placeholder="Enter HSN code">
            </ion-input>
          </ion-item>

          <ion-item class="form-item">
            <ion-label position="stacked">Sort Order</ion-label>
            <ion-input
              [ngModel]="mode === 'edit' ? productData?.sort_order : ''"
              type="number"
              name="sort_order"
              required="false"
              placeholder="Enter sort order">
            </ion-input>
          </ion-item>
        </div>

        <!-- Pricing Information Section -->
        <div class="form-section">
          <h3 class="section-title">
            <ion-icon name="pricetag-outline" class="section-icon"></ion-icon>
            Pricing Information
          </h3>

          <ion-item class="form-item">
            <ion-label position="stacked">MRP
              <ion-text color="danger">*</ion-text>
            </ion-label>
            <ion-input
              type="number"
              name="mrp"
              [ngModel]="mrp"
              (ngModelChange)="updateMrp($event)"
              [required]="marginalItem"
              placeholder="0.00">
            </ion-input>
          </ion-item>

          <ion-row>
            <ion-col size="6">
              <ion-item class="form-item">
                <ion-label position="stacked">PR Rate
                  <ion-text color="danger">*</ion-text>
                </ion-label>
                <ion-input
                  type="number"
                  name="pr_rate"
                  [ngModel]="calculatedPrRate"
                  (ngModelChange)="calculatePrMargin($event)"
                  [required]="marginalItem"
                  placeholder="0.00">
                </ion-input>
              </ion-item>
            </ion-col>
            <ion-col size="6">
              <ion-item class="form-item">
                <ion-label position="stacked">PR Margin
                  <ion-text color="danger">*</ion-text>
                </ion-label>
                <ion-input
                  type="number"
                  name="pr_margin"
                  [ngModel]="calculatedPrMargin"
                  (ngModelChange)="calculatePrRate($event)"
                  [required]="marginalItem"
                  placeholder="0.00">
                </ion-input>
              </ion-item>
            </ion-col>
          </ion-row>

          <ion-row>
            <ion-col size="6">
              <ion-item class="form-item">
                <ion-label position="stacked">Selling Rate
                  <ion-text color="danger">*</ion-text>
                </ion-label>
                <ion-input
                  type="number"
                  name="rate"
                  [ngModel]="calculatedSellingRate"
                  (ngModelChange)="calculateSellingMargin($event)"
                  [required]="marginalItem"
                  placeholder="0.00">
                </ion-input>
              </ion-item>
            </ion-col>
            <ion-col size="6">
              <ion-item class="form-item">
                <ion-label position="stacked">Selling Margin
                  <ion-text color="danger">*</ion-text>
                </ion-label>
                <ion-input
                  type="number"
                  name="margin"
                  [ngModel]="calculatedSellingMargin"
                  (ngModelChange)="calculateSellingRate($event)"
                  [required]="marginalItem"
                  placeholder="0.00">
                </ion-input>
              </ion-item>
            </ion-col>
          </ion-row>

          <ion-row>
            <ion-col size="6">
              <ion-item class="form-item">
                <ion-label position="stacked">Tax Rate
                  <ion-text color="danger">*</ion-text>
                </ion-label>
                <ion-input
                  [ngModel]="mode === 'edit' ? productData?.tax_rate : ''"
                  type="number"
                  name="tax_rate"
                  [required]="marginalItem"
                  placeholder="0.00">
                </ion-input>
              </ion-item>
            </ion-col>
            <ion-col size="6" class="ion-align-self-end">
              <ion-button
                expand="block"
                fill="outline"
                (click)="openPurchaseCalculator()"
                class="calculator-button">
                <ion-icon name="calculator-outline" slot="start"></ion-icon>
                Calculate PR Rate
              </ion-button>
            </ion-col>
          </ion-row>
        </div>

        <!-- Product Configuration Section -->
        <div class="form-section">
          <h3 class="section-title">
            <ion-icon name="settings-outline" class="section-icon"></ion-icon>
            Product Configuration
          </h3>

          <ion-item class="form-item">
            <ion-label position="stacked">Unit Contents
              <ion-text color="danger">*</ion-text>
            </ion-label>
            <ion-input
              [ngModel]="mode === 'edit' ? productData?.unit_contains : ''"
              type="number"
              name="unit_contains"
              [required]="marginalItem"
              placeholder="1">
            </ion-input>
          </ion-item>

          <ion-item class="form-item">
            <ion-label>Is Crate Based Product</ion-label>
            <ion-toggle
              [(ngModel)]="isCrateBased"
              name="is_crate_based"
              (ngModelChange)="onCrateBasedToggle($event)"
              slot="end">
            </ion-toggle>
          </ion-item>

          <ion-item class="form-item" *ngIf="isCrateBased">
            <ion-label position="stacked">Crate Size
              <ion-text color="danger">*</ion-text>
            </ion-label>
            <ion-input
              [(ngModel)]="crateSize"
              type="number"
              name="crate_size"
              (ngModelChange)="updateCrateSize($event)"
              min="1"
              [required]="isCrateBased"
              placeholder="1">
            </ion-input>
          </ion-item>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
          <ion-button
            type="submit"
            [disabled]="form.invalid"
            expand="block"
            fill="solid"
            shape="round"
            [color]="mode === 'edit' ? 'warning' : 'primary'"
            class="submit-button">
            <ion-icon [name]="mode === 'create' ? 'add-outline' : 'create-outline'" slot="start"></ion-icon>
            {{ mode === 'create' ? 'Add Product' : 'Update Product' }}
          </ion-button>
        </div>
      </form>
    </ion-content>
  </ng-template>
</ion-modal>

<!-- Purchase Rate Calculator Modal -->
<ion-modal [isOpen]="isPurchaseCalculatorOpen" class="purchase-calculator-modal">
  <ng-template>
    <ion-header translucent>
      <ion-toolbar color="primary">
        <ion-title>Purchase Rate Calculator</ion-title>
        <ion-buttons slot="end">
          <ion-button (click)="closePurchaseCalculator()" fill="clear" color="light">
            <ion-icon name="close-outline"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>
    <ion-content class="ion-padding calculator-content">
      <div class="calculator-header">
        <ion-icon name="calculator-outline" class="header-icon"></ion-icon>
        <h2 class="calculator-title">Calculate Purchase Rate</h2>
        <p class="calculator-subtitle">Enter purchase details to calculate the optimal rate</p>
      </div>

      <!-- Input Fields Section -->
      <div class="form-section">
        <h3 class="section-title">
          <ion-icon name="create-outline" class="section-icon"></ion-icon>
          Input Fields
        </h3>

        <ion-row>
          <ion-col size="12">
            <ion-item class="form-item">
              <ion-label position="stacked">Total Purchase Amount
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input
                type="number"
                [(ngModel)]="calculatorTotalPurchaseAmount"
                (ngModelChange)="onCalculatorFieldChange()"
                placeholder="0.00">
              </ion-input>
            </ion-item>
          </ion-col>
        </ion-row>

        <ion-row>
          <ion-col size="6">
            <ion-item class="form-item">
              <ion-label position="stacked">Order Quantity
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input
                type="number"
                [(ngModel)]="calculatorOrderQuantity"
                (ngModelChange)="onCalculatorFieldChange()"
                placeholder="0">
              </ion-input>
            </ion-item>
          </ion-col>
          <ion-col size="6">
            <ion-item class="form-item">
              <ion-label position="stacked">Unit Contains
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input
                type="number"
                [(ngModel)]="calculatorUnitContains"
                (ngModelChange)="onCalculatorFieldChange()"
                placeholder="0">
              </ion-input>
            </ion-item>
          </ion-col>
        </ion-row>

        <ion-row>
          <ion-col size="12">
            <ion-item class="form-item">
              <ion-label position="stacked">Crate Contains
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input
                type="number"
                [(ngModel)]="calculatorCrateContains"
                (ngModelChange)="onCalculatorFieldChange()"
                placeholder="0">
              </ion-input>
            </ion-item>
          </ion-col>
        </ion-row>

        <!-- GST Toggle -->
        <ion-item class="form-item">
          <ion-label>Include GST in Calculation</ion-label>
          <ion-toggle
            [(ngModel)]="calculatorGstEnabled"
            (ngModelChange)="onCalculatorFieldChange()"
            slot="end">
          </ion-toggle>
        </ion-item>

        <ion-row *ngIf="calculatorGstEnabled">
          <ion-col size="12">
            <ion-item class="form-item">
              <ion-label position="stacked">GST Rate (%)
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input
                type="number"
                [(ngModel)]="calculatorGstRate"
                (ngModelChange)="onCalculatorFieldChange()"
                placeholder="18">
              </ion-input>
            </ion-item>
          </ion-col>
        </ion-row>
      </div>

      <!-- Calculated Results Section -->
      <div class="results-section" *ngIf="calculatorPrRate">
        <h3 class="section-title">
          <ion-icon name="checkmark-circle-outline" class="section-icon"></ion-icon>
          Calculated Results
        </h3>

        <div class="result-item">
          <span class="result-label">Basic Purchase Amount:</span>
          <span class="result-value">₹{{ calculatorBasicPurchase?.toFixed(2) || '0.00' }}</span>
        </div>

        <div class="result-item" *ngIf="calculatorGstEnabled">
          <span class="result-label">Tax Amount:</span>
          <span class="result-value">₹{{ calculatorTaxAmount?.toFixed(2) || '0.00' }}</span>
        </div>

        <div class="result-item">
          <span class="result-label">Net Purchase Cost:</span>
          <span class="result-value">₹{{ calculatorNetPurchaseCost?.toFixed(2) || '0.00' }}</span>
        </div>

        <div class="result-item primary-result">
          <span class="result-label">Calculated PR Rate:</span>
          <span class="result-value">₹{{ calculatorPrRate?.toFixed(2) || '0.00' }}</span>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="action-buttons">
        <ion-button 
          expand="block" 
          fill="solid" 
          (click)="applyCalculatedRate()"
          [disabled]="!calculatorPrRate"
          class="apply-button">
          <ion-icon name="checkmark-outline" slot="start"></ion-icon>
          Apply Calculated Rate
        </ion-button>
      </div>
    </ion-content>
  </ng-template>
</ion-modal>
