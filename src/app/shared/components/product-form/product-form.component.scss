/* Product Form Component Styling */

/* Product Form Modal Styling */
.product-form-modal {
  --width: 90%;
  --max-width: 600px;
  --height: 100vh;
  --max-height: 90%;
  --border-radius: 16px;
}

.modal-content {
  --background: var(--content-background, #f8f9fa);
}

.form-section {
  background: var(--card-background, white);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e0e0e0;
}

.section-icon {
  font-size: 18px;
  margin-right: 8px;
  color: var(--ion-color-primary);
}

.form-item {
  --background: transparent;
  --border-radius: 8px;
  --padding-start: 0;
  --padding-end: 0;
  margin-bottom: 16px;
}

.form-item ion-label {
  font-weight: 500;
  color: #333;
}

.form-item ion-input {
  --background: #f5f5f5;
  --border-radius: 8px;
  --padding-start: 12px;
  --padding-end: 12px;
  margin-top: 8px;
}

.form-item ion-toggle {
  --background: var(--ion-color-step-200, #e0e0e0);
  --background-checked: var(--ion-color-primary);
  --handle-background: var(--ion-background-color, white);
  --handle-background-checked: var(--ion-background-color, white);
}

.form-actions {
  margin-top: 24px;
  padding: 0 8px;
}

.submit-button {
  --border-radius: 12px;
  --padding-top: 16px;
  --padding-bottom: 16px;
  font-weight: 600;
  font-size: 16px;
}

.calculator-button {
  --color: var(--ion-color-primary);
  --border-color: var(--ion-color-primary);
  --border-radius: 8px;
  --padding-start: 12px;
  --padding-end: 12px;
  height: 36px;
  font-size: 12px;
  margin-top: 8px;
}

/* Purchase Rate Calculator Modal Styling */
.purchase-calculator-modal {
  --width: 95%;
  --max-width: 600px;
  --height: 100vh;
  --max-height: 90%;
  --border-radius: 16px;
}

.calculator-content {
  --background: #f8f9fa;
}

/* Calculator Header */
.calculator-header {
  text-align: center;
  margin-bottom: 24px;
  padding: 16px;
  background: var(--card-background, white);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-icon {
  font-size: 48px;
  color: var(--ion-color-primary);
  margin-bottom: 8px;
}

.calculator-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 8px 0 4px 0;
}

.calculator-subtitle {
  font-size: 14px;
  color: #666;
  margin: 0;
}

/* Form Sections */
.form-section {
  background: var(--card-background, white);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e9ecef;
}

.section-icon {
  font-size: 20px;
  color: var(--ion-color-primary);
  margin-right: 8px;
}

.form-item {
  margin-bottom: 8px;
  --background: #f8f9fa;
  --border-radius: 8px;
  --padding-start: 12px;
  --padding-end: 12px;
}

.form-item ion-label {
  font-weight: 500;
  color: #495057;
}

.form-item ion-input {
  --color: #212529;
  --placeholder-color: #6c757d;
}

/* Results Section */
.results-section {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}

.result-item:last-child {
  border-bottom: none;
}

.result-label {
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

.result-value {
  font-weight: 600;
  color: #212529;
  font-size: 16px;
}

.primary-result {
  background: rgba(255, 255, 255, 0.4);
  border-radius: 8px;
  padding: 16px 12px;
  margin-top: 8px;
  border: none;
}

.primary-result .result-label {
  font-size: 16px;
  font-weight: 600;
  color: var(--ion-color-primary);
}

.primary-result .result-value {
  font-size: 20px;
  font-weight: 700;
  color: var(--ion-color-primary);
}

/* Action Buttons */
.action-buttons {
  padding: 16px 0;
}

.apply-button {
  --background: var(--ion-color-success);
  --color: white;
  --border-radius: 12px;
  height: 48px;
  font-weight: 600;
  font-size: 16px;
}

.apply-button:hover {
  --background: var(--ion-color-success-shade);
}

.apply-button[disabled] {
  --background: #6c757d;
  --color: #adb5bd;
}

/* Responsive Design for Product Form Modal */
@media (max-width: 768px) {
  .product-form-modal {
    --width: 95%;
    --max-width: none;
    --height: 100vh;
    --max-height: 95%;
  }

  .form-section {
    padding: 12px;
    margin-bottom: 12px;
  }

  .section-title {
    font-size: 14px;
  }

  .section-icon {
    font-size: 16px;
  }

  .form-item {
    margin-bottom: 12px;
  }

  .submit-button {
    --padding-top: 14px;
    --padding-bottom: 14px;
    font-size: 14px;
  }

  .calculator-button {
    font-size: 11px;
    height: 32px;
  }
}

/* Responsive Design for Purchase Calculator */
@media (max-width: 768px) {
  .purchase-calculator-modal {
    --width: 98%;
    --max-height: 95%;
  }

  .calculator-header {
    padding: 12px;
    margin-bottom: 16px;
  }

  .calculator-title {
    font-size: 20px;
  }

  .header-icon {
    font-size: 40px;
  }

  .results-section {
    padding: 16px;
    margin-bottom: 16px;
  }
}

/* Animation for smooth transitions */
.form-item,
.result-item {
  transition: all 0.3s ease;
}

.form-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Input focus styling */
.form-item ion-input {
  --highlight-color-focused: var(--ion-color-primary);
}

/* Toggle styling */
ion-toggle {
  --background: var(--ion-color-step-200, #e9ecef);
  --background-checked: var(--ion-color-primary);
  --handle-background: var(--ion-background-color, white);
  --handle-background-checked: var(--ion-background-color, white);
}

.product-form-container {
  --background: var(--ion-background-color);
}

.form-title {
  color: var(--ion-text-color);
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 4px;
}

.form-subtitle {
  color: var(--ion-text-color-step-600);
  font-size: 14px;
  margin-bottom: 4px;
}
