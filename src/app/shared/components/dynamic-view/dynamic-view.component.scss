/* Profile Completion Styles */
.profile-completion-header {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid var(--ion-color-primary);
}

.completion-summary {
  margin-bottom: 12px;
}

.completion-badge-large {
  font-size: 12px;
  font-weight: 600;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 6px;

  ion-icon {
    font-size: 16px;
  }
}

.completion-details {
  margin-top: 16px;

  h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
  }
}

.missing-modules-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;

  ion-chip {
    --background: transparent;
    --color: var(--ion-color-danger);
    font-size: 12px;

    ion-icon {
      font-size: 14px;
    }
  }
}

.completion-breakdown {
  margin-top: 16px;
}

.module-status {
  margin-bottom: 12px;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border-left: 3px solid #f44336;

  h5 {
    margin: 0 0 8px 0;
    font-size: 13px;
    font-weight: 600;
    color: #f44336;
  }

  p {
    margin: 0;
    font-size: 12px;
    color: #666;
    font-style: italic;
  }

  ul {
    margin: 0;
    padding-left: 16px;

    li {
      font-size: 12px;
      color: #666;
      margin-bottom: 4px;
    }
  }
}

/* General dynamic view styles */
.text-green {
  color: #4caf50;
}

.text-red {
  color: #f44336;
}