
<ion-card *ngIf="data">
  <ion-card-header>
    <ion-card-title>{{ data.name || 'Details' }}</ion-card-title>
    <!-- Profile Completion Status for Buyers -->
    <div class="profile-completion-header" *ngIf="data.profile_completion">
      <div class="completion-summary">
        <ion-badge
          [color]="data.profile_completion.is_complete ? 'success' :
                   data.profile_completion.completion_percentage >= 66 ? 'warning' : 'danger'"
          class="completion-badge-large">
          <ion-icon name="checkmark-circle" *ngIf="data.profile_completion.is_complete"></ion-icon>
          <ion-icon name="warning" *ngIf="!data.profile_completion.is_complete && data.profile_completion.completion_percentage >= 66"></ion-icon>
          <ion-icon name="alert-circle" *ngIf="data.profile_completion.completion_percentage < 66"></ion-icon>
          Profile {{data.profile_completion.completion_percentage}}% Complete
        </ion-badge>
      </div>

      <!-- Detailed Completion Status -->
      <div class="completion-details" *ngIf="!data.profile_completion.is_complete">
        <h4>Missing Modules:</h4>
        <div class="missing-modules-list">
          <ion-chip
            *ngFor="let module of data.profile_completion.missing_modules"
            color="danger"
            outline="true">
            <ion-icon name="close-circle"></ion-icon>
            <ion-label>{{module}}</ion-label>
          </ion-chip>
        </div>

        <div class="completion-breakdown" *ngIf="data.profile_completion.details">
          <div class="module-status" *ngIf="data.profile_completion.details.basic_profile?.status === 'incomplete'">
            <h5>Basic Profile Issues:</h5>
            <ul>
              <li *ngFor="let field of data.profile_completion.details.basic_profile.missing_fields">
                {{field | titlecase}} is required
              </li>
            </ul>
          </div>

          <div class="module-status" *ngIf="data.profile_completion.details.assets?.status === 'incomplete'">
            <h5>Assets Module:</h5>
            <p>{{data.profile_completion.details.assets.message}}</p>
          </div>

          <div class="module-status" *ngIf="data.profile_completion.details.deposits?.status === 'incomplete'">
            <h5>Deposits Module:</h5>
            <p>{{data.profile_completion.details.deposits.message}}</p>
          </div>
        </div>
      </div>
    </div>
  </ion-card-header>
  <ion-card-content>
    <ion-grid>
      <ion-row *ngFor="let key of getKeys(data)">
        <ion-col size="5">
          <strong>{{ key | titlecase }}:</strong>
        </ion-col>
        <ion-col  [size]="isObject(data[key]) || isArray(data[key]) ? '12' : '7'">
          <!-- Check if value is an image file -->
          <ng-container *ngIf="key.includes('file'); else checkObject">
            <ion-thumbnail>
              <img [src]="apiUrlClean+data[key]" alt="File Preview" (click)="openImagePreview(data[key])" />
            </ion-thumbnail>
          </ng-container>

          <!-- Check if value is an object  src="{{apiUrlClean}}{{data[key]}}"-->

          <ng-template #checkObject>
            <ng-container *ngIf="isObject(data[key]); else checkArray">
              <ion-grid>
                <ion-row *ngFor="let subKey of getKeys(data[key])">
                  <ion-col size="5">
                    <strong>{{ subKey | titlecase }}:</strong>
                  </ion-col>
                  <ion-col size="7">
                    <ng-container *ngIf="subKey.includes('file'); else normalSubValue">
                      <ion-thumbnail>
                        <img [src]="apiUrlClean+data[key][subKey]" alt="File Preview" (click)="openImagePreview(data[key][subKey])" />
                      </ion-thumbnail>
                    </ng-container>
                    <ng-template #normalSubValue>
                      {{ data[key][subKey] }}
                    </ng-template>
                  </ion-col>
                </ion-row>
              </ion-grid>
            </ng-container>
          </ng-template>

          <!-- Check if value is an array -->
          <ng-template #checkArray>
            <ng-container *ngIf="isArray(data[key]); else normalValue">
              <ion-grid>
                <ion-row *ngFor="let item of data[key]; let i = index">
                  <ion-item>
                    <ion-label>Actions</ion-label>
                    <ion-icon slot="end" *ngIf="isObject(data[key]) || isArray(data[key])" (click)="edit(key,data[key][i])" name="pencil" color="primary"></ion-icon>
                    <ion-icon slot="end" *ngIf="isObject(data[key]) || isArray(data[key])" (click)="delete(key,data[key][i])" name="trash" color="danger"></ion-icon>
                  </ion-item>
                  <ion-col size="12">
                    <strong>Item {{ i + 1 }}:</strong>
                    <ion-grid>
                      <ion-row *ngFor="let subKey of getKeys(item)">
                        <ion-col size="5">
                          <strong>{{ subKey | titlecase }}:</strong>
                        </ion-col>
                        <ion-col size="7">
                          <ng-container *ngIf="subKey.includes('file'); else normalArrayValue">
                            <ion-thumbnail>
                              <img [src]="apiUrlClean+item[subKey]" alt="File Preview" (click)="openImagePreview(item[subKey])" />
                            </ion-thumbnail>
                          </ng-container>
                          <ng-template #normalArrayValue>
                            {{ item[subKey] }}
                          </ng-template>
                        </ion-col>
                      </ion-row>
                    </ion-grid>
                  </ion-col>
                </ion-row>
              </ion-grid>
            </ng-container>
          </ng-template>

          <!-- Normal value -->
          <ng-template #normalValue>
            <span *ngIf="key === 'active'">
              <strong [ngClass]="{ 'text-green': data[key], 'text-red': !data[key] }">
                {{ data[key] ? 'Active' : 'Inactive' }}
              </strong>
            </span>
            <span *ngIf="key !== 'active'">{{ data[key] }}</span>
          </ng-template>
        </ion-col>
      </ion-row>
    </ion-grid>
  </ion-card-content>
  <ion-footer class="ion-padding">
    <ion-button *ngFor="let btn of buttons" (click)="onButtonClick(btn.action)" [color]="btn.color || 'primary'" expand="full">
      {{ btn.label }}
    </ion-button>
  </ion-footer>
</ion-card>
