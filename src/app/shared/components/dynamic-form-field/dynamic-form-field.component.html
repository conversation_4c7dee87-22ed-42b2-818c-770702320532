<ng-container [formGroup]="form">
  <ion-item *ngIf="field.type === 'text'">
    <ion-label position="stacked">{{ field.label }}</ion-label>
    <ion-input type="text" [formControlName]="field.name" ngDefaultControl></ion-input>
  </ion-item>
  <ion-item *ngIf="field.type === 'number'">
    <ion-label position="stacked">{{ field.label }}</ion-label>
    <ion-input type="number" [formControlName]="field.name" ngDefaultControl></ion-input>
  </ion-item>


  <ion-item *ngIf="field.type === 'textarea'">
    <ion-label position="stacked">{{ field.label }}</ion-label>
    <ion-textarea [formControlName]="field.name" ngDefaultControl></ion-textarea>
  </ion-item>

  <!-- Toggle (Fix) -->
  <ion-item *ngIf="field.type === 'toggle'">
    <ion-label>{{ field.label }}</ion-label>
    <ion-toggle [checked]="form.controls[field.name].value"
                (ionChange)="setChecked(field.name,$event)">
    </ion-toggle>
  </ion-item>

  <!-- Checkbox (Fix) -->
  <ion-item *ngIf="field.type === 'checkbox'">
    <ion-label>{{ field.label }}</ion-label>
    <ion-checkbox [checked]="form.controls[field.name].value"
                  (ionChange)="setChecked(field.name,$event)">
    </ion-checkbox>
  </ion-item>

  <!-- Datetime Picker (Fix) -->
  <ion-item *ngIf="field.type === 'date'">
    <ion-label>{{ field.label }}</ion-label>
    <ion-datetime [value]="form.controls[field.name].value"
                  (ionChange)="setValue(field.name,$event)">
    </ion-datetime>
  </ion-item>
  <!-- <ion-item *ngIf="field.type === 'select'">
    <ion-label>{{ field.label }}</ion-label>
    <ionic-selectable
      [items]="field.options"
      itemValueField="value"
      itemTextField="label"
      [formControlName]="field.name">
    </ionic-selectable>
  </ion-item> -->

  <ion-item *ngIf="field.type === 'file'">
    <ion-label position="stacked">{{ field.label }}</ion-label>
  </ion-item>
  <ion-item *ngIf="field.type === 'file'">
    <ion-label position="stacked">{{field.label || 'File'}}
      <ion-text color="danger" *ngIf="field.required">*</ion-text>
    </ion-label>
    
    <!-- Image Preview -->
    <div class="image-preview" *ngIf="previewUrl">
      <img [src]="previewUrl" alt="File preview" class="preview-image">
      <ion-button 
        fill="clear" 
        size="small" 
        (click)="clearImageSelection()"
        class="remove-preview-btn">
        <ion-icon name="close-circle" slot="icon-only"></ion-icon>
      </ion-button>
    </div>

    <!-- Upload Controls -->
    <div class="upload-controls" *ngIf="!previewUrl">
      <ion-button 
        expand="block" 
        fill="outline" 
        class="select-image-btn"
        (click)="presentImageOptions()">
        <ion-icon name="camera-outline" slot="start"></ion-icon>
        {{field.multiple ? 'Add Images' : 'Add Image'}}
      </ion-button>
    </div>

    <!-- Hidden File Inputs -->
    <input 
      #fileInput 
      type="file" 
      accept="image/*" 
      [multiple]="field.multiple"
      (change)="onFileSelected($event)" 
      style="display: none;">
    
    <input 
      #cameraInput 
      type="file" 
      accept="image/*" 
      capture="environment" 
      [multiple]="field.multiple"
      (change)="onFileSelected($event)" 
      style="display: none;">
  </ion-item>
</ng-container>