/* Image Upload Styling */
.image-preview {
  position: relative;
  margin-top: 12px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-image {
  width: 100%;
  max-height: 200px;
  object-fit: cover;
  display: block;
}

.remove-preview-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  --background: rgba(255, 255, 255, 0.9);
  --color: #dc3545;
  --border-radius: 50%;
  width: 32px;
  height: 32px;
  margin: 0;
}

.upload-controls {
  margin-top: 12px;
}

.select-image-btn {
  --border-radius: 8px;
  height: 44px;
  --background: #f8f9fa;
  --color: var(--ion-color-primary);
  --border-color: var(--ion-color-primary);
}
