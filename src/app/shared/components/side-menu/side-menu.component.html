<ion-header>
  <ion-toolbar color="primary">
    <ion-title>
      <div class="menu-header">
        <ion-icon name="business-outline" class="app-icon"></ion-icon>
        <div class="app-info">
          <div class="app-name">{{constant.appTitle}}</div>
          <div class="user-role" *ngIf="role">{{role | titlecase}}</div>
        </div>
      </div>
    </ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-list class="menu-list">
    <!-- Main Navigation Section -->
    <ion-list-header>
      <ion-label>Main Navigation</ion-label>
    </ion-list-header>

    <ion-item button (click)="navigateAndCloseMenu('tabs/home')" class="menu-item">
      <ion-icon name="home-outline" slot="start"></ion-icon>
      <ion-label>Home</ion-label>
    </ion-item>

    <!-- Dynamic Menu Items from Metadata - Exact same logic as menu page -->
    <ng-container *ngIf="role != 'buyer'">
      <ion-list-header>
        <ion-label>Business Operations</ion-label>
      </ion-list-header>

      <ng-container *ngFor="let item of component">
        <ion-item
          button
          *ngIf="item.active && authService.checkPermission('view', item.slug)"
          (click)="navigateAndCloseMenu(item.url)"
          class="menu-item">
          <ion-icon [name]="item.icon" slot="start"></ion-icon>
          <ion-label>{{item.title}}</ion-label>
        </ion-item>
      </ng-container>
    </ng-container>

    <!-- Tab Navigation Items - Matching tabs.page.html -->
    <ion-list-header>
      <ion-label>Quick Access</ion-label>
    </ion-list-header>

    <ion-item button (click)="navigateAndCloseMenu('tabs/product')" class="menu-item">
      <ion-icon name="cube" slot="start"></ion-icon>
      <ion-label>Product</ion-label>
    </ion-item>

    <ion-item button (click)="navigateAndCloseMenu('tabs/sales-bill')" class="menu-item">
      <ion-icon name="receipt" slot="start"></ion-icon>
      <ion-label>Sales Bill</ion-label>
    </ion-item>

    <ion-item button (click)="navigateAndCloseMenu('purchase-order')" class="menu-item" *ngIf="role != 'buyer'">
      <ion-icon name="document-text" slot="start"></ion-icon>
      <ion-label>Purchase Orders</ion-label>
    </ion-item>

    <ion-item button (click)="navigateAndCloseMenu('report')" class="menu-item">
      <ion-icon name="analytics" slot="start"></ion-icon>
      <ion-label>Report</ion-label>
    </ion-item>

    <!-- Settings & Account Section -->
    <ion-list-header>
      <ion-label>Account</ion-label>
    </ion-list-header>

    <ion-item button (click)="navigateAndCloseMenu('tabs/settings')" class="menu-item">
      <ion-icon name="settings" slot="start"></ion-icon>
      <ion-label>Settings</ion-label>
    </ion-item>

    <ion-item button (click)="logout()" class="menu-item logout-item">
      <ion-icon name="log-out-outline" slot="start"></ion-icon>
      <ion-label>Logout</ion-label>
    </ion-item>
  </ion-list>
</ion-content>

<ion-footer>
  <ion-toolbar>
    <ion-title class="footer-info">
      <small>Version 0.0.1</small>
    </ion-title>
  </ion-toolbar>
</ion-footer>
