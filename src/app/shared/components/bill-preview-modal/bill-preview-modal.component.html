<ion-header>
  <ion-toolbar>
    <ion-title>Invoice Details</ion-title>
    <ion-buttons slot="start">
      <ion-button (click)="closeModal()">
        <ion-icon name="arrow-back" slot="icon-only"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-buttons slot="end">
      <ion-button (click)="viewFullInvoice()" *ngIf="invoice">
        <ion-icon name="open-outline" slot="icon-only"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="preview-content">
  <!-- Loading State -->
  <div *ngIf="loading" class="loading-container">
    <ion-spinner name="crescent"></ion-spinner>
    <p>Loading invoice details...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="error-container">
    <ion-card class="error-card">
      <ion-card-content>
        <div class="error-content">
          <ion-icon name="alert-circle-outline" class="error-icon"></ion-icon>
          <h2>Invoice Not Found</h2>
          <p>{{ error }}</p>
          <ion-button (click)="closeModal()" fill="outline">
            <ion-icon name="arrow-back" slot="start"></ion-icon>
            Go Back
          </ion-button>
        </div>
      </ion-card-content>
    </ion-card>
  </div>

  <!-- Invoice Details -->
  <div *ngIf="invoice && !loading && !error" class="invoice-container">
    <!-- Scanned Barcode Info -->
    <ion-card class="barcode-info-card">
      <ion-card-content>
        <div class="barcode-info">
          <ion-icon name="checkmark-circle" class="success-icon"></ion-icon>
          <div class="barcode-text">
            <h3>Barcode Scanned Successfully</h3>
            <p>{{ barcodeData }}</p>
          </div>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Invoice Card -->
    <ion-card class="invoice-card">
      <ion-card-content class="invoice-content">
        <div class="invoice-header">
          <!-- Invoice Icon -->
          <div class="invoice-icon">
            <ion-icon name="receipt-outline" class="receipt-icon"></ion-icon>
          </div>

          <!-- Invoice Details -->
          <div class="invoice-details">
            <h2 class="customer-name">{{ invoice.name }}</h2>

            <!-- Invoice ID and Date -->
            <div class="invoice-meta">
              <span class="invoice-id">INV-{{ invoice.id }}</span>
              <span class="invoice-date">{{ invoice.date | date:'dd/MM/yyyy' }}</span>
            </div>

            <!-- Contact Details -->
            <div class="contact-details">
              <div class="contact-item">
                <ion-icon name="call-outline" class="contact-icon"></ion-icon>
                <span>{{ invoice.phone_no }}</span>
              </div>
              <div class="contact-item">
                <ion-icon name="location-outline" class="contact-icon"></ion-icon>
                <span>{{ invoice.place }}</span>
              </div>
            </div>

            <!-- Amount Details -->
            <div class="amount-details">
              <div class="amount-row">
                <span class="amount-label">Bill Amount:</span>
                <span class="amount-value bill-amount">{{ invoice.bill_amount | currency: 'INR':'symbol':'1.0-0' }}</span>
              </div>
              <div class="amount-row">
                <span class="amount-label">Received:</span>
                <span class="amount-value received-amount">{{ invoice.received_amount | currency: 'INR':'symbol':'1.0-0' }}</span>
              </div>
              <div class="amount-row">
                <span class="amount-label">Balance:</span>
                <span class="amount-value" [ngClass]="{'balance-positive': invoice.current_balance > 0, 'balance-zero': invoice.current_balance === 0}">
                  {{ invoice.current_balance | currency: 'INR':'symbol':'1.0-0' }}
                </span>
              </div>
              <!-- Payment Status -->
              <div class="amount-row">
                <span class="amount-label">Status:</span>
                <ion-badge [color]="getPaymentStatusColor(getPaymentStatus(invoice))" class="payment-status-badge">
                  {{ getPaymentStatusText(getPaymentStatus(invoice)) }}
                </ion-badge>
              </div>
            </div>
          </div>
        </div>
      </ion-card-content>

      <!-- Action Buttons - Horizontal Layout at Bottom -->
      <div class="invoice-actions-footer">
        <!-- Payment Receipt Button - Only show if there's outstanding balance -->
        <ion-button
          *ngIf="invoice.current_balance > 0"
          fill="clear"
          size="small"
          class="action-button payment-button"
          (click)="openPaymentModal()"
          title="Add Payment Receipt">
          <ion-icon name="card-outline" slot="icon-only"></ion-icon>
        </ion-button>

        <ion-button
          fill="clear"
          size="small"
          class="action-button print-button"
          (click)="printInvoice()"
          title="Print Invoice">
          <ion-icon name="print-outline" slot="icon-only"></ion-icon>
        </ion-button>

        <ion-button
          fill="clear"
          size="small"
          class="action-button receipt-button"
          (click)="printCollectionReceipt()"
          title="Print Collection Receipt">
          <ion-icon name="receipt-outline" slot="icon-only"></ion-icon>
        </ion-button>

        <ion-button
          fill="clear"
          size="small"
          class="action-button edit-button"
          (click)="editInvoice()"
          title="Edit Invoice">
          <ion-icon name="create-outline" slot="icon-only"></ion-icon>
        </ion-button>

        <ion-button
          fill="clear"
          size="small"
          class="action-button delivery-button"
          (click)="deliveryInvoice()"
          title="Mark as Delivered">
          <ion-icon name="checkmark-circle-outline" slot="icon-only"></ion-icon>
        </ion-button>

        <ion-button
          fill="clear"
          size="small"
          class="action-button delete-button"
          (click)="deleteInvoice()"
          title="Delete Invoice">
          <ion-icon name="trash-outline" slot="icon-only"></ion-icon>
        </ion-button>
      </div>
    </ion-card>

    <!-- Quick Actions -->
    <div class="quick-actions">
      <ion-button (click)="viewFullInvoice()" expand="block" fill="solid" class="view-full-button">
        <ion-icon name="eye-outline" slot="start"></ion-icon>
        View Full Invoice
      </ion-button>
    </div>
  </div>
</ion-content>

<!-- Payment Receipt Modal -->
<app-payment-receipt-modal
  *ngIf="isPaymentModalOpen"
  [isOpen]="isPaymentModalOpen"
  [invoice]="invoice"
  (modalClosed)="closePaymentModal()"
  (paymentCreated)="onPaymentCreated($event)">
</app-payment-receipt-modal>
