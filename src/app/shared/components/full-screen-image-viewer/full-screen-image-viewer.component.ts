import { Component, Input } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { environment } from 'src/environments/environment';

export interface ImageData {
  id: number;
  photo: string;
  date_taken: string;
  time_taken: string;
  notes: string;
  uploaded_by_name: string;
  location_latitude?: number;
  location_longitude?: number;
}

@Component({
  selector: 'app-full-screen-image-viewer',
  templateUrl: './full-screen-image-viewer.component.html',
  styleUrls: ['./full-screen-image-viewer.component.scss'],
})
export class FullScreenImageViewerComponent {
  @Input() image!: ImageData;

  constructor(private modalController: ModalController) {}

  async closeModal() {
    await this.modalController.dismiss();
  }

  getImageUrl(): string {
    return this.image.photo.startsWith('http') ? this.image.photo : `${environment.apiUrlClean}${this.image.photo}`;
  }

  async openInBrowser() {
    const url = this.getImageUrl();
    window.open(url, '_blank');
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  formatTime(timeString: string): string {
    const time = new Date(`2000-01-01T${timeString}`);
    return time.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  }

  async shareImage() {
    if (navigator.share) {
      try {
        await navigator.share({
          title: this.image.notes || 'Shop Photo',
          text: `Photo taken on ${this.formatDate(this.image.date_taken)} at ${this.formatTime(this.image.time_taken)}`,
          url: this.getImageUrl()
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback: copy URL to clipboard
      const url = this.getImageUrl();
      try {
        await navigator.clipboard.writeText(url);
        // You might want to show a toast here
      } catch (error) {
        console.log('Error copying to clipboard:', error);
      }
    }
  }
} 