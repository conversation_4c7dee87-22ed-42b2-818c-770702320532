import { <PERSON><PERSON>, PipeTransform, ChangeDetectorRef, OnD<PERSON>roy } from '@angular/core';
import { TranslationService } from '../services/translation.service';
import { Subscription } from 'rxjs';

@Pipe({
  name: 'translate',
  pure: false // Make it impure so it updates when language changes
})
export class TranslatePipe implements PipeTransform, OnDestroy {
  private lastKey: string = '';
  private lastValue: string = '';
  private languageSubscription: Subscription;

  constructor(
    private translationService: TranslationService,
    private cdr: ChangeDetectorRef
  ) {
    // Subscribe to language changes
    this.languageSubscription = this.translationService.getCurrentLanguage().subscribe(() => {
      this.lastKey = ''; // Reset to force re-translation
      this.cdr.markForCheck();
    });
  }

  transform(key: string, params?: { [key: string]: any }): string {
    if (!key) {
      return '';
    }

    // Only re-translate if key changed or language changed
    if (key !== this.lastKey) {
      this.lastKey = key;
      this.lastValue = this.translationService.instant(key, params);
    }

    return this.lastValue;
  }

  ngOnDestroy() {
    if (this.languageSubscription) {
      this.languageSubscription.unsubscribe();
    }
  }
}