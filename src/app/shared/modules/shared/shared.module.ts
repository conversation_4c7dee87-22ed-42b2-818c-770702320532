import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FloatingMenuComponent } from "../../components/floating-menu/floating-menu.component";
import { HeaderComponent } from "../../components/header/header.component";
import { BackButtonComponent } from "../../components/back-button/back-button.component";
import { SideMenuComponent } from "../../components/side-menu/side-menu.component";
import { FormsModule,ReactiveFormsModule } from '@angular/forms';
import { StyleFormatterPipe } from "../../pipe/style-formatter.pipe";
import { TranslatePipe } from "../../pipes/translate.pipe";
import { SelectDropDownModule } from 'ngx-select-dropdown';
import { DynamicViewComponent } from "../../components/dynamic-view/dynamic-view.component";
import { DynamicFormComponent } from "../../components/dynamic-form/dynamic-form.component";
import { DynamicFormFieldComponent } from "../../components/dynamic-form-field/dynamic-form-field.component";
import { ImportExportComponent } from "../../components/import-export/import-export.component";
import { ImportExportModalComponent } from "../../components/import-export-modal/import-export-modal.component";
import { RetailerMarginImportExportComponent } from "../../components/retailer-margin-import-export/retailer-margin-import-export.component";
import { ProductFormComponent } from "../../components/product-form/product-form.component";
import { PaymentReceiptModalComponent } from "../../components/payment-receipt-modal/payment-receipt-modal.component";
import { BarcodeScannerModalComponent } from "../../components/barcode-scanner-modal/barcode-scanner-modal.component";
import { BillPreviewModalComponent } from "../../components/bill-preview-modal/bill-preview-modal.component";
import { UnbilledShopsPopoverComponent } from "../../components/unbilled-shops-popover/unbilled-shops-popover.component";
import { UnbilledShopsModalComponent } from "../../components/unbilled-shops-modal/unbilled-shops-modal.component";
import { ShopImageUploadModalComponent } from "../../components/shop-image-upload-modal/shop-image-upload-modal.component";
import { PhotoUploadFooterComponent } from "../../components/photo-upload-footer/photo-upload-footer.component";
import { InvoiceImageModalComponent } from "../../components/invoice-image-modal/invoice-image-modal.component";
import { FullScreenImageViewerComponent } from "../../components/full-screen-image-viewer/full-screen-image-viewer.component";
import { IonicModule } from '@ionic/angular';
import { IonicSelectableModule } from 'ionic-selectable';

@NgModule({
  declarations: [ImportExportComponent, ImportExportModalComponent, RetailerMarginImportExportComponent, DynamicFormFieldComponent, DynamicFormComponent, HeaderComponent, FloatingMenuComponent, BackButtonComponent, SideMenuComponent, DynamicViewComponent, StyleFormatterPipe, TranslatePipe, ProductFormComponent, PaymentReceiptModalComponent, BarcodeScannerModalComponent, BillPreviewModalComponent, UnbilledShopsPopoverComponent, UnbilledShopsModalComponent, ShopImageUploadModalComponent, PhotoUploadFooterComponent, InvoiceImageModalComponent, FullScreenImageViewerComponent],
  exports: [ImportExportComponent, ImportExportModalComponent, RetailerMarginImportExportComponent, DynamicFormFieldComponent, DynamicFormComponent, HeaderComponent, FloatingMenuComponent, BackButtonComponent, SideMenuComponent, DynamicViewComponent, StyleFormatterPipe, TranslatePipe, ProductFormComponent, PaymentReceiptModalComponent, BarcodeScannerModalComponent, BillPreviewModalComponent, UnbilledShopsPopoverComponent, UnbilledShopsModalComponent, ShopImageUploadModalComponent, PhotoUploadFooterComponent, InvoiceImageModalComponent, FullScreenImageViewerComponent],
  imports: [CommonModule, FormsModule,ReactiveFormsModule, SelectDropDownModule, IonicModule, IonicSelectableModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class SharedModule { }
