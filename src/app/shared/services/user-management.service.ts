import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';

// Interface for inactive buyer data
export interface InactiveBuyer {
  id: number;
  name: string;
  place: string;
  contact_person: string;
  phone_no: string;
  gst_no: string;
  current_balance: number;
  credit_limit: number;
  buyer_class: string | null;
  route: string | null;
  last_purchase_date: string | null;
  expected_frequency: string;
  days_since_last_purchase: number | null;
  total_purchases: number;
  average_monthly_purchases: number;
  total_purchase_amount: number;
  average_purchase_amount: number;
  is_purchasing: boolean;
  purchasing_status: string;
  frequency_analysis: string;
}

// Interface for buyer management response
export interface BuyerManagementResponse {
  inactive_buyers: InactiveBuyer[];
  total_inactive_count: number;
  analysis_date: string;
}

// API Response interface
interface ApiResponse {
  success: boolean;
  message?: string;
  data?: BuyerManagementResponse;
}

@Injectable({
  providedIn: 'root'
})
export class BuyerManagementService {
  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) { }

  /**
   * Get inactive buyers based on purchasing patterns
   * @returns Promise<BuyerManagementResponse | null>
   */
  async getInactiveBuyers(): Promise<BuyerManagementResponse | null> {
    try {
      const response = await this.http.get(`${this.apiUrl}/buyer-management/`).toPromise() as ApiResponse;
      if (response && response.success) {
        return response.data || null;
      }
      return null;
    } catch (error) {
      console.error('Error loading inactive buyers:', error);
      return null;
    }
  }

  /**
   * Get buyer activity status based on purchasing patterns
   * @param buyerId - The buyer ID to check
   * @returns Promise<InactiveBuyer | null>
   */
  async getBuyerActivityStatus(buyerId: number): Promise<InactiveBuyer | null> {
    try {
      const response = await this.http.get(`${this.apiUrl}/buyer-management/?buyer_id=${buyerId}`).toPromise() as ApiResponse;
      if (response && response.success && response.data?.inactive_buyers) {
        return response.data.inactive_buyers.find(buyer => buyer.id === buyerId) || null;
      }
      return null;
    } catch (error) {
      console.error('Error loading buyer activity status:', error);
      return null;
    }
  }

  /**
   * Get frequency color based on expected frequency
   * @param frequency - The expected frequency
   * @returns string - CSS color class
   */
  getFrequencyColor(frequency: string): string {
    switch (frequency) {
      case 'weekly':
        return 'success';
      case 'monthly':
        return 'primary';
      case 'quarterly':
        return 'warning';
      case 'yearly':
        return 'medium';
      default:
        return 'dark';
    }
  }

  /**
   * Get status color based on invoicing status
   * @param status - The invoicing status
   * @returns string - CSS color class
   */
  getStatusColor(status: string): string {
    if (status.includes('Active')) {
      return 'success';
    } else if (status.includes('Recently active')) {
      return 'primary';
    } else if (status.includes('may need attention')) {
      return 'warning';
    } else if (status.includes('requires immediate attention')) {
      return 'danger';
    } else {
      return 'medium';
    }
  }

  /**
   * Get priority level based on days since last purchase
   * @param daysSinceLastPurchase - Days since last purchase
   * @returns string - Priority level
   */
  getPriorityLevel(daysSinceLastPurchase: number | null): string {
    if (daysSinceLastPurchase === null) {
      return 'high';
    } else if (daysSinceLastPurchase <= 7) {
      return 'low';
    } else if (daysSinceLastPurchase <= 30) {
      return 'medium';
    } else if (daysSinceLastPurchase <= 90) {
      return 'high';
    } else {
      return 'critical';
    }
  }

  /**
   * Format frequency for display
   * @param frequency - The frequency string
   * @returns string - Formatted frequency
   */
  formatFrequency(frequency: string): string {
    switch (frequency) {
      case 'weekly':
        return 'Weekly';
      case 'monthly':
        return 'Monthly';
      case 'quarterly':
        return 'Quarterly';
      case 'yearly':
        return 'Yearly';
      default:
        return 'Unknown';
    }
  }

  /**
   * Format days since last purchase for display
   * @param days - Number of days
   * @returns string - Formatted string
   */
  formatDaysSinceLastPurchase(days: number | null): string {
    if (days === null) {
      return 'Never';
    } else if (days === 0) {
      return 'Today';
    } else if (days === 1) {
      return '1 day ago';
    } else if (days <= 7) {
      return `${days} days ago`;
    } else if (days <= 30) {
      const weeks = Math.floor(days / 7);
      return weeks === 1 ? '1 week ago' : `${weeks} weeks ago`;
    } else if (days <= 365) {
      const months = Math.floor(days / 30);
      return months === 1 ? '1 month ago' : `${months} months ago`;
    } else {
      const years = Math.floor(days / 365);
      return years === 1 ? '1 year ago' : `${years} years ago`;
    }
  }

  /**
   * Get icon based on buyer class or default buyer icon
   * @param buyerClass - Buyer class name
   * @returns string - Ionic icon name
   */
  getBuyerIcon(buyerClass: string | null): string {
    if (buyerClass) {
      // You can customize icons based on buyer class
      switch (buyerClass.toLowerCase()) {
        case 'premium':
          return 'diamond';
        case 'wholesale':
          return 'business';
        case 'retail':
          return 'storefront';
        default:
          return 'person-circle';
      }
    }
    return 'person-circle';
  }

  /**
   * Get action suggestions based on buyer status
   * @param buyer - Inactive buyer data
   * @returns string[] - Array of suggested actions
   */
  getActionSuggestions(buyer: InactiveBuyer): string[] {
    const suggestions: string[] = [];

    if (buyer.days_since_last_purchase === null) {
      suggestions.push('Contact buyer to encourage first purchase');
      suggestions.push('Offer welcome discount or promotion');
    } else if (buyer.days_since_last_purchase <= 7) {
      suggestions.push('Monitor for continued purchasing activity');
    } else if (buyer.days_since_last_purchase <= 30) {
      suggestions.push('Send promotional email or catalog');
      suggestions.push('Check if buyer needs product information');
    } else if (buyer.days_since_last_purchase <= 90) {
      suggestions.push('Schedule follow-up call');
      suggestions.push('Review buyer account and credit status');
      suggestions.push('Offer special pricing or incentives');
    } else {
      suggestions.push('Urgent: Contact buyer immediately');
      suggestions.push('Review account for potential issues');
      suggestions.push('Consider win-back campaign');
    }

    return suggestions;
  }
}
