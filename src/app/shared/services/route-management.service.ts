import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

export interface RouteSchedule {
  id?: number;
  route: number;
  route_name?: string;
  weekday: string;
  is_active: boolean;
  expected_billing_time?: string;
  notes?: string;
}

export interface RouteWithSchedules {
  id: number;
  name: string;
  schedules: RouteSchedule[];
  active_days: string[];
}

@Injectable({
  providedIn: 'root'
})
export class RouteManagementService {

  constructor(private http: HttpClient) { }

  /**
   * Get all routes
   */
  getRoutes(): Promise<any> {
    return this.http
      .get(`${environment.apiUrl}/route/`)
      .toPromise();
  }

  /**
   * Create a new route
   */
  createRoute(name: string): Promise<any> {
    return this.http
      .post(`${environment.apiUrl}/route/`, { name })
      .toPromise();
  }

  /**
   * Update route
   */
  updateRoute(routeId: number, name: string): Promise<any> {
    return this.http
      .put(`${environment.apiUrl}/route/`, { id: routeId, name })
      .toPromise();
  }

  /**
   * Delete route
   */
  deleteRoute(routeId: number): Promise<any> {
    return this.http
      .delete(`${environment.apiUrl}/route/`, { body: { id: routeId } })
      .toPromise();
  }

  /**
   * Get route schedules
   */
  getRouteSchedules(routeId?: number): Promise<any> {
    let params = '';
    if (routeId) {
      params = `?route_id=${routeId}`;
    }
    
    return this.http
      .get(`${environment.apiUrl}/route-schedule/${params}`)
      .toPromise();
  }

  /**
   * Create or update route schedules
   */
  saveRouteSchedules(routeId: number, weekdays: string[], expectedBillingTime?: string, notes?: string): Promise<any> {
    // Convert empty string to null for time field
    const billingTime = expectedBillingTime && expectedBillingTime.trim() ? expectedBillingTime.trim() : null;
    const scheduleNotes = notes && notes.trim() ? notes.trim() : null;

    return this.http
      .post(`${environment.apiUrl}/route-schedule/`, {
        route_id: routeId,
        weekdays: weekdays,
        expected_billing_time: billingTime,
        notes: scheduleNotes
      })
      .toPromise();
  }

  /**
   * Update individual route schedule
   */
  updateRouteSchedule(scheduleId: number, data: Partial<RouteSchedule>): Promise<any> {
    return this.http
      .put(`${environment.apiUrl}/route-schedule/`, { id: scheduleId, ...data })
      .toPromise();
  }

  /**
   * Delete route schedule
   */
  deleteRouteSchedule(scheduleId: number): Promise<any> {
    return this.http
      .delete(`${environment.apiUrl}/route-schedule/`, { body: { id: scheduleId } })
      .toPromise();
  }

  /**
   * Get routes with their schedules
   */
  async getRoutesWithSchedules(): Promise<RouteWithSchedules[]> {
    try {
      const [routesResponse, schedulesResponse] = await Promise.all([
        this.getRoutes(),
        this.getRouteSchedules()
      ]);

      if (routesResponse.success && schedulesResponse.success) {
        const routes = routesResponse.data;
        const schedules = schedulesResponse.data;

        return routes.map(route => {
          const routeSchedules = schedules.filter(schedule => schedule.route === route.id);
          const activeDays = routeSchedules
            .filter(schedule => schedule.is_active)
            .map(schedule => schedule.weekday);

          return {
            id: route.id,
            name: route.name,
            schedules: routeSchedules,
            active_days: activeDays
          };
        });
      }

      return [];
    } catch (error) {
      console.error('Error getting routes with schedules:', error);
      return [];
    }
  }

  /**
   * Get weekday options
   */
  getWeekdayOptions(): { value: string; label: string }[] {
    return [
      { value: 'monday', label: 'Monday' },
      { value: 'tuesday', label: 'Tuesday' },
      { value: 'wednesday', label: 'Wednesday' },
      { value: 'thursday', label: 'Thursday' },
      { value: 'friday', label: 'Friday' },
      { value: 'saturday', label: 'Saturday' },
      { value: 'sunday', label: 'Sunday' }
    ];
  }

  /**
   * Get routes for a specific weekday
   */
  async getRoutesForWeekday(weekday: string): Promise<RouteWithSchedules[]> {
    const routesWithSchedules = await this.getRoutesWithSchedules();
    return routesWithSchedules.filter(route => 
      route.active_days.includes(weekday.toLowerCase())
    );
  }

  /**
   * Check if route is active on a specific day
   */
  async isRouteActiveOnDay(routeId: number, weekday: string): Promise<boolean> {
    try {
      const response = await this.getRouteSchedules(routeId);
      if (response.success) {
        const schedules = response.data;
        return schedules.some(schedule => 
          schedule.weekday === weekday.toLowerCase() && schedule.is_active
        );
      }
      return false;
    } catch (error) {
      console.error('Error checking route activity:', error);
      return false;
    }
  }

  /**
   * Get route summary for dashboard
   */
  async getRouteSummary(): Promise<any> {
    try {
      const routesWithSchedules = await this.getRoutesWithSchedules();
      const totalRoutes = routesWithSchedules.length;
      const activeRoutes = routesWithSchedules.filter(route => route.active_days.length > 0).length;
      
      const weekdayCounts = {};
      this.getWeekdayOptions().forEach(day => {
        weekdayCounts[day.value] = routesWithSchedules.filter(route => 
          route.active_days.includes(day.value)
        ).length;
      });

      return {
        total_routes: totalRoutes,
        active_routes: activeRoutes,
        inactive_routes: totalRoutes - activeRoutes,
        weekday_counts: weekdayCounts,
        routes: routesWithSchedules
      };
    } catch (error) {
      console.error('Error getting route summary:', error);
      return {
        total_routes: 0,
        active_routes: 0,
        inactive_routes: 0,
        weekday_counts: {},
        routes: []
      };
    }
  }
}
