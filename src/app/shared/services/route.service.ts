import { Injectable } from '@angular/core';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class RouteService {

  constructor(
    private router: Router,
  ) { }
  
  routerFunction(path: string) {
    // Use navigate instead of navigateByUrl to ensure proper lifecycle events
    this.router.navigate([path]);
  }

  // Method to force refresh a page by navigating with replaceUrl
  routerFunctionWithRefresh(path: string) {
    this.router.navigate([path], { replaceUrl: true });
  }

  // Method to navigate back and refresh
  navigateBackAndRefresh(path: string) {
    this.router.navigate([path], { 
      replaceUrl: true,
      queryParams: { refresh: Date.now() } // Add timestamp to force refresh
    });
  }
}
