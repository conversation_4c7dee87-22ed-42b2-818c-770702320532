import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, firstValueFrom } from 'rxjs';
import { environment } from '../../../environments/environment';

export interface BuyerBrand {
  id?: number;
  buyer_id: number;
  brand: number;
  buyer_name?: string;
  brand_name?: string;
  is_active?: boolean;
  created_date?: string;
}

export interface BuyerProductsResponse {
  products: any[];
  brands: any[];
  buyer_id: number;
  message?: string;
}

@Injectable({
  providedIn: 'root'
})
export class BuyerBrandService {
  private apiUrl = `${environment.apiUrl}/buyer_brand/`;
  private buyerProductsUrl = `${environment.apiUrl}/buyer_products/`;

  constructor(private http: HttpClient) { }

  // Get buyer-brand relationships
  getBuyerBrands(buyerId?: number, brandId?: number): Observable<any> {
    let url = this.apiUrl;
    const params: string[] = [];
    
    if (buyerId) {
      params.push(`buyer_id=${buyerId}`);
    }
    if (brandId) {
      params.push(`brand_id=${brandId}`);
    }
    
    if (params.length > 0) {
      url += `?${params.join('&')}`;
    }
    
    return this.http.get(url);
  }

  // Create buyer-brand relationship
  createBuyerBrand(buyerBrand: { buyer_id: number; brand_id: number }): Observable<any> {
    return this.http.post(this.apiUrl, buyerBrand);
  }

  // Delete buyer-brand relationship
  deleteBuyerBrand(buyerBrandId: number): Observable<any> {
    return this.http.delete(this.apiUrl, { body: { id: buyerBrandId } });
  }

  // Get products filtered by buyer brands
  getProductsByBuyer(buyerId: number): Observable<any> {
    return this.http.get(`${this.buyerProductsUrl}?buyer_id=${buyerId}`);
  }

  // Bulk create buyer-brand relationships
  createBulkBuyerBrands(buyerId: number, brandIds: number[]): Observable<any[]> {
    const requests = brandIds.map(brandId => 
      this.createBuyerBrand({ buyer_id: buyerId, brand_id: brandId })
    );
    return new Observable(observer => {
      Promise.all(requests.map(req => firstValueFrom(req)))
        .then(results => {
          observer.next(results);
          observer.complete();
        })
        .catch(error => observer.error(error));
    });
  }

  // Bulk delete buyer-brand relationships
  deleteBulkBuyerBrands(buyerBrandIds: number[]): Observable<any[]> {
    const requests = buyerBrandIds.map(id => this.deleteBuyerBrand(id));
    return new Observable(observer => {
      Promise.all(requests.map(req => firstValueFrom(req)))
        .then(results => {
          observer.next(results);
          observer.complete();
        })
        .catch(error => observer.error(error));
    });
  }
} 