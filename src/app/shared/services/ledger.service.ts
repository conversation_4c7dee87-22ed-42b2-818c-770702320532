import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class LedgerService {
 

  constructor(private http: HttpClient) { }

  getInitialLedger(buyerView,page_number,page_size,search_text) : Observable<any>  {
    return this.http.get(`${environment.apiUrl}/ledger/?page_size=${page_size}&page_number=${page_number}&${buyerView}${search_text?'&search='+search_text:''}`);
  }
  getFilterLedger(from_date,to_date,id,mode,page_number?,page_size?): Observable<any> {
    let url = `${environment.apiUrl}/ledger/?${from_date ? 'from_date='+from_date : ''}${to_date ? '&to_date='+to_date : ''}${mode == 'pay-in' ?'&buyer_id=' : '&supplier_id='}${id}`;
    
    // Add pagination parameters if provided
    if (page_number && page_size) {
      url += `&page_size=${page_size}&page_number=${page_number}`;
    }
    
    return this.http.get(url);
  }
  saveLedger(data,entryMode){
    return this.http.post(`${environment.apiUrl}/ledger/${entryMode}`,data).toPromise();
  }
  deleteLedger(id: any) {
    return this.http.delete(`${environment.apiUrl}/ledger/?ledger_id=${id}`).toPromise();

  }

  editLedger(data){
    return this.http.put(`${environment.apiUrl}/ledger/`,data).toPromise();
  }
}
