import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ExpenseService {
  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) { }

  getCategories(): Observable<any> {
    return this.http.get(`${this.apiUrl}/expense_category/`);
  }

  getSubCategories(category: string): Observable<any> {
    return this.http.get(`${this.apiUrl}/expense_sub_category/?category=${category}`);
  }
  getExpense(): Observable<any> {
    return this.http.get(`${this.apiUrl}/expense/`);
  }
  getFilterExpense(from_date,to_date): Observable<any> {
    return this.http.get(`${environment.apiUrl}/expense/?${from_date ? 'from_date='+from_date : ''}${to_date ? '&to_date='+to_date : ''}`);
  }

  addCategory(category: string): Observable<any> {
    return this.http.post(`${this.apiUrl}/expense_category/`, { name: category });
  }

  addSubCategory(category: string, subcategory: string): Observable<any> {
    return this.http.post(`${this.apiUrl}/expense_sub_category/`, { category, name: subcategory });
  }

  addExpense(expense: any): Observable<any> {
    return this.http.post(`${this.apiUrl}/expense/`, expense);
  }

  updateExpense(expense: any): Observable<any> {
    return this.http.put(`${this.apiUrl}/expense/`, expense);
  }
  deleteExpense(id: any): Observable<any>  {
    return this.http.delete(`${environment.apiUrl}/expense/?expense_id=${id}`);

  }
}
