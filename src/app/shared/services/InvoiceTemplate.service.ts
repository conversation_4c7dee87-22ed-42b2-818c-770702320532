import { Injectable } from '@angular/core';
import { Platform } from '@ionic/angular';

export interface InvoiceTemplateConfig {
    pageSize?: string;
    orientation?: 'portrait' | 'landscape';
    fontFamily?: string;
    fontSize?: string;
    sections?: SectionConfig[];
    styles?: {
        global?: Record<string, string>;
        sections?: Record<string, Record<string, string>>;
    };
    printOptions?: {
        marginTop?: string;
        marginBottom?: string;
        marginLeft?: string;
        marginRight?: string;
    };
}

interface SectionConfig {
    type: string;
    layout?: string;
    columns?: ColumnConfig[];
    components?: ComponentConfig[];
    sections?: SectionConfig[];

    // Add these properties to resolve the TypeScript errors
    width?: string;
    items?: Array<{
        label: string;
        key?: string;
        content?: (data: any) => any;
    }>;
}

interface ColumnConfig {
    width?: string;
    content?: (data: any) => Record<string, any>;
    key?: string;
    label?: string;
    transform?: (value: any) => string;
}

interface ComponentConfig {
    type: string;
    width?: string;
    source?: (data: any) => string;
    content?: (data: any) => Record<string, any>;
}

@Injectable({
    providedIn: 'root'
})
export class InvoiceTemplateService {
    private defaultConfig: InvoiceTemplateConfig = {
        pageSize: 'A4',
        orientation: 'portrait',
        fontFamily: 'Arial, sans-serif',
        fontSize: '10px',
        sections: [
            {
                type: 'header',
                layout: 'flex',
                components: [
                    {
                        type: 'logo',
                        width: 'auto',
                        source: (data) => data.logoUrl || this.getStoredCompanyLogo()
                    },
                    {
                        type: 'companyInfo',
                        width: '100%',
                        content: () => this.getCompanyDetails()
                    }
                ]
            },
            {
                type: 'customerInfo',
                layout: 'grid',
                columns: [
                    {
                        width: '55%',
                        content: (data) => ({
                            name: data.name,
                            phone: data.phone_no,
                            address: data.place
                        })
                    },
                    {
                        width: '35%',
                        content: (data) => ({
                            placeOfSupply: 'Tamilnadu(33)',
                            gstNo: data.gst_no
                        })
                    },
                    {
                        width: '30%',
                        content: (data) => ({
                            invoiceNo: data.id,
                            date: data.date
                        })
                    }
                ]
            },
            {
                type: 'itemsTable',
                columns: [
                    { key: 'product_name', label: 'Particulars', width: '35%' },
                    { key: 'mrp', label: 'MRP', width: '9%' },
                    {
                        key: 'rate',
                        label: 'Rate',
                        width: '9%',
                        transform: (item) => (item.rate - (item.rate * item.tax_rate) / (100 + item.tax_rate)).toFixed(2)
                    },
                    { key: 'no', label: 'Box', width: '8%' },
                    { key: 'weight', label: 'Pcs', width: '8%' },
                    { key: 'tax_rate', label: 'Tax%', width: '7%' },
                    {
                        key: 'tax_amount',
                        label: 'Tax ₹',
                        width: '12%',
                        transform: (item) => ((item.line_total * item.tax_rate) / (100 + item.tax_rate)).toFixed(2)
                    },
                    {
                        key: 'line_total',
                        label: 'Amount',
                        width: '12%',
                        transform: (item) => item.line_total.toFixed(2)
                    }
                ]
            }
        ],
        styles: {
            global: {
                'font-family': 'Arial, sans-serif',
                'font-size': '10px',
                'line-height': '1.5',
                'color': '#000'
            },
            sections: {
                header: {
                    'background-color': 'transparent',
                    'border': '0.5px solid #000'
                },
                itemsTable: {
                    'border': '0.5px solid #000',
                    'header-background-color': '#f0f0f0'
                }
            }
        },
        printOptions: {
            marginTop: '10mm',
            marginBottom: '10mm',
            marginLeft: '10mm',
            marginRight: '10mm'
        }
    };

    constructor(private platform: Platform) { }

    // Utility method for deep merging configurations
    private deepMerge(target: any, source: any): any {
        const output = { ...target };
        if (this.isObject(target) && this.isObject(source)) {
            Object.keys(source).forEach(key => {
                if (this.isObject(source[key])) {
                    if (!(key in target)) {
                        output[key] = source[key];
                    } else {
                        output[key] = this.deepMerge(target[key], source[key]);
                    }
                } else {
                    output[key] = source[key];
                }
            });
        }
        return output;
    }

    // Check if item is a plain object
    private isObject(item: any): boolean {
        return item && typeof item === 'object' && !Array.isArray(item);
    }

    // Generate invoice HTML with customizable configuration
    generateInvoiceHTML(data: any, customConfig?: InvoiceTemplateConfig): string {
        // Merge default and custom configurations
        const config = this.deepMerge(this.defaultConfig, customConfig || {});

        // Generate HTML content
        return this.createHTMLTemplate(data, config);
    }

    // Print invoice directly
    printInvoice(data: any, customConfig?: InvoiceTemplateConfig): void {
        const htmlContent = this.generateInvoiceHTML(data, customConfig);

        // Create a new window for printing
        const printWindow = window.open('', '_blank');
        if (printWindow) {
            printWindow.document.write(htmlContent);
            printWindow.document.close();

            // Trigger print after a short delay to ensure content is loaded
            setTimeout(() => {
                printWindow.print();
                printWindow.close();
            }, 500);
        }
    }

    // Create HTML template based on configuration
    private createHTMLTemplate(data: any, config: InvoiceTemplateConfig): string {
        return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>${data.name} - Invoice</title>
        <meta charset="UTF-8">
        <style>
          ${this.generateStyles(config)}
        </style>
      </head>
      <body>
        ${this.renderSections(config.sections!, data)}
      </body>
      </html>
    `;
    }

    // Generate CSS styles from configuration
    private generateStyles(config: InvoiceTemplateConfig): string {
        const globalStyles = Object.entries(config.styles?.global || {})
            .map(([prop, value]) => `${prop}: ${value};`)
            .join('\n');

        const sectionStyles = Object.entries(config.styles?.sections || {})
            .map(([sectionName, styles]) =>
                `.${sectionName}-section {
          ${Object.entries(styles)
                    .map(([prop, value]) => `${this.camelToKebabCase(prop)}: ${value};`)
                    .join('\n')
                }
        }`
            )
            .join('\n');

        return `
      @page {
        size: ${config.pageSize} ${config.orientation};
        margin: ${config.printOptions?.marginTop} ${config.printOptions?.marginRight} ${config.printOptions?.marginBottom} ${config.printOptions?.marginLeft};
      }
      * {
        box-sizing: border-box;
        ${globalStyles}
      }
      ${sectionStyles}
    `;
    }

    // Render invoice sections
    private renderSections(sections: SectionConfig[], data: any): string {
        return sections.map(section => {
            switch (section.type) {
                case 'header':
                    return this.renderHeaderSection(section, data);
                case 'customerInfo':
                    return this.renderCustomerInfoSection(section, data);
                case 'itemsTable':
                    return this.renderItemsTableSection(section, data);
                default:
                    return '';
            }
        }).join('');
    }

    // Add these methods to the InvoiceTemplateService class

    // Render header section
    private renderHeaderSection(section: SectionConfig, data: any): string {
        if (!section.components || section.components.length === 0) {
            return '';
        }

        const renderComponent = (component: ComponentConfig) => {
            switch (component.type) {
                case 'logo':
                    const logoSrc = component.source ? component.source(data) : '';
                    return `
            <div class="logo" style="width: ${component.width || 'auto'};">
              <img src="${logoSrc}" alt="Company Logo" style="max-width: 100%; height: auto;"/>
            </div>
          `;

                case 'companyInfo':
                    const companyInfo = component.content ? component.content(data) : {};
                    return `
            <div class="company-info" style="width: ${component.width || '100%'};">
              <h1>${companyInfo.name || ''}</h1>
              <p>${companyInfo.address || ''}</p>
              <div class="contact-info">
                <span>${companyInfo.contact?.left || ''}</span>
                <span>${companyInfo.contact?.right || ''}</span>
              </div>
              <div class="registration-info">
                <span>GST: ${companyInfo.gst || 'N/A'}</span>
                <span>FSSAI: ${companyInfo.fssai || 'N/A'}</span>
              </div>
            </div>
          `;

                default:
                    return '';
            }
        };

        const componentHtml = section.components
            .map(component => renderComponent(component))
            .join('');

        return `
      <div class="header-section" style="display: ${section.layout === 'flex' ? 'flex' : 'block'}; 
             justify-content: space-between; 
             align-items: center; 
             margin-bottom: 15px;">
        ${componentHtml}
      </div>
    `;
    }

    // Render customer info section
    private renderCustomerInfoSection(section: SectionConfig, data: any): string {
        if (!section.columns || section.columns.length === 0) {
            return '';
        }

        const renderColumn = (column: ColumnConfig) => {
            const content = column.content ? column.content(data) : {};

            // Conditional rendering based on column type/content
            const renderContent = () => {
                if (content.name) {
                    return `
            <strong>${content.name}</strong>
            <p>${content.phone ? `Phone: ${content.phone}` : ''}</p>
            <p>${content.address || ''}</p>
          `;
                }

                if (content.placeOfSupply) {
                    return `
            <p>Place of Supply: ${content.placeOfSupply}</p>
            <p>GST No: ${content.gstNo || 'N/A'}</p>
          `;
                }

                if (content.invoiceNo) {
                    return `
            <p>Invoice No: ${content.invoiceNo}</p>
            <p>Date: ${content.date}</p>
          `;
                }

                return '';
            };

            return `
        <div class="customer-info-column" style="width: ${column.width || 'auto'};">
          ${renderContent()}
        </div>
      `;
        };

        const columnsHtml = section.columns
            .map(column => renderColumn(column))
            .join('');

        return `
      <div class="customer-info-section" style="
          display: ${section.layout === 'grid' ? 'grid' : 'flex'}; 
          grid-template-columns: repeat(${section.columns?.length || 1}, 1fr);
          gap: 10px; 
          margin-bottom: 15px;">
        ${columnsHtml}
      </div>
    `;
    }

    // Render items table section
    private renderItemsTableSection(section: SectionConfig, data: any): string {
        if (!section.columns || section.columns.length === 0 || !data.items) {
            return '';
        }

        // Generate table header
        const tableHeader = section.columns.map(column =>
            `<th style="width: ${column.width || 'auto'}">${column.label || column.key}</th>`
        ).join('');

        // Generate table rows
        const tableRows = data.items.map(item => {
            const rowCells = section.columns!.map(column => {
                // Handle custom transformations
                const value = column.key ? item[column.key] : '';
                const transformedValue = column.transform
                    ? column.transform(item)
                    : value;

                return `<td>${transformedValue}</td>`;
            }).join('');

            return `<tr>${rowCells}</tr>`;
        }).join('');

        return `
      <div class="items-table-section">
        <table style="width: 100%; border-collapse: collapse;">
          <thead>
            <tr>${tableHeader}</tr>
          </thead>
          <tbody>
            ${tableRows}
          </tbody>
        </table>
      </div>
    `;
    }

    // Optional: Summary Section Renderer
    private renderSummarySection(section: SectionConfig, data: any): string {
        if (!section.sections) return '';

        const renderSummarySections = section.sections.map(summarySection => {
            switch (summarySection.type) {
                case 'balanceDetails':
                    return this.renderBalanceDetails(summarySection, data);
                case 'taxBreakdown':
                    return this.renderTaxBreakdown(summarySection, data);
                case 'finalCalculations':
                    return this.renderFinalCalculations(summarySection, data);
                default:
                    return '';
            }
        }).join('');

        return `
      <div class="summary-section" style="display: flex; justify-content: space-between;">
        ${renderSummarySections}
      </div>
    `;
    }

    // Helper methods for summary sections
    private renderBalanceDetails(section: SectionConfig, data: any): string {
        const items = section.items?.map(item => `
      <div class="balance-item">
        <span>${item.label}</span>
        <span>${data[item.key] || 'N/A'}</span>
      </div>
    `).join('');

        return `
      <div class="balance-details" style="width: ${section.width || 'auto'};">
        <h3>Balance Details</h3>
        ${items}
      </div>
    `;
    }

    private renderTaxBreakdown(section: SectionConfig, data: any): string {
        // Implement tax breakdown rendering logic
        return `
      <div class="tax-breakdown" style="width: ${section.width || 'auto'};">
        <h3>Tax Breakdown</h3>
        <!-- Implement tax breakdown table -->
      </div>
    `;
    }

    private renderFinalCalculations(section: SectionConfig, data: any): string {
        const items = section.items?.map(item => `
      <div class="calculation-item">
        <span>${item.label}</span>
        <span>${data[item.key] || 'N/A'}</span>
      </div>
    `).join('');

        return `
      <div class="final-calculations" style="width: ${section.width || 'auto'};">
        <h3>Final Calculations</h3>
        ${items}
      </div>
    `;
    }

    // Utility to convert camelCase to kebab-case
    private camelToKebabCase(str: string): string {
        return str.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();
    }

    // Helper methods for retrieving stored data
    private getStoredCompanyLogo(): string {
        return localStorage.getItem('company_logo') || '';
    }

    private getCompanyDetails(): Record<string, any> {
        return {
            name: localStorage.getItem('company_name') || '',
            address: localStorage.getItem('address') || '',
            contact: {
                left: localStorage.getItem('contact_no_left') || '',
                right: localStorage.getItem('contact_no_right') || ''
            },
            gst: localStorage.getItem('gst_no') || '',
            fssai: localStorage.getItem('fssai_no') || ''
        };
    }
}
