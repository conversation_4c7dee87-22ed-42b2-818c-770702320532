import { Injectable } from '@angular/core';

import { HttpClient ,HttpHeaders} from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { CacheService } from './cache.service';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ProductService {

  constructor(
    private http: HttpClient,
    private cacheService: CacheService
  ) { }

  getProduct(page_number,page_size,search_text) {
    // Create cache key based on parameters
    const cacheKey = `products_${page_number}_${page_size}_${search_text || 'all'}`;
    
    // For first page or searches, use shorter cache duration
    const cacheDuration = (page_number === 1 || search_text) ? 2 * 60 * 1000 : 5 * 60 * 1000; // 2 or 5 minutes
    
    return this.cacheService.getOrSet(
      cacheKey,
      () => this.http.get(`${environment.apiUrl}/product/?page_size=${page_size}&page_number=${page_number}${search_text?'&search='+search_text:''}`),
      cacheDuration
    ).toPromise();
  }
  saveProduct(data) {
    // Invalidate product cache when adding new product
    this.cacheService.invalidatePattern('products_');
    this.cacheService.invalidatePattern('brands_');
    
    return this.http
      .post(`${environment.apiUrl}/product/`, data)
      .toPromise();
  }
  editProduct(data){
    // Invalidate product cache when editing product
    this.cacheService.invalidatePattern('products_');
    this.cacheService.invalidatePattern('brands_');
    
    return this.http.put(`${environment.apiUrl}/product/`, data)
    .toPromise();
  }
  editProductStatus(data){
    // Invalidate product cache when changing status
    this.cacheService.invalidatePattern('products_');
    
    return this.http.put(`${environment.apiUrl}/product/`, data)
    .toPromise();
  }
  deleteProduct(data){
    // Invalidate product cache when deleting product
    this.cacheService.invalidatePattern('products_');
    this.cacheService.invalidatePattern('brands_');
    
    return this.http.delete(`${environment.apiUrl}/product/`, {body:data})
    .toPromise();
  }

  getBrand(){
    return this.http.get(`${environment.apiUrl}/brand/`)
      .toPromise();
  }
  addBrand(data){
    return this.http.post(`${environment.apiUrl}/brand/`, {name:data})
      .toPromise();
  }
}
