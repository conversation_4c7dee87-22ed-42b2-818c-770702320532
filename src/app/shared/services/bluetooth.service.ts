import { Injectable } from '@angular/core';
import { BluetoothSerial } from '@awesome-cordova-plugins/bluetooth-serial/ngx'
import { AndroidPermissions } from '@awesome-cordova-plugins/android-permissions/ngx';
import { Platform } from '@ionic/angular';
import { AlertController } from '@ionic/angular';
import { StorageService } from './storage.service';

@Injectable({
    providedIn: 'root'
})
export class BluetoothService {
    private device: any = null;
    serviceUUID: string;
    characteristicUUID: any;
    constructor(
        private bluetoothSerial: BluetoothSerial,
        private androidPermissions: AndroidPermissions,
        private platform: Platform,
        private alertController: AlertController,
        private storageService: StorageService
    ) {
        this.platform.ready().then(() => {
            this.checkBluetoothPermissions();
        });
    }
    checkBluetoothPermissions() {
        this.platform.ready().then(() => {
            if (this.platform.is('android')) {
                // Check for basic Bluetooth permission first
                this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.BLUETOOTH).then(
                    result => {
                        if (!result.hasPermission) {
                            this.androidPermissions.requestPermission(this.androidPermissions.PERMISSION.BLUETOOTH);
                        }
                    },
                    err => console.log('Bluetooth permission check error:', err)
                );
            }
        });
    }

    getPermissions() {
        return new Promise((resolve, reject) => {
            this.platform.ready().then(() => {
                if (this.platform.is('android')) {
                    // For Android 12+ we need BLUETOOTH_CONNECT, for older versions BLUETOOTH is enough
                    const permission = this.androidPermissions.PERMISSION.BLUETOOTH_CONNECT || this.androidPermissions.PERMISSION.BLUETOOTH;

                    this.androidPermissions.checkPermission(permission).then(
                        result => {
                            if (!result.hasPermission) {
                                this.androidPermissions.requestPermission(permission).then(
                                    result => {
                                        if (result.hasPermission) {
                                            resolve('ok');
                                        } else {
                                            reject('Bluetooth permission denied. Please enable Bluetooth permissions in Settings.');
                                        }
                                    },
                                    err => reject('Failed to request Bluetooth permission: ' + err)
                                );
                            } else {
                                resolve('ok');
                            }
                        },
                        err => {
                            console.log('Permission check failed, trying to request anyway:', err);
                            this.androidPermissions.requestPermission(permission).then(
                                result => {
                                    if (result.hasPermission) {
                                        resolve('ok');
                                    } else {
                                        reject('Bluetooth permission denied');
                                    }
                                },
                                err => reject('Permission request failed: ' + err)
                            );
                        }
                    );
                } else {
                    resolve('ok'); // Non-Android platforms
                }
            }).catch(err => {
                reject('Platform not ready: ' + err);
            });
        });
    }

    getList() {
        this.getPermissions().then(() => {
            this.bluetoothSerial.list().then(async devices => {
                if (devices.length === 0) {
                    const noDevicesAlert = await this.alertController.create({
                        header: 'No Devices Found',
                        message: 'No paired Bluetooth devices found. Please pair your printer in Android Settings first.',
                        buttons: ['OK']
                    });
                    await noDevicesAlert.present();
                    return;
                }

                localStorage.setItem('devices_list', JSON.stringify(devices));
                const alert = await this.alertController.create({
                    header: 'Select a Printer',
                    inputs: devices.map(device => ({
                        name: 'printer',
                        type: 'radio',
                        label: device.name || 'Unnamed Device',
                        value: device.id,
                        checked: false
                    })),
                    buttons: [
                        {
                            text: 'Cancel',
                            role: 'cancel',
                            cssClass: 'secondary',
                            handler: () => {
                                console.log('Device selection cancelled');
                            },
                        },
                        {
                            text: 'Select',
                            handler: (deviceId) => {
                                if (deviceId) {
                                    localStorage.setItem('deviceId', deviceId);
                                    console.log('Device selected:', deviceId);
                                }
                            },
                        },
                    ],
                });

                await alert.present();

            }).catch(async error => {
                console.error('Error getting device list:', error);
                const errorAlert = await this.alertController.create({
                    header: 'Error',
                    message: 'Failed to get Bluetooth devices. Please ensure Bluetooth is enabled.',
                    buttons: ['OK']
                });
                await errorAlert.present();
            });
        }).catch(async error => {
            console.error('Permission error:', error);
            const errorAlert = await this.alertController.create({
                header: 'Permission Required',
                message: typeof error === 'string' ? error : 'Bluetooth permission is required to select printers.',
                buttons: ['OK']
            });
            await errorAlert.present();
        });
    }
    getSelectedDevice() {
        let bluetoothDevices = JSON.parse(localStorage.getItem('devices_list'))
        let selectedDeviceId = localStorage.getItem('deviceId')
        if (bluetoothDevices && selectedDeviceId) {
            return bluetoothDevices.find((e) => e.id === selectedDeviceId)
        } else {
            return null
        }
    }
    printToDevice(resultByte) {
        this.getPermissions().then(() => {
            this.bluetoothSerial.connect(localStorage.getItem('deviceId'),).subscribe(() => {
                this.bluetoothSerial.write(resultByte)
                    .then(() => {
                        console.log('Print success');
                        this.disconnectPrinter()
                    })
                    .catch((err) => {
                        console.error('Print error:', err);
                    });
            }, (err) => {
                console.error('Connection error:', err);
            });
        }).catch(error => {
            console.error('Permission error:', error);
        });
    }
    disconnectPrinter() {
        this.getPermissions().then(() => {
            this.bluetoothSerial.disconnect().then(() => {
                console.log("Disconnected successfully");
            }).catch(error => {
                console.error(error);
            });
        });
    }

    async scanForPrinters() {
        try {
            const device = await navigator.bluetooth.requestDevice({
                acceptAllDevices: true,
                optionalServices: ['battery_service', 'device_information'] // Example services
            });
            console.log(`Device selected`, device);

            const server = await device.gatt.connect();

            // Using a type assertion to bypass TypeScript error
            const services = await (server as any).getPrimaryServices();
            console.log('Available services:', services);

            for (const service of services) {
                console.log(`Service UUID: ${service.uuid}`);
                this.serviceUUID = service.uuid

                // Discover characteristics of each service
                const characteristics = await service.getCharacteristics();
                for (const characteristic of characteristics) {
                    if (characteristic.properties.write) {
                        console.log(`Characteristic ${characteristic.uuid} supports writing.`);
                        this.characteristicUUID = characteristic.uuid
                    }

                    if (characteristic.properties.writeWithoutResponse) {
                        console.log(`Characteristic ${characteristic.uuid} supports writing without response.`);
                    }

                }
            }
        } catch (error) {
            console.log(`There was an error: ${error}`);
        }
    }

    async requestDevice() {
        try {
            this.device = await navigator.bluetooth.requestDevice({
                acceptAllDevices: true,
                optionalServices: [this.serviceUUID]
            });
            console.log('Device connected:', this.device);
        } catch (error) {
            console.error('Device connection failed', error);
        }
    }

    async connectToDevice() {
        if (!this.device) {
            return;
        }
        const server = await this.device.gatt.connect();
        const service = await server.getPrimaryService(this.serviceUUID);
        console.log(service);

        const characteristic = await service.getCharacteristic(this.characteristicUUID);
        console.log(characteristic);
        await characteristic.writeValueWithoutResponse(new TextEncoder().encode('Your message here'));
    }

}
