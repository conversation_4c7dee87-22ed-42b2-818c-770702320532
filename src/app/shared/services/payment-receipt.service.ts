import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class PaymentReceiptService {

  constructor(private http: HttpClient) { }

  /**
   * Get payment history for a specific sales invoice
   * @param invoiceId - The ID of the sales invoice
   * @returns Observable with payment history data
   */
  getPaymentHistory(invoiceId: number): Observable<any> {
    return this.http.get(`${environment.apiUrl}/sales_invoice_payment/?invoice_id=${invoiceId}`);
  }

  /**
   * Create a new payment receipt for a sales invoice
   * @param paymentData - Payment receipt data
   * @returns Promise with API response
   */
  createPaymentReceipt(paymentData: any): Promise<any> {
    return this.http.post(`${environment.apiUrl}/sales_invoice_payment/`, paymentData).toPromise();
  }

  /**
   * Calculate outstanding balance for an invoice
   * @param billAmount - Total bill amount
   * @param totalPayments - Total payments received
   * @returns Outstanding balance
   */
  calculateOutstandingBalance(billAmount: number, totalPayments: number): number {
    return billAmount - totalPayments;
  }

  /**
   * Validate payment amount (removed outstanding balance restriction)
   * @param paymentAmount - Amount to be paid
   * @param outstandingBalance - Current outstanding balance (kept for compatibility)
   * @returns Validation result
   */
  validatePaymentAmount(paymentAmount: number, outstandingBalance: number): { isValid: boolean, message?: string } {
    if (paymentAmount <= 0) {
      return { isValid: false, message: 'Payment amount must be greater than 0' };
    }
    
    // Removed restriction: Allow amounts greater than outstanding balance
    // This allows for advance payments or overpayments
    
    return { isValid: true };
  }

  /**
   * Format payment data for API submission
   * @param formData - Form data from payment modal
   * @returns Formatted payment data
   */
  formatPaymentData(formData: any): any {
    return {
      invoice_id: formData.invoiceId,
      amount: parseFloat(formData.amount),
      mode_of_payment: formData.modeOfPayment,
      reference_number: formData.referenceNumber || '',
      notes: formData.notes || ''
    };
  }
}
