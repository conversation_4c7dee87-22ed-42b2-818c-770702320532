import { BluetoothService } from './bluetooth.service';
import { Injectable } from '@angular/core';
import { UtilService } from './util.service';
import EscPosEncoder from 'esc-pos-encoder-ionic';
import { environment } from "src/environments/environment";
import { Printer } from '@bcyesil/capacitor-plugin-printer';


// TypeScript interfaces for improved type safety and maintainability
export interface PrintConfiguration {
  paperSize: 'A4' | 'A5' | '58mm' | '80mm';
  paperType: 'Thermal' | 'Regular';
  orientation: 'portrait' | 'landscape';
  isMobile: boolean;
}

export interface InvoiceData {
  id: number;
  name: string;
  date: string;
  phone_no: string;
  place: string;
  gst_no?: string;
  sales_invoice_items: InvoiceItem[];
  bill_amount: number;
  previous_balance: number;
  received_amount: number;
  current_balance: number;
  rounding_enabled?: boolean;
  rounding_adjustment?: number;
  gross_total?: number;
  delivery_challan?: boolean;
  invoice_status?: string;
  remarks?: string;
  headerdata?: any[];
  metadata?: any[];
  billed_by?: string;
  delivery_by?: string;
  collected_by?: string;
}

export interface InvoiceItem {
  product_name: string;
  no: number;
  weight: number;
  rate: number;
  line_total: number;
  mrp: number;
  tax_rate: number;
  hsn_code?: string;
  remarks?: string;
}

export interface PrintLayoutConfig {
  headerHeight: number;
  footerHeight: number;
  contentMargin: number;
  pageHeight: number;
  itemsPerPage: number;
}

export interface PageBreakResult {
  currentPageItems: InvoiceItem[];
  remainingItems: InvoiceItem[];
  hasMorePages: boolean;
}


@Injectable({
    providedIn: 'root'
})
export class PrintServiceService {
    billing_field_settings: any = JSON.parse(localStorage.getItem('metadata') || '{}').billing_field_settings?.filter((item: any) => item.print_active)?.sort((a: any, b: any) => a.print_order - b.print_order) || [];

    constructor(
        private util: UtilService,
        private bluetoothService: BluetoothService
    ) { }

    /**
     * Calculate maximum characters per line for thermal printing
     * @param paperWidthMm - Paper width in millimeters
     * @returns Maximum characters that fit per line
     */
    getMaxCharsPerLine(paperWidthMm: number): number {
        // Average character width in mm (adjust based on your printer and font)
        const avgCharWidthMm = paperWidthMm === 58 ? 1.88 : 1.65;
        return Math.floor(paperWidthMm / avgCharWidthMm);
    }

    /**
     * Center text within specified character width
     * @param text - Text to center
     * @param maxCharsPerLine - Maximum characters per line
     * @returns Centered text with padding
     */
    centerText(text: string, maxCharsPerLine: number): string {
        const space = (maxCharsPerLine - text.length) / 2;
        return ' '.repeat(Math.max(0, space)) + text;
    }

    /**
     * Get print configuration based on current settings
     * @param isMobile - Whether printing from mobile device
     * @returns Print configuration object
     */
    getPrintConfiguration(isMobile: boolean = false): PrintConfiguration {
        const paperSize = localStorage.getItem('paper_size') as 'A4' | 'A5' | '58mm' | '80mm' || 'A4';
        const paperType = localStorage.getItem('paper_type') as 'Thermal' | 'Regular' || 'Regular';

        return {
            paperSize,
            paperType,
            orientation: 'portrait',
            isMobile
        };
    }

    /**
     * Calculate layout configuration for different paper sizes
     * @param config - Print configuration
     * @returns Layout configuration with dimensions
     */
    getLayoutConfig(config: PrintConfiguration): PrintLayoutConfig {
        switch (config.paperSize) {
            case 'A4':
                return {
                    headerHeight: 200,
                    footerHeight: 100,
                    contentMargin: 20,
                    pageHeight: 1123, // A4 height in pixels at 96 DPI
                    itemsPerPage: 25
                };
            case 'A5':
                return {
                    headerHeight: 150,
                    footerHeight: 80,
                    contentMargin: 15,
                    pageHeight: 794, // A5 height in pixels at 96 DPI
                    itemsPerPage: 15
                };
            case '58mm':
            case '80mm':
                return {
                    headerHeight: 100,
                    footerHeight: 50,
                    contentMargin: 5,
                    pageHeight: 0, // Continuous for thermal
                    itemsPerPage: 50
                };
            default:
                return {
                    headerHeight: 200,
                    footerHeight: 100,
                    contentMargin: 20,
                    pageHeight: 1123,
                    itemsPerPage: 25
                };
        }
    }

    /**
     * Calculate page breaks for invoice items to prevent splitting
     * @param items - Invoice items to paginate
     * @param itemsPerPage - Maximum items per page
     * @returns Page break result with current and remaining items
     */
    calculatePageBreak(items: InvoiceItem[], itemsPerPage: number): PageBreakResult {
        if (items.length <= itemsPerPage) {
            return {
                currentPageItems: items,
                remainingItems: [],
                hasMorePages: false
            };
        }

        return {
            currentPageItems: items.slice(0, itemsPerPage),
            remainingItems: items.slice(itemsPerPage),
            hasMorePages: true
        };
    }


    /**
     * Calculate tax details grouped by tax rate
     * @param data - Invoice data containing items
     * @returns Tax details with breakdown by rate and total tax amount
     */
    getTaxDetails(data: InvoiceData): { taxDetails: any[], totalTaxAmount: number } {
        const distinctValues = [...new Set(data.sales_invoice_items.map((item: InvoiceItem) => item.tax_rate))];
        const taxDetails = distinctValues.map(value => ({
            value,
            tax_amount: data.sales_invoice_items
                .filter((item: InvoiceItem) => item.tax_rate === value)
                .reduce((acc: number, cur: InvoiceItem) => acc + (cur.line_total * cur.tax_rate) / (100 + cur.tax_rate), 0),
            taxable_amount: data.sales_invoice_items
                .filter((item: InvoiceItem) => item.tax_rate === value)
                .reduce((acc: number, cur: InvoiceItem) => acc + (cur.line_total - (cur.line_total * cur.tax_rate) / (100 + cur.tax_rate)), 0),
        }));
        const totalTaxAmount = data.sales_invoice_items.reduce((acc: number, cur: any) => acc + (cur.line_total * cur.tax_rate) / (100 + cur.tax_rate), 0)
        return { taxDetails, totalTaxAmount }
    }
    /**
     * Calculate total values from invoice items
     * @param data - Invoice data containing items
     * @returns Total weight, quantity, and amount
     */
    getTotalValue(data: InvoiceData): { totalWeight: number, totalNo: number, totalAmount: number } {
        let totalWeight = 0;
        let totalNo = 0;
        let totalAmount = 0;

        data.sales_invoice_items.forEach((element: InvoiceItem) => {
            if (element.weight) {
                totalWeight += element.weight;
            }
            if (element.no) {
                totalNo += element.no;
            }
            if (element.line_total) {
                totalAmount += element.line_total;
            }
        });
        return { totalWeight, totalNo, totalAmount };
    }

    /**
     * Generate mobile format HTML content for thermal/mobile printing
     * @param data - Invoice data to print
     * @returns Complete HTML string for mobile printing
     */
    getHtmlContentForMobile(data: InvoiceData): string {
        // Calculate totals and tax details
        const totalValue = this.getTotalValue(data);
        const totalTaxAmount = this.getTaxDetails(data).totalTaxAmount;

        // Generate modular sections
        const headerHtml = this.generateMobileHeader(data);
        const customerHtml = this.generateMobileCustomerInfo(data);
        const itemsHtml = this.generateMobileItemsTable(data);
        const taxDetailsHtml = this.generateMobileTaxDetails(data);
        const summaryHtml = this.generateMobileSummary(data, totalValue, totalTaxAmount);
        const balanceHtml = this.generateMobileBalanceInfo(data);
        const footerHtml = this.generateMobileFooter(data);

        // Generate complete HTML document
        return `
            <!DOCTYPE html>
            <html lang="en">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>${data.name} - ${data.date}</title>
                    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/paper-css/0.3.0/paper.css">
                    ${this.generateMobilePrintStyles()}
                </head>
                <body>
                    ${headerHtml}
                    ${customerHtml}
                    ${itemsHtml}
                    ${taxDetailsHtml}
                    ${summaryHtml}
                    ${balanceHtml}
                    ${footerHtml}
                </body>
            </html>
        `;
    }
    /**
     * Generate A5 format HTML content for web printing
     * @param data - Invoice data to print
     * @returns Complete HTML string for A5 printing
     */
    getHtmlContentForWebA5(data: InvoiceData): string {
        // Calculate totals and tax details
        const totalValue = this.getTotalValue(data);
        const totalTaxAmount = this.getTaxDetails(data).totalTaxAmount;

        // Generate modular sections
        const headerHtml = this.generateA5Header(data);
        const itemsHtml = this.generateA5ItemsTable(data);
        const summaryHtml = this.generateA5Summary(data, totalValue, totalTaxAmount);
        const footerHtml = this.generateA5Footer(data);

        // Generate complete HTML document
        return `
            <html>
                <head>
                    <title>${data.name} - ${data.date}</title>
                    <meta charset="UTF-8">
                    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/paper-css/0.3.0/paper.css">
                    ${this.generateA5PrintStyles()}
                </head>
                <body class="A5">
                    <table class="sheet padding-10mm" cellspacing="0" cellpadding="0" width="100%" style="width: 100%;">
                        ${headerHtml}
                        <tbody>
                            <tr>
                                <td>
                                    <div class="page" style="border: 0.5px solid; font-size: smaller; padding: 10px;">
                                        <!-- Items Section -->
                                        ${itemsHtml}

                                        <!-- Total Section - Moved above footer -->
                                        <div style="width: 100%; margin-top: 15px; border-top: 0.5px solid; padding-top: 10px;">
                                            <div style="display: flex; justify-content: flex-end;">
                                                <div style="width: 70%; border: 0.5px solid; padding: 10px;">
                                                    ${summaryHtml}
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Remarks Section -->
                                        ${data.remarks ? `<div style="width: 100%; margin-top: 10px; border: 0.5px solid; padding: 8px;">
                                            <strong style="font-size: 12px;">Remarks:</strong> <span style="font-size: 12px;">${data.remarks}</span>
                                        </div>` : ''}
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                        ${footerHtml}
                    </table>
                </body>
            </html>
        `;
    }


    /**
     * Generate mobile print styles optimized for thermal/mobile printing
     * @returns CSS styles string for mobile printing
     */
    private generateMobilePrintStyles(): string {
        return `
            <style>
                @media print {
                    body {
                        width: 70mm;
                        margin: 0.2mm 0.2mm 0.2mm 0.2mm;
                    }
                }

                #options {
                    align-content: center;
                    align-items: center;
                    text-align: center;
                }

                body {
                    font-family: Arial, sans-serif;
                    font-size: 12px;
                    margin: 0;
                    padding: 5px;
                }

                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 10px;
                }

                th, td {
                    padding: 2px;
                    text-align: left;
                    border-bottom: 1px solid #ddd;
                }

                th {
                    font-weight: bold;
                    background-color: #f5f5f5;
                }

                .header-section {
                    text-align: center;
                    margin-bottom: 10px;
                }

                .customer-section {
                    margin-bottom: 10px;
                }

                .totals-section {
                    margin-top: 10px;
                    border-top: 2px solid #000;
                }

                .footer-section {
                    text-align: center;
                    margin-top: 10px;
                    border-top: 1px solid #000;
                    padding-top: 5px;
                }
            </style>
        `;
    }

    /**
     * Generate A5 print styles with proper page breaks and layout
     * @returns CSS styles string for A5 printing
     */
    private generateA5PrintStyles(): string {
        return `
            <style>
                * {
                    font-family: "Arial", sans-serif;
                    box-sizing: border-box;
                }

                .sheet {
                    page-break-after: auto !important;
                }

                html, body {
                    height: 100%;
                    margin: 0 !important;
                    padding: 0 !important;
                    overflow: hidden;
                }

                .page {
                    page-break-after: avoid;
                    page-break-inside: avoid;
                    font-size: smaller;
                    min-height: 180mm;
                    padding: 5mm;
                }

                .container {
                    display: table;
                    font-size: 0;
                }

                .container:nth-child(15) {
                    page-break-after: always;
                    page-break-inside: avoid;
                }

                .header-item {
                    display: inline-block;
                    font-size: 14px !important;
                }

                div {
                    box-sizing: border-box;
                }

                /* Enhanced table styling for A5 format */
                table {
                    border-collapse: collapse;
                    width: 100%;
                    margin: 8px 0;
                }

                table th {
                    background-color: #495057 !important;
                    color: white !important;
                    font-weight: bold;
                    padding: 10px 6px;
                    text-align: center;
                    border: 1px solid #343a40;
                    font-size: 14px;
                }

                table td {
                    padding: 6px;
                    border: 1px solid #dee2e6;
                    font-size: 13px;
                    line-height: 1.3;
                }

                table tbody tr:nth-child(even) {
                    background-color: #f8f9fa !important;
                }

                table tbody tr:nth-child(odd) {
                    background-color: #ffffff !important;
                }

                @page {
                    size: A5;
                    margin: 8mm;
                }

                @media print {
                    body {
                        margin: 0;
                        zoom: 75%;
                    }

                    thead {
                        display: table-header-group;
                    }

                    tfoot {
                        display: table-footer-group;
                    }

                    table th {
                        background-color: #495057 !important;
                        color: white !important;
                        -webkit-print-color-adjust: exact;
                        print-color-adjust: exact;
                    }

                    table tbody tr:nth-child(even) {
                        background-color: #f8f9fa !important;
                        -webkit-print-color-adjust: exact;
                        print-color-adjust: exact;
                    }
                }
            </style>
        `;
    }

    /**
     * Generate CSS styles for A4 printing with proper page breaks
     * @returns CSS string for A4 format
     */
    private generateA4PrintStyles(): string {
        return `
            <style>
                * {
                    font-family: "Arial", sans-serif;
                    box-sizing: border-box;
                }

                html, body {
                    height: 100%;
                    margin: 0 !important;
                    padding: 0 !important;
                }

                @page {
                    size: A4;
                    margin: 10mm;
                }

                /* Header Section */
                .page-header {
                    width: 100%;
                    background: white;
                    border-bottom: 2px solid #000;
                    padding-bottom: 10px;
                    margin-bottom: 20px;
                }

                /* Footer Section */
                .page-footer {
                    width: 100%;
                    background: white;
                    border-top: 2px solid #000;
                    padding-top: 10px;
                    margin-top: 30px;
                    page-break-inside: avoid;
                }

                /* Content Area with proper spacing */
                .page-content {
                    width: 100%;
                    min-height: 400px;
                    padding: 10px 0;
                }

                /* Items Table with pagination support */
                .items-table {
                    width: 100%;
                    border-collapse: collapse;
                    page-break-inside: auto;
                    margin: 10px 0;
                }

                .items-table thead {
                    display: table-header-group;
                }

                .items-table tbody tr {
                    page-break-inside: avoid;
                    break-inside: avoid;
                    min-height: 40px;
                }

                .items-table tfoot {
                    display: table-footer-group;
                }

                /* Enhanced table styling for better readability */
                table {
                    border-collapse: collapse;
                    width: 100%;
                }

                table th {
                    background-color: #34495e !important;
                    color: white !important;
                    font-weight: bold;
                    padding: 12px 8px;
                    text-align: center;
                    border: 1px solid #2c3e50;
                }

                table td {
                    padding: 8px;
                    border: 1px solid #ddd;
                    font-size: 14px;
                    line-height: 1.4;
                }

                table tbody tr:nth-child(even) {
                    background-color: #f9f9f9 !important;
                }

                table tbody tr:nth-child(odd) {
                    background-color: #ffffff !important;
                }

                .container {
                    display: table;
                    font-size: 0;
                }

                .header-item {
                    display: inline-block;
                    font-size: 22px !important;
                }

                div {
                    box-sizing: border-box;
                }

                /* Page break utilities */
                .page-break {
                    page-break-before: always;
                }

                .no-break {
                    page-break-inside: avoid;
                }

                @media print {
                    body {
                        margin: 0;
                        zoom: 70%;
                        -webkit-print-color-adjust: exact;
                        print-color-adjust: exact;
                    }

                    .page-header {
                        width: 100%;
                        background: white;
                        border-bottom: 2px solid #000;
                        margin-bottom: 20px;
                    }

                    .page-footer {
                        width: 100%;
                        background: white;
                        border-top: 2px solid #000;
                        margin-top: 30px;
                        page-break-inside: avoid;
                    }

                    .page-content {
                        width: 100%;
                        padding: 10px 0;
                    }

                    .items-table thead {
                        display: table-header-group;
                    }

                    .items-table tfoot {
                        display: table-footer-group;
                    }

                    .page-break {
                        page-break-before: always;
                    }

                    .no-break {
                        page-break-inside: avoid;
                    }

                    /* Enhanced print styling for tables */
                    table th {
                        background-color: #34495e !important;
                        color: white !important;
                        -webkit-print-color-adjust: exact;
                        print-color-adjust: exact;
                    }

                    table tbody tr:nth-child(even) {
                        background-color: #f9f9f9 !important;
                        -webkit-print-color-adjust: exact;
                        print-color-adjust: exact;
                    }

                    table tbody tr:nth-child(odd) {
                        background-color: #ffffff !important;
                        -webkit-print-color-adjust: exact;
                        print-color-adjust: exact;
                    }
                }
            </style>
        `;
    }

    /**
     * Generate mobile header section with company information
     * @param data - Invoice data
     * @returns HTML string for mobile header section
     */
    private generateMobileHeader(data: InvoiceData): string {
        const companyName = localStorage.getItem("company_name") || "Company Name";
        const address = localStorage.getItem("address") || "Address";
        const contactLeft = localStorage.getItem('contact_no_left') || "";
        const contactRight = localStorage.getItem('contact_no_right') || "";
        const gstNo = localStorage.getItem('gst_no') || "";
        const fssaiNo = localStorage.getItem('fssai_no') || "";

        return `
            <div class="header-section">
                <h2 style="margin: 5px 0; font-size: 18px; font-weight: bold; color: #2c3e50;">${companyName}</h2>
                <p style="margin: 2px 0; font-size: 14px; color: #34495e;">${address}</p>
                <p style="margin: 2px 0; font-size: 12px; color: #7f8c8d;">GST: ${gstNo} | FSSAI: ${fssaiNo}</p>
                ${(contactLeft || contactRight) ? `<p style="margin: 2px 0; font-size: 12px; font-weight: bold; color: #2c3e50;">
                    Phone: ${[contactLeft, contactRight].filter(phone => phone).join(', ')}
                </p>` : ''}
                <hr style="margin: 5px 0; border: 1px solid #bdc3c7;">
                <h3 style="margin: 5px 0; font-size: 16px; font-weight: bold; color: #34495e;">${this.getInvoiceTitle(data)}</h3>
                <p style="margin: 2px 0; font-size: 14px; color: #2c3e50;">Bill No: ${data.id} | Date: ${data.date}</p>
                <hr style="margin: 5px 0; border: 1px solid #bdc3c7;">
            </div>
        `;
    }

    /**
     * Generate mobile customer information section
     * @param data - Invoice data
     * @returns HTML string for customer section
     */
    private generateMobileCustomerInfo(data: InvoiceData): string {
        return `
            <div class="customer-section">
                <p style="margin: 2px 0; font-size: 12px;"><strong>Customer:</strong> ${data.name}</p>
                <p style="margin: 2px 0; font-size: 10px;">Phone: ${data.phone_no}</p>
                <p style="margin: 2px 0; font-size: 10px;">Address: ${data.place}</p>
                <p style="margin: 2px 0; font-size: 10px;">GST: ${data.gst_no || 'N/A'}</p>
                <hr style="margin: 5px 0;">
            </div>
        `;
    }

    /**
     * Generate A5 header section with company and customer information
     * @param data - Invoice data
     * @returns HTML string for header section
     */
    private generateA5Header(data: InvoiceData): string {
        const companyLogo = localStorage.getItem("company_logo") || "";
        const companyName = localStorage.getItem("company_name") || "Company Name";
        const address = localStorage.getItem("address") || "Address";
        const contactLeft = localStorage.getItem('contact_no_left') || "";
        const contactRight = localStorage.getItem('contact_no_right') || "";
        const gstNo = localStorage.getItem('gst_no') || "";
        const fssaiNo = localStorage.getItem('fssai_no') || "";

        // Debug: Log phone numbers to console to check if they're being retrieved
        console.log('A5 Header - Contact Left:', contactLeft);
        console.log('A5 Header - Contact Right:', contactRight);

        return `
            <thead>
                <tr>
                    <td width="100%">
                        <div style="display: flex;width: 100%;height:70%;">
                            <div style="width:auto;">
                                <img src="${environment.apiUrlClean + companyLogo}" style="width: 100px;height: 120px;">
                            </div>
                            <div style="width:100%;height:100%;margin-top: -20px;text-align: center;">
                                <div style="display:flex; justify-content: space-between; width: 100%; margin-bottom: 5px;">
                                    <h5 style="padding:0; margin:0; text-align: left; font-size: 16px; font-weight: bold; color: #2c3e50;">
                                        ${contactLeft ? `Phone: ${contactLeft}` : ''}
                                    </h5>
                                    <h5 style="padding:0; margin:0; text-align: center; font-size: 20px; font-weight: bold; color: #34495e;">
                                        ${this.getInvoiceTitle(data)}
                                    </h5>
                                    <h5 style="padding:0; margin:0; text-align: right; font-size: 16px; font-weight: bold; color: #2c3e50;">
                                        ${contactRight ? `Phone: ${contactRight}` : ''}
                                    </h5>
                                </div>
                                <h4 style="padding: 5px; margin: 5px; font-weight: bold; font-size: 24px; color: #2c3e50;">${companyName}</h4>
                                <h5 style="padding: 3px; margin: 3px; font-size: 16px; font-weight: normal; color: #34495e;">${address}</h5>
                                <h5 style="font-size: 14px; font-weight: bold; margin: 5px; color: #7f8c8d;">GST NO: ${gstNo} &emsp; FSSAI NO: ${fssaiNo}</h5>
                            </div>
                        </div>
                        <div style="display: flex;">
                            <div style="width: 55%; margin-top: 3px; display: flex; flex-direction: column; border: 0.5px solid; border-bottom:none; border-right:none; justify-content:center;">
                                <div style="display:flex; flex-direction: column;">
                                    <p style="margin: 0; font-size: 18px; font-weight: 300;">To</p>
                                    <h2 style="margin: 0; font-size: 22px;">${data.name}</h2>
                                    <div style="display:flex;">
                                        <h5 style="margin: 0; font-size: 18px; font-weight: 300;">
                                            <strong>Phone:</strong> ${data.phone_no}
                                        </h5>
                                    </div>
                                    <div style="display:flex;">
                                        <h5 style="margin: 0; font-size: 18px; font-weight: 300;">
                                            <strong>Address:</strong> ${data.place}
                                        </h5>
                                    </div>
                                </div>
                            </div>
                            <div style="width: 35%; margin-top: 3px; display: flex; flex-direction: column; border: 0.5px solid; border-bottom:none; border-right:none; justify-content:center;">
                                <div style="display:flex; flex-direction: column;">
                                    <div style="display:flex;">
                                        <h5 style="margin: 0; font-size: 16px; font-weight: 300;">
                                            Place Of Supply: Tamilnadu(33)
                                        </h5>
                                    </div>
                                    <div style="display:flex;">
                                        <h5 style="margin: 0; font-size: 16px; font-weight: 300;">
                                            GST No:-${data.gst_no || ""}
                                        </h5>
                                    </div>
                                </div>
                            </div>
                            <div style="width: 30%; margin-top: 3px; padding: 8px; border: 0.5px solid; border-bottom:none; display: flex;" align="center">
                                <div class="content" style="text-align: left; width: 100%;">
                                    <p style="font-size: 18px; font-weight: 300; margin: 0;">
                                        No : ${data.id}
                                    </p>
                                    <p style="font-size: 18px; font-weight: 300; margin: 0;">
                                        Date: ${data.date}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
            </thead>
        `;
    }

    /**
     * Generate A4 header section with company and customer information
     * @param data - Invoice data
     * @returns HTML string for header section
     */
    private generateA4Header(data: InvoiceData): string {
        const companyLogo = localStorage.getItem("company_logo") || "";
        const companyName = localStorage.getItem("company_name") || "Company Name";
        const address = localStorage.getItem("address") || "Address";
        const contactLeft = localStorage.getItem('contact_no_left') || "";
        const contactRight = localStorage.getItem('contact_no_right') || "";
        const gstNo = localStorage.getItem('gst_no') || "";
        const fssaiNo = localStorage.getItem('fssai_no') || "";

        // Debug: Log phone numbers to console to check if they're being retrieved
        console.log('A4 Header - Contact Left:', contactLeft);
        console.log('A4 Header - Contact Right:', contactRight);

        const headerData = data?.headerdata?.filter(e => e.value).map(e => {
            return `<div style="display:flex;"><h4 style="margin: 0; font-size: 20px; font-weight: 300;">
                        ${e.name}:-${e.value}
                    </h4></div>`;
        }) || [];
        const headerDataHtml = headerData.join("");

        return `
            <thead>
                <tr>
                    <td width="100%">
                        <div style="display: flex;width: 100%;height:70%;">
                            <div style="width:auto;">
                                <img src="${environment.apiUrlClean + companyLogo}" style="width: 125px;height: 140px;">
                            </div>
                            <div style="width:100%;height:100%;margin-top: -25px;text-align: center;">
                                <div style="display:flex; justify-content: space-between; width: 100%; margin-bottom: 8px;">
                                    <h5 style="padding:0; margin:0; text-align: left; font-size: 18px; font-weight: bold; color: #2c3e50;">
                                        ${contactLeft ? `Phone: ${contactLeft}` : ''}
                                    </h5>
                                    <h5 style="padding:0; margin:0; text-align: center; font-size: 24px; font-weight: bold; color: #34495e;">
                                        ${this.getInvoiceTitle(data)}
                                    </h5>
                                    <h5 style="padding:0; margin:0; text-align: right; font-size: 18px; font-weight: bold; color: #2c3e50;">
                                        ${contactRight ? `Phone: ${contactRight}` : ''}
                                    </h5>
                                </div>
                                <h5 style="padding: 5px; margin: 5px; font-weight: bold; font-size: 32px; color: #2c3e50;">${companyName}</h5>
                                <h5 style="padding: 3px; margin: 3px; font-size: 18px; font-weight: normal; color: #34495e;">${address}</h5>
                                <h5 style="font-size: 16px; font-weight: bold; margin: 5px; color: #7f8c8d;">GST NO: ${gstNo} &emsp; FSSAI NO: ${fssaiNo}</h5>
                            </div>
                        </div>
                        <div style="display: flex;">
                            <div style="width: 55%; margin-top: 3px; display: flex; flex-direction: column; border: 0.5px solid; border-bottom:none; border-right:none; justify-content:center;">
                                <div style="display:flex; flex-direction: column;">
                                    <p style="margin: 0; font-size: 25px; font-weight: 300;">To</p>
                                    <h1 style="margin: 0; font-size: 30px;">${data.name}</h1>
                                    <div style="display:flex;">
                                        <h4 style="margin: 0; font-size: 25px; font-weight: 300;">
                                            <strong>Phone:</strong> ${data.phone_no}
                                        </h4>
                                    </div>
                                    <div style="display:flex;">
                                        <h4 style="margin: 0; font-size: 25px; font-weight: 300;">
                                            <strong>Address:</strong> ${data.place}
                                        </h4>
                                    </div>
                                </div>
                            </div>
                            <div style="width: 35%; margin-top: 3px; display: flex; flex-direction: column; border: 0.5px solid; border-bottom:none; border-right:none; justify-content:center;">
                                <div style="display:flex; flex-direction: column;">
                                    <div style="display:flex;">
                                        <h4 style="margin: 0; font-size: 20px; font-weight: 300;">
                                            Place Of Supply: Tamilnadu(33)
                                        </h4>
                                    </div>
                                    <div style="display:flex;">
                                        <h4 style="margin: 0; font-size: 20px; font-weight: 300;">
                                            GST No:-${data.gst_no || ""}
                                        </h4>
                                    </div>
                                    ${headerDataHtml}
                                </div>
                            </div>
                            <div style="width: 30%; margin-top: 3px; padding: 10px; border: 0.5px solid; border-bottom:none; display: flex;" align="center">
                                <div class="content" style="text-align: left; width: 100%;">
                                    <p style="font-size: 25px; font-weight: 300; margin: 0;">
                                        No : ${data.id}
                                    </p>
                                    <p style="font-size: 25px; font-weight: 300; margin: 0;">
                                        Date: ${data.date}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
            </thead>
        `;
    }

    /**
     * Generate mobile items table with compact layout
     * @param data - Invoice data
     * @returns HTML string for mobile items table
     */
    private generateMobileItemsTable(data: InvoiceData): string {
        // Generate items rows with improved two-row format for mobile
        const itemsHtml = data.sales_invoice_items.map((e: InvoiceItem, index: number) => {
            // Alternate row colors for better readability
            const rowColor = index % 2 === 0 ? '#ffffff' : '#f8f9fa';

            return `
                <tr style="background-color: ${rowColor}; border-bottom: 1px solid #dee2e6;">
                    <td style="width:60%; font-size: 14px; padding: 8px; font-weight: 600; line-height: 1.4;">${e.product_name}</td>
                    <td style="width:10%; font-size: 14px; padding: 8px; text-align: center; font-weight: 500;">${e.no}</td>
                    <td style="width:10%; font-size: 14px; padding: 8px; text-align: center; font-weight: 500;">${e.weight}</td>
                    <td style="width:20%; font-size: 14px; padding: 8px; text-align: right; font-weight: 600; color: #2c3e50;">₹${e.line_total.toFixed(2)}</td>
                </tr>
                <tr style="background-color: ${rowColor}; border-bottom: 2px solid #dee2e6; font-size: 12px; color: #6c757d;">
                    <td style="width:25%; padding: 4px 8px;">MRP: ₹${e.mrp}</td>
                    <td style="width:25%; padding: 4px 8px;">Rate: ₹${(e.rate - (e.rate * e.tax_rate) / (100 + e.tax_rate)).toFixed(2)}</td>
                    <td style="width:25%; padding: 4px 8px;">Tax: ${e.tax_rate}%</td>
                    <td style="width:25%; padding: 4px 8px; text-align: right;">Tax: ₹${((e.line_total * e.tax_rate) / (100 + e.tax_rate)).toFixed(2)}</td>
                </tr>
            `;
        }).join('');

        // Generate totals
        const totalValue = this.getTotalValue(data);

        return `
            <table style="width: 100%; border-collapse: collapse; border: 2px solid #495057; margin: 10px 0;">
                <thead>
                    <tr style="background-color: #495057; color: white; border-bottom: 2px solid #343a40;">
                        <th style="width:60%; font-size: 16px; padding: 12px 8px; text-align: center; font-weight: bold;">Item Description</th>
                        <th style="width:10%; font-size: 16px; padding: 12px 8px; text-align: center; font-weight: bold;">Box</th>
                        <th style="width:10%; font-size: 16px; padding: 12px 8px; text-align: center; font-weight: bold;">Pcs</th>
                        <th style="width:20%; font-size: 16px; padding: 12px 8px; text-align: center; font-weight: bold;">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    ${itemsHtml}
                    <tr style="border-top: 3px solid #495057; background-color: #e9ecef; font-weight: bold;">
                        <td style="width:60%; font-size: 16px; padding: 10px 8px; color: #495057;">TOTAL</td>
                        <td style="width:10%; font-size: 16px; padding: 10px 8px; text-align: center; color: #495057;">${totalValue.totalNo}</td>
                        <td style="width:10%; font-size: 16px; padding: 10px 8px; text-align: center; color: #495057;">${totalValue.totalWeight}</td>
                        <td style="width:20%; font-size: 16px; padding: 10px 8px; text-align: right; color: #2c3e50; font-weight: bold;">₹${totalValue.totalAmount.toFixed(2)}</td>
                    </tr>
                </tbody>
            </table>
        `;
    }

    /**
     * Generate mobile tax details section
     * @param data - Invoice data
     * @returns HTML string for tax details
     */
    private generateMobileTaxDetails(data: InvoiceData): string {
        const taxDetails = this.getTaxDetails(data).taxDetails;

        const taxRows = taxDetails.map((e: any) => {
            return `
                <tr>
                    <th style="width:25%">${e.value}%</th>
                    <td style="width:25%">${e.taxable_amount.toFixed(2)}</td>
                    <td style="width:25%">${(e.tax_amount / 2).toFixed(2)}</td>
                    <td style="width:25%">${(e.tax_amount / 2).toFixed(2)}</td>
                </tr>
            `;
        }).join('');

        return `
            <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                <thead>
                    <tr>
                        <th style="width:25%">Tax%</th>
                        <th style="width:25%">Taxable</th>
                        <th style="width:25%">CGST</th>
                        <th style="width:25%">SGST</th>
                    </tr>
                </thead>
                <tbody>
                    ${taxRows}
                </tbody>
            </table>
        `;
    }

    /**
     * Generate A5 items table with proper formatting and pagination
     * @param data - Invoice data
     * @returns HTML string for items table
     */
    private generateA5ItemsTable(data: InvoiceData): string {
        const itemRows = data.sales_invoice_items.map((item: InvoiceItem, index: number) => {
            // Alternate row colors for better readability
            const rowColor = index % 2 === 0 ? '#ffffff' : '#f8f9fa';

            return `
                <tr style="border-bottom: 1px solid #dee2e6; background-color: ${rowColor};">
                    <td style="width:6%; font-size: 13px; padding: 6px; text-align: center; border-right: 1px solid #dee2e6; font-weight: 500;">${index + 1}</td>
                    <td style="width:40%; font-size: 13px; padding: 6px; text-align: left; border-right: 1px solid #dee2e6; font-weight: 500; line-height: 1.3;">${item.product_name}</td>
                    <td style="width:9%; font-size: 13px; padding: 6px; text-align: right; border-right: 1px solid #dee2e6; font-weight: 500;">₹${item.mrp}</td>
                    <td style="width:9%; font-size: 13px; padding: 6px; text-align: right; border-right: 1px solid #dee2e6; font-weight: 500;">₹${item.rate.toFixed(2)}</td>
                    <td style="width:7%; font-size: 13px; padding: 6px; text-align: center; border-right: 1px solid #dee2e6; font-weight: 500;">${item.no}</td>
                    <td style="width:7%; font-size: 13px; padding: 6px; text-align: center; border-right: 1px solid #dee2e6; font-weight: 500;">${item.weight}</td>
                    <td style="width:8%; font-size: 13px; padding: 6px; text-align: center; border-right: 1px solid #dee2e6; font-weight: 500;">${item.tax_rate}%</td>
                    <td style="width:14%; font-size: 13px; padding: 6px; text-align: right; font-weight: 600; color: #2c3e50;">₹${item.line_total.toFixed(2)}</td>
                </tr>
            `;
        }).join('');

        // Generate totals row
        const totalValue = this.getTotalValue(data);
        const totalRow = `
            <tr style="background-color: #e9ecef; border-top: 2px solid #495057; font-weight: bold;">
                <td colspan="2" style="font-size: 14px; padding: 8px; text-align: right; border-right: 1px solid #495057; color: #495057;">TOTAL</td>
                <td style="width:9%; font-size: 14px; padding: 8px; text-align: center; border-right: 1px solid #495057; color: #495057;">-</td>
                <td style="width:9%; font-size: 14px; padding: 8px; text-align: center; border-right: 1px solid #495057; color: #495057;">-</td>
                <td style="width:7%; font-size: 14px; padding: 8px; text-align: center; border-right: 1px solid #495057; color: #495057;">${totalValue.totalNo}</td>
                <td style="width:7%; font-size: 14px; padding: 8px; text-align: center; border-right: 1px solid #495057; color: #495057;">${totalValue.totalWeight}</td>
                <td style="width:8%; font-size: 14px; padding: 8px; text-align: center; border-right: 1px solid #495057; color: #495057;">-</td>
                <td style="width:14%; font-size: 14px; padding: 8px; text-align: right; color: #2c3e50; font-weight: bold;">₹${totalValue.totalAmount.toFixed(2)}</td>
            </tr>
        `;

        return `
            <table style="width: 100%; border-collapse: collapse; border: 2px solid #495057; margin: 8px 0;">
                <thead>
                    <tr style="background-color: #495057; color: white; border-bottom: 2px solid #343a40;">
                        <th style="width:6%; font-size: 14px; padding: 10px 6px; text-align: center; border-right: 1px solid #343a40; font-weight: bold;">Sr</th>
                        <th style="width:40%; font-size: 14px; padding: 10px 6px; text-align: center; border-right: 1px solid #343a40; font-weight: bold;">Item Description</th>
                        <th style="width:9%; font-size: 14px; padding: 10px 6px; text-align: center; border-right: 1px solid #343a40; font-weight: bold;">MRP</th>
                        <th style="width:9%; font-size: 14px; padding: 10px 6px; text-align: center; border-right: 1px solid #343a40; font-weight: bold;">Rate</th>
                        <th style="width:7%; font-size: 14px; padding: 10px 6px; text-align: center; border-right: 1px solid #343a40; font-weight: bold;">Box</th>
                        <th style="width:7%; font-size: 14px; padding: 10px 6px; text-align: center; border-right: 1px solid #343a40; font-weight: bold;">Pcs</th>
                        <th style="width:8%; font-size: 14px; padding: 10px 6px; text-align: center; border-right: 1px solid #343a40; font-weight: bold;">Tax%</th>
                        <th style="width:14%; font-size: 14px; padding: 10px 6px; text-align: center; font-weight: bold;">Total ₹</th>
                    </tr>
                </thead>
                <tbody>
                    ${itemRows}
                    ${totalRow}
                </tbody>
            </table>
        `;
    }

    /**
     * Generate A4 items table with proper formatting and pagination
     * @param data - Invoice data
     * @returns HTML string for items table
     */
    private generateA4ItemsTable(data: InvoiceData): string {
        const itemRows = data.sales_invoice_items.map((item: InvoiceItem, index: number) => {
            // Alternate row colors for better readability
            const rowColor = index % 2 === 0 ? '#ffffff' : '#f9f9f9';

            return `
                <tr style="border-bottom: 1px solid #ddd; background-color: ${rowColor};">
                    <td style="width:5%; font-size: 14px; padding: 8px; text-align: center; border-right: 1px solid #ddd; font-weight: 500;">${index + 1}</td>
                    <td style="width:35%; font-size: 14px; padding: 8px; text-align: left; border-right: 1px solid #ddd; font-weight: 500; line-height: 1.4;">${item.product_name}</td>
                    <td style="width:8%; font-size: 14px; padding: 8px; text-align: right; border-right: 1px solid #ddd; font-weight: 500;">₹${item.mrp}</td>
                    <td style="width:8%; font-size: 14px; padding: 8px; text-align: right; border-right: 1px solid #ddd; font-weight: 500;">₹${item.rate.toFixed(2)}</td>
                    <td style="width:6%; font-size: 14px; padding: 8px; text-align: center; border-right: 1px solid #ddd; font-weight: 500;">${item.no}</td>
                    <td style="width:6%; font-size: 14px; padding: 8px; text-align: center; border-right: 1px solid #ddd; font-weight: 500;">${item.weight}</td>
                    <td style="width:8%; font-size: 14px; padding: 8px; text-align: center; border-right: 1px solid #ddd; font-weight: 500;">${item.tax_rate}%</td>
                    <td style="width:10%; font-size: 14px; padding: 8px; text-align: right; border-right: 1px solid #ddd; font-weight: 500;">₹${((item.line_total * item.tax_rate) / (100 + item.tax_rate)).toFixed(2)}</td>
                    <td style="width:14%; font-size: 14px; padding: 8px; text-align: right; font-weight: 600; color: #2c3e50;">₹${item.line_total.toFixed(2)}</td>
                </tr>
            `;
        }).join('');

        return `
            <table style="width: 100%; border-collapse: collapse; border: 2px solid #34495e; margin: 10px 0;">
                <thead>
                    <tr style="background-color: #34495e; color: white; border-bottom: 2px solid #2c3e50;">
                        <th style="width:5%; font-size: 16px; padding: 12px 8px; text-align: center; border-right: 1px solid #2c3e50; font-weight: bold;">Sr</th>
                        <th style="width:35%; font-size: 16px; padding: 12px 8px; text-align: center; border-right: 1px solid #2c3e50; font-weight: bold;">Item Description</th>
                        <th style="width:8%; font-size: 16px; padding: 12px 8px; text-align: center; border-right: 1px solid #2c3e50; font-weight: bold;">MRP</th>
                        <th style="width:8%; font-size: 16px; padding: 12px 8px; text-align: center; border-right: 1px solid #2c3e50; font-weight: bold;">Rate</th>
                        <th style="width:6%; font-size: 16px; padding: 12px 8px; text-align: center; border-right: 1px solid #2c3e50; font-weight: bold;">Box</th>
                        <th style="width:6%; font-size: 16px; padding: 12px 8px; text-align: center; border-right: 1px solid #2c3e50; font-weight: bold;">Pcs</th>
                        <th style="width:8%; font-size: 16px; padding: 12px 8px; text-align: center; border-right: 1px solid #2c3e50; font-weight: bold;">Tax%</th>
                        <th style="width:10%; font-size: 16px; padding: 12px 8px; text-align: center; border-right: 1px solid #2c3e50; font-weight: bold;">Tax ₹</th>
                        <th style="width:14%; font-size: 16px; padding: 12px 8px; text-align: center; font-weight: bold;">Total ₹</th>
                    </tr>
                </thead>
                <tbody>
                    ${itemRows}
                </tbody>
            </table>
        `;
    }

    /**
     * Generate mobile summary section with totals and rounding
     * @param data - Invoice data
     * @param totalValue - Calculated totals
     * @param totalTaxAmount - Total tax amount
     * @returns HTML string for mobile summary section
     */
    private generateMobileSummary(data: InvoiceData, totalValue: any, totalTaxAmount: number): string {
        // Generate billing summary with rounding support
        let billingSummary = `
            <tr>
                <th style="width:60%">Subtotal</th>
                <td style="width:40%">₹${(totalValue.totalAmount - totalTaxAmount).toFixed(2)}</td>
            </tr>
            <tr>
                <th style="width:60%">Tax Total</th>
                <td style="width:40%">₹${totalTaxAmount.toFixed(2)}</td>
            </tr>
            <tr>
                <th style="width:60%">Gross Total</th>
                <td style="width:40%">₹${(data.gross_total || data.bill_amount).toFixed(2)}</td>
            </tr>`;

        // Add rounding line if enabled and adjustment is not zero
        if (data.rounding_enabled && data.rounding_adjustment !== 0) {
            billingSummary += `
                <tr>
                    <th style="width:60%">Rounding Off</th>
                    <td style="width:40%">₹${(data.rounding_adjustment > 0 ? '+' : '') + data.rounding_adjustment.toFixed(2)}</td>
                </tr>`;
        }

        billingSummary += `
            <tr style="border-top: 2px solid #000; font-weight: bold; font-size: 14px;">
                <th style="width:60%">Net Amount</th>
                <td style="width:40%">₹${data.bill_amount.toFixed(2)}</td>
            </tr>`;

        return `
            <div class="totals-section">
                <table style="width: 100%; border-collapse: collapse;">
                    <tbody>
                        ${billingSummary}
                    </tbody>
                </table>
            </div>
        `;
    }

    /**
     * Generate mobile balance information section
     * @param data - Invoice data
     * @returns HTML string for balance section
     */
    private generateMobileBalanceInfo(data: InvoiceData): string {
        return `
            <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                <tbody>
                    <tr>
                        <th style="width:60%">Opening Balance</th>
                        <td style="width:40%">₹${data.previous_balance.toFixed(2)}</td>
                    </tr>
                    <tr>
                        <th style="width:60%">Bill Amount</th>
                        <td style="width:40%">₹${data.bill_amount.toFixed(2)}</td>
                    </tr>
                    <tr>
                        <th style="width:60%">Received Amount</th>
                        <td style="width:40%">₹${data.received_amount.toFixed(2)}</td>
                    </tr>
                    <tr style="border-top: 2px solid #000; font-weight: bold;">
                        <th style="width:60%">Closing Balance</th>
                        <td style="width:40%">₹${data.current_balance.toFixed(2)}</td>
                    </tr>
                </tbody>
            </table>
        `;
    }

    /**
     * Generate staff information section for bills
     * @param data - Invoice data
     * @returns HTML string for staff information
     */
    private generateStaffInfo(data: InvoiceData): string {
        let staffInfo = '';

        if (data.billed_by || data.delivery_by || data.collected_by) {
            staffInfo += '<div style="margin: 8px 0; font-size: 12px;">';

            if (data.billed_by) {
                staffInfo += `<p style="margin: 2px; text-align: center;"><strong>Billed by:</strong> ${data.billed_by}</p>`;
            }

            if (data.delivery_by) {
                staffInfo += `<p style="margin: 2px; text-align: center;"><strong>Delivery by:</strong> ${data.delivery_by}</p>`;
            }

            if (data.collected_by) {
                staffInfo += `<p style="margin: 2px; text-align: center;"><strong>Collected by:</strong> ${data.collected_by}</p>`;
            }

            staffInfo += '</div>';
        }

        return staffInfo;
    }

    /**
     * Generate staff information section for A5 format
     * @param data - Invoice data
     * @returns HTML string for staff information in A5 format
     */
    private generateStaffInfoForA5(data: InvoiceData): string {
        let staffInfo = '';

        if (data.billed_by || data.delivery_by || data.collected_by) {
            if (data.billed_by) {
                staffInfo += `<div style="display: flex;">
                    <p style="margin: 2px; font-size: 12px; text-align: left; padding-left: 8px;">
                        <strong>Billed by:</strong> ${data.billed_by}</p>
                </div>`;
            }

            if (data.delivery_by) {
                staffInfo += `<div style="display: flex;">
                    <p style="margin: 2px; font-size: 12px; text-align: left; padding-left: 8px;">
                        <strong>Delivery by:</strong> ${data.delivery_by}</p>
                </div>`;
            }

            if (data.collected_by) {
                staffInfo += `<div style="display: flex;">
                    <p style="margin: 2px; font-size: 12px; text-align: left; padding-left: 8px;">
                        <strong>Collected by:</strong> ${data.collected_by}</p>
                </div>`;
            }
        }

        return staffInfo;
    }

    /**
     * Generate staff information section for A4 format
     * @param data - Invoice data
     * @returns HTML string for staff information in A4 format
     */
    private generateStaffInfoForA4(data: InvoiceData): string {
        let staffInfo = '';

        if (data.billed_by || data.delivery_by || data.collected_by) {
            staffInfo += '<div style="margin-bottom: 15px; font-size: 14px; display: flex; justify-content: space-around; flex-wrap: wrap;">';

            if (data.billed_by) {
                staffInfo += `<div style="margin: 5px;"><strong>Billed by:</strong> ${data.billed_by}</div>`;
            }

            if (data.delivery_by) {
                staffInfo += `<div style="margin: 5px;"><strong>Delivery by:</strong> ${data.delivery_by}</div>`;
            }

            if (data.collected_by) {
                staffInfo += `<div style="margin: 5px;"><strong>Collected by:</strong> ${data.collected_by}</div>`;
            }

            staffInfo += '</div>';
        }

        return staffInfo;
    }

    /**
     * Generate mobile footer section
     * @param data - Invoice data
     * @returns HTML string for footer section
     */
    private generateMobileFooter(data: InvoiceData): string {
        return `
            <div class="footer-section">
                <p style="margin: 3px; text-align: center;">*******************************</p>
                ${this.generateStaffInfo(data)}
                <p style="margin: 3px; text-align: center;">Thank You</p>
            </div>
        `;
    }

    /**
     * Generate A5 summary section with totals and rounding
     * @param data - Invoice data
     * @param totalValue - Calculated totals
     * @param totalTaxAmount - Total tax amount
     * @returns HTML string for summary section
     */
    private generateA5Summary(data: InvoiceData, totalValue: any, totalTaxAmount: number): string {
        let customBillingItems = "";

        // Generate custom billing items (discounts, etc.)
        data.metadata.filter((item: any) => item.value).forEach((element: any) => {
            switch (element.fieldType) {
                case 'discount':
                    customBillingItems += `<div style="display: flex;">
                        <p style="margin: 3px; font-size: 14px; width: 50%; text-align: right; padding-left: 8px;">
                            ${element.label}</p>
                        <p style="margin: 3px; font-size: 14px; width: 50%; text-align: left; padding-right: 8px;text-align:right;">
                            ${element.value.toFixed(2)} </p>
                    </div>`;
                    break;
                default:
                    customBillingItems += `<div style="display: flex;">
                        <p style="margin: 3px; font-size: 14px; width: 50%; text-align: right; padding-left: 8px;">
                            ${element.label}</p>
                        <p style="margin: 3px; font-size: 14px; width: 50%; text-align: left; padding-right: 8px;text-align:right;">
                            ${element.value} </p>
                    </div>`;
                    break;
            }
        });

        // Generate tax details
        const taxDetails = this.getTaxDetails(data).taxDetails;
        const taxItems = taxDetails.map((e: any) => {
            return `<div style="display: flex;">
                <p style="margin: 2px; font-size: 12px; width: 20%; text-align: right; padding-left: 8px;">
                    ${e.value}</p>
                <p style="margin: 3px; font-size: 12px; width: 40%; text-align: right; padding-left: 8px;">
                    ${e.taxable_amount.toFixed(2)}</p>
                <p style="margin: 3px; font-size: 12px; width: 30%; text-align: right; padding-left: 8px;">
                    ${(e.tax_amount / 2).toFixed(2)}</p>
                <p style="margin: 3px; font-size: 12px; width: 30%; text-align: right; padding-left: 8px;">
                    ${(e.tax_amount / 2).toFixed(2)}</p>
            </div>`;
        }).join('');

        // Generate billing summary with rounding support
        let billingSummary = `
            <div style="display: flex;">
                <p style="margin: 3px; font-size: 14px; width: 50%; text-align: right; padding-left: 8px;">
                    Subtotal</p>
                <p style="margin: 3px; font-size: 14px; width: 50%; text-align: left; padding-right: 8px;text-align:right;">
                    ${(totalValue.totalAmount - totalTaxAmount).toFixed(2)} </p>
            </div>
            <div style="display: flex;">
                <p style="margin: 3px; font-size: 14px; width: 50%; text-align: right; padding-left: 8px;">
                    Tax Total</p>
                <p style="margin: 3px; font-size: 14px; width: 50%; text-align: left; padding-right: 8px;text-align:right;">
                    ${totalTaxAmount.toFixed(2)} </p>
            </div>
            <div style="display: flex;">
                <p style="margin: 3px; font-size: 14px; width: 50%; text-align: right; padding-left: 8px;">
                    Gross Total</p>
                <p style="margin: 3px; font-size: 14px; width: 50%; text-align: left; padding-right: 8px;text-align:right;">
                    ${(data.gross_total || data.bill_amount).toFixed(2)} </p>
            </div>`;

        // Add rounding line if enabled and adjustment is not zero
        if (data.rounding_enabled && data.rounding_adjustment !== 0) {
            billingSummary += `
                <div style="display: flex;">
                    <p style="margin: 3px; font-size: 14px; width: 50%; text-align: right; padding-left: 8px;">
                        Rounding Off</p>
                    <p style="margin: 3px; font-size: 14px; width: 50%; text-align: left; padding-right: 8px;text-align:right;">
                        ${(data.rounding_adjustment > 0 ? '+' : '') + data.rounding_adjustment.toFixed(2)} </p>
                </div>`;
        }

        billingSummary += `
            <div style="display: flex;">
                <p style="margin: 3px; font-size: 16px; width: 50%; text-align: right; padding-left: 8px;">
                    <strong>Net Amount</strong></p>
                <p style="margin: 3px; font-size: 16px; width: 50%; text-align: left; padding-right: 8px;text-align:right;">
                    <strong>${data.bill_amount.toFixed(2)}</strong> </p>
            </div>`;

        return `
            <div style="display: flex; flex-direction: column; width: 40%; border: 0.5px solid; padding-top: 5px;border-top:none;">
                <div style="display: flex;border-bottom:0.5px solid;">
                    <p style="margin: 2px; font-size: 12px; width: 20%; text-align: right; padding-left: 8px;">
                        Tax Rate</p>
                    <p style="margin: 3px; font-size: 12px; width: 40%; text-align: right; padding-left: 8px;">
                        Taxable Amount</p>
                    <p style="margin: 3px; font-size: 12px; width: 30%; text-align: right; padding-left: 8px;">
                        CGST</p>
                    <p style="margin: 3px; font-size: 12px; width: 30%; text-align: right; padding-left: 8px;">
                        SGST</p>
                </div>
                ${taxItems}
            </div>
            <div style="display: flex; flex-direction: column; width: 30%; border: 0.5px solid; padding-top: 5px;border-top:none;border-left:none;">
                ${customBillingItems}
                ${billingSummary}
                <div style="display: flex;">
                    <p style="margin: 3px; font-size: 14px; text-align: center; padding-left: 8px; border-top: 0.5px solid;">
                        Authorized Sign</p>
                </div>
            </div>
        `;
    }

    /**
     * Generate A5 footer section with balance information
     * @param data - Invoice data
     * @returns HTML string for footer section
     */
    private generateA5Footer(data: InvoiceData): string {
        return `
            <tfoot>
                <tr>
                    <td>
                        <div id="page-footer">
                            <div style="display: flex;">
                                <div style="display: flex; flex-direction: column; width: 30%; border: 0.5px solid; padding-top: 5px;border-top:none;border-right:none;">
                                    <div style="display: flex;">
                                        <p style="margin: 3px; font-size: 14px; width: 50%; text-align: right; padding-left: 8px;">
                                            Opening Balance</p>
                                        <p style="margin: 3px; font-size: 14px;font-weight:900; width: 50%; text-align: left; padding-right: 8px;text-align:right;">
                                            ${data.previous_balance.toFixed(2)} </p>
                                    </div>
                                    <div style="display: flex;">
                                        <p style="margin: 3px; font-size: 14px; width: 50%; text-align: right; padding-left: 8px;">
                                            Bill value</p>
                                        <p style="margin: 3px; font-size: 14px; font-weight:900; width: 50%; text-align: left; padding-right: 8px;text-align:right;">
                                            ${data.bill_amount.toFixed(2)}</p>
                                    </div>
                                    <div style="display: flex;">
                                        <p style="margin: 3px; font-size: 14px; width: 50%; text-align: right; padding-left: 8px;">
                                            Received Amount</p>
                                        <p style="margin: 3px; font-size: 14px;font-weight:900; width: 50%; text-align: left; padding-right: 8px;text-align:right;">
                                            ${data.received_amount.toFixed(2)}</p>
                                    </div>
                                    <div style="display: flex;">
                                        <p style="margin: 3px; font-size: 14px; width: 50%; text-align: right; padding-left: 8px;">
                                            Closing Balance</p>
                                        <p style="margin: 3px; font-size: 16px;font-weight:900; width: 50%; text-align: left; padding-right: 8px;text-align:right;">
                                            <b>${data.current_balance.toFixed(2)}</b> </p>
                                    </div>
                                </div>
                                <div style="display: flex; flex-direction: column; width: 70%; border: 0.5px solid; padding-top: 5px;border-top:none;">
                                    ${this.generateStaffInfoForA5(data)}
                                    <div style="display: flex;">
                                        <p style="margin: 3px; font-size: 14px; text-align: center; padding-left: 8px; border-top: 0.5px solid;">
                                            Authorized Sign</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
            </tfoot>
        `;
    }

    /**
     * Generate A4 tax details section with GST breakdown (bottom layout)
     * @param data - Invoice data
     * @returns HTML string for tax details table
     */
    private generateA4TaxDetails(data: InvoiceData): string {
        const taxDetails = this.getTaxDetails(data).taxDetails;

        const taxRows = taxDetails.map((e: any) => {
            return `
                <tr>
                    <td style="width:20%; font-size: 14px; padding: 5px; text-align: center; border: 0.5px solid;">${e.value}%</td>
                    <td style="width:30%; font-size: 14px; padding: 5px; text-align: right; border: 0.5px solid;">${e.taxable_amount.toFixed(2)}</td>
                    <td style="width:25%; font-size: 14px; padding: 5px; text-align: right; border: 0.5px solid;">${(e.tax_amount / 2).toFixed(2)}</td>
                    <td style="width:25%; font-size: 14px; padding: 5px; text-align: right; border: 0.5px solid;">${(e.tax_amount / 2).toFixed(2)}</td>
                </tr>
            `;
        }).join('');

        return `
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background-color: #f5f5f5;">
                        <th style="width:20%; font-size: 14px; padding: 5px; text-align: center; border: 0.5px solid; font-weight: bold;">Tax Rate</th>
                        <th style="width:30%; font-size: 14px; padding: 5px; text-align: center; border: 0.5px solid; font-weight: bold;">Taxable Amount</th>
                        <th style="width:25%; font-size: 14px; padding: 5px; text-align: center; border: 0.5px solid; font-weight: bold;">CGST</th>
                        <th style="width:25%; font-size: 14px; padding: 5px; text-align: center; border: 0.5px solid; font-weight: bold;">SGST</th>
                    </tr>
                </thead>
                <tbody>
                    ${taxRows}
                </tbody>
            </table>
        `;
    }

    /**
     * Generate A4 summary section with totals and rounding
     * @param data - Invoice data
     * @param totalValue - Calculated totals
     * @param totalTaxAmount - Total tax amount
     * @returns HTML string for summary section
     */
    private generateA4Summary(data: InvoiceData, totalValue: any, totalTaxAmount: number): string {
        let customBillingItems = "";

        // Process metadata for custom billing items
        data?.metadata?.filter(item => item.value).forEach((element: any) => {
            switch (element.fieldType) {
                case 'discount':
                    customBillingItems += `<div style="display: flex;">
                                <p style="margin: 5px; font-size: 18px; width: 50%; text-align: right; padding-left: 10px;">
                                    ${element.displayName}(${element.operation == "add" ? "+" : "-"}) &emsp; (${element.percentage}%)</p>
                                <p style="margin: 5px; font-size: 18px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;">
                                    : ${element.value} </p>
                            </div>`;
                    break;
                default:
                    customBillingItems += `<div style="display: flex;">
                                <p style="margin: 5px; font-size: 18px; width: 50%; text-align: right; padding-left: 10px;">
                                    ${element.displayName}(${element.operation == "add" ? "+" : "-"})</p>
                                <p style="margin: 5px; font-size: 18px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;">
                                    : ${element.value} </p>
                            </div>`;
                    break;
            }
        });

        // Generate totals section with rounding support
        return `
            <div style="display: flex;">
                <p style="margin: 5px; font-size: 18px; width: 50%; text-align: right; padding-left: 10px;">
                    Subtotal</p>
                <p style="margin: 5px; font-size: 18px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                    ${(totalValue.totalAmount - totalTaxAmount).toFixed(2)} </p>
            </div>
            <div style="display: flex;">
                <p style="margin: 5px; font-size: 18px; width: 50%; text-align: right; padding-left: 10px;">
                    Tax Amount</p>
                <p style="margin: 5px; font-size: 18px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                    ${totalTaxAmount.toFixed(2)} </p>
            </div>
            <div style="display: flex;">
                <p style="margin: 5px; font-size: 18px; width: 50%; text-align: right; padding-left: 10px;">
                    Gross Total</p>
                <p style="margin: 5px; font-size: 18px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                    ${(data.gross_total || data.bill_amount).toFixed(2)} </p>
            </div>
            ${customBillingItems}
            ${data.rounding_enabled && data.rounding_adjustment !== 0 ? `
            <div style="display: flex;">
                <p style="margin: 5px; font-size: 18px; width: 50%; text-align: right; padding-left: 10px;">
                    Rounding Off</p>
                <p style="margin: 5px; font-size: 18px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                    ${data.rounding_adjustment > 0 ? '+' : ''}${data.rounding_adjustment.toFixed(2)} </p>
            </div>` : ''}
            <div style="display: flex;">
                <p style="margin: 5px; font-size: 18px; width: 50%; text-align: right; padding-left: 10px;">
                    Net Amount</p>
                <p style="margin: 5px; font-size: 18px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                    ${data.bill_amount.toFixed(2)}</p>
            </div>
        `;
    }

    /**
     * Generate A4 footer section with balance information
     * @param data - Invoice data
     * @returns HTML string for footer section
     */
    private generateA4Footer(data: InvoiceData): string {
        return `
            <tfoot>
                <tr>
                    <td>
                        <div id="page-footer">
                            <div style="display: flex;">
                                <div style="display: flex; flex-direction: column; width: 30%; border: 0.5px solid; padding-top: 5px;border-top:none;border-right:none;">
                                    <div style="display: flex;">
                                        <p style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                            Opening Balance</p>
                                        <p style="margin: 5px; font-size: 20px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                                            ${data.previous_balance.toFixed(2)} </p>
                                    </div>
                                    <div style="display: flex;">
                                        <p style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                            Bill value</p>
                                        <p style="margin: 5px; font-size: 20px; font-weight:900;font-weight:1200em;width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                                            ${data.bill_amount.toFixed(2)}</p>
                                    </div>
                                    <div style="display: flex;">
                                        <p style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                            Received Amount</p>
                                        <p style="margin: 5px; font-size: 20px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                                            ${data.received_amount.toFixed(2)}</p>
                                    </div>
                                    <div style="display: flex;">
                                        <p style="margin: 5px; font-size: 20px; width: 50%; text-align: right; padding-left: 10px;">
                                            Closing Balance</p>
                                        <p style="margin: 5px; font-size: 25px;font-weight:900; width: 50%; text-align: left; padding-right: 10px;text-align:right;">
                                            ${data.current_balance.toFixed(2)}</p>
                                    </div>
                                </div>
                                <div style="display: flex; flex-direction: column; width: 70%; border: 0.5px solid; padding-top: 5px;border-top:none;">
                                    <div style="display: flex;">
                                        <p style="margin: 5px; font-size: 20px; font-weight: 300; text-align: center; padding-left: 10px; border-top: 0.5px solid;">
                                            Authorized Sign</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
            </tfoot>
        `;
    }

    /**
     * Generate A4 format HTML content for web printing
     * @param data - Invoice data to print
     * @returns Complete HTML string for A4 printing
     */
    getHtmlContentForWebA4(data: InvoiceData): string {
        // Calculate totals and tax details
        const totalValue = this.getTotalValue(data);
        const totalTaxAmount = this.getTaxDetails(data).totalTaxAmount;

        // Generate modular sections
        const headerHtml = this.generateA4Header(data);
        const itemsHtml = this.generateA4ItemsTable(data);
        const taxDetailsHtml = this.generateA4TaxDetails(data);
        const summaryHtml = this.generateA4Summary(data, totalValue, totalTaxAmount);

        // Generate complete HTML document with improved layout
        return `
            <!DOCTYPE html>
            <html>
                <head>
                    <title>${data.name} - ${data.date}</title>
                    <meta charset="UTF-8">
                    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/paper-css/0.3.0/paper.css">
                    ${this.generateA4PrintStyles()}
                </head>
                <body>
                    <!-- Header Section -->
                    <div class="page-header">
                        ${headerHtml.replace('<thead>', '').replace('</thead>', '').replace('<tr>', '').replace('</tr>', '')}
                    </div>

                    <!-- Page Content -->
                    <div class="page-content">
                        <!-- Items Section with pagination support -->
                        ${itemsHtml}

                        <!-- Items Total Summary Row -->
                        <div style="width: 100%; margin-top: 10px;">
                            <table style="width: 100%; border-collapse: collapse; border: 0.5px solid;">
                                <tbody>
                                    <tr style="background-color: #f9f9f9; font-weight: bold;">
                                        <td colspan="4" style="font-size: 16px; padding: 8px; text-align: right; border-right: 0.5px solid;">Total</td>
                                        <td style="width:6%; font-size: 16px; padding: 8px; text-align: center; border-right: 0.5px solid;">${this.getTotalValue(data).totalNo}</td>
                                        <td style="width:6%; font-size: 16px; padding: 8px; text-align: center; border-right: 0.5px solid;">${this.getTotalValue(data).totalWeight}</td>
                                        <td style="width:8%; font-size: 16px; padding: 8px; text-align: center; border-right: 0.5px solid;"></td>
                                        <td style="width:10%; font-size: 16px; padding: 8px; text-align: right; border-right: 0.5px solid;">${this.getTaxDetails(data).totalTaxAmount.toFixed(2)}</td>
                                        <td style="width:14%; font-size: 16px; padding: 8px; text-align: right;">${this.getTotalValue(data).totalAmount.toFixed(2)}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Total Section - Moved above footer -->
                        <div style="width: 100%; margin-top: 20px; display: flex; justify-content: space-between;">
                            <!-- Left Section - Balance Information -->
                            <div style="width: 32%; border: 0.5px solid; padding: 10px;">
                                <h4 style="margin: 0 0 10px 0; font-size: 16px; text-align: center; border-bottom: 1px solid; padding-bottom: 5px;">Balance Information</h4>
                                <div style="display: flex; justify-content: space-between; margin: 5px 0;">
                                    <span style="font-size: 14px;">Opening Balance</span>
                                    <span style="font-size: 14px; font-weight: bold;">₹${data.previous_balance.toFixed(2)}</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin: 5px 0;">
                                    <span style="font-size: 14px;">Bill Amount</span>
                                    <span style="font-size: 14px; font-weight: bold;">₹${data.bill_amount.toFixed(2)}</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin: 5px 0;">
                                    <span style="font-size: 14px;">Received Amount</span>
                                    <span style="font-size: 14px; font-weight: bold;">₹${data.received_amount.toFixed(2)}</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin: 5px 0; border-top: 1px solid; padding-top: 5px;">
                                    <span style="font-size: 16px; font-weight: bold;">Closing Balance</span>
                                    <span style="font-size: 16px; font-weight: bold;">₹${data.current_balance.toFixed(2)}</span>
                                </div>
                            </div>

                            <!-- Center Section - Tax Calculations -->
                            <div style="width: 32%; border: 0.5px solid; border-left: none; padding: 10px;">
                                <h4 style="margin: 0 0 10px 0; font-size: 16px; text-align: center; border-bottom: 1px solid; padding-bottom: 5px;">Tax Details</h4>
                                ${taxDetailsHtml}
                            </div>

                            <!-- Right Section - Invoice Summary -->
                            <div style="width: 32%; border: 0.5px solid; border-left: none; padding: 10px;">
                                <h4 style="margin: 0 0 10px 0; font-size: 16px; text-align: center; border-bottom: 1px solid; padding-bottom: 5px;">Invoice Summary</h4>
                                ${summaryHtml}
                            </div>
                        </div>

                        <!-- Notes Section -->
                        ${data.remarks ? `<div style="width: 100%; margin-top: 15px; border: 0.5px solid; padding: 10px;">
                            <strong style="font-size: 16px;">Notes:</strong> <span style="font-size: 16px;">${data.remarks}</span>
                        </div>` : ''}
                    </div>

                    <!-- Footer Section -->
                    <div class="page-footer">
                        <div style="text-align: center; padding: 20px; border-top: 2px solid #000;">
                            ${this.generateStaffInfoForA4(data)}
                            <div style="margin-bottom: 30px;">
                                <span style="font-size: 16px; font-weight: bold;">Authorized Signature</span>
                            </div>
                            <div style="font-size: 14px;">
                                <p style="margin: 5px 0;">Thank You for Your Business!</p>
                                <p style="margin: 5px 0;">Visit Again</p>
                            </div>
                        </div>
                    </div>
                </body>
            </html>
        `;
    }

    getHtmlContent(data: any, isMobile: boolean): string {
        const paperSize = localStorage.getItem('paper_size');
        let htmlContent: string;

        switch (paperSize) {
            case "A4":
                htmlContent = this.getHtmlContentForWebA4(data);
                break;
            case "A5":
                htmlContent = this.getHtmlContentForWebA5(data);
                break;
            default:
                htmlContent = this.getHtmlContentForWebA4(data);
                break;
        }

        return isMobile ? this.getHtmlContentForMobile(data) : htmlContent;
    }

    print(data: any, isMobile: boolean = false, filename: string = ''): void {
        console.log(isMobile);
        const paperType = localStorage.getItem('paper_type');

        if (this.util.isAndroid() && isMobile && paperType === 'Thermal') {
            this.printBillSlip(data);
        } else if (this.util.isCordova() && paperType !== 'Thermal') {
            await this.printDocument(this.getHtmlContent(data, isMobile), filename);
        } else {
            this.printHtmlDocument(this.getHtmlContent(data, isMobile));
        }
    }

    /**
     * Print HTML document in browser window
     * @param htmlContent - HTML content to print
     */
    printHtmlDocument(htmlContent: string): void {
        const printWindow = window.open('', '_blank');
        if (printWindow) {
            printWindow.document.open();
            printWindow.document.write(htmlContent);
            printWindow.document.close();
            printWindow.focus();
            printWindow.print();
            printWindow.close();
        }
    }

    /**
     * Preview invoice in a new browser window without printing
     * @param data - Invoice data to preview
     * @param isMobile - Whether to use mobile format
     * @returns void
     */
    previewInvoice(data: any, isMobile: boolean = false): void {
        const htmlContent = this.getHtmlContent(data, isMobile);
        const previewWindow = window.open('', '_blank');

        if (previewWindow) {
            // Add preview-specific styles and controls
            const previewHtml = this.addPreviewControls(htmlContent, data);
            previewWindow.document.open();
            previewWindow.document.write(previewHtml);
            previewWindow.document.close();
            previewWindow.focus();
        }
    }

    /**
     * Add preview controls to HTML content
     * @param htmlContent - Original HTML content
     * @param data - Invoice data
     * @returns Enhanced HTML with preview controls
     */
    private addPreviewControls(htmlContent: string, data: any): string {
        const previewControls = `
            <div id="preview-controls" style="position: fixed; top: 10px; right: 10px; z-index: 1000; background: white; padding: 10px; border: 1px solid #ccc; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <button onclick="window.print()" style="margin-right: 10px; padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    🖨️ Print
                </button>
                <button onclick="window.close()" style="padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    ✕ Close
                </button>
            </div>
            <style>
                @media print {
                    #preview-controls { display: none !important; }
                }
                #preview-controls button:hover {
                    opacity: 0.8;
                }
            </style>
        `;

        // Insert preview controls after the opening body tag
        return htmlContent.replace('<body>', `<body>${previewControls}`);
    }

    /**
     * Print document using Capacitor printer plugin
     * @param htmlContent - HTML content to print
     * @param filename - Optional filename for the document
     */
    async printDocument(htmlContent: string, filename: string = ''): Promise<void> {
        try {
            await Printer.print({
                content: htmlContent,
                name: filename || 'Invoice',
                orientation: 'portrait'
            });
            console.log('Print successful');
        } catch (error) {
            console.error('Print failed:', error);
            // Fallback to browser-based printing
            this.printHtmlDocument(htmlContent);
        }
    }

    /**
     * Get invoice title based on delivery challan status
     * @param data - Invoice data
     * @returns Invoice title string
     */
    getInvoiceTitle(data: InvoiceData): string {
        return data.delivery_challan ? 'DELIVERY CHALLAN' : 'TAX INVOICE';
    }

    /**
     * Generate enhanced report HTML for various reports
     * @param title - Report title
     * @param dateRange - Date range string
     * @param tableContent - HTML table content
     * @param summary - Summary information
     * @returns Complete HTML string for report
     */
    generateEnhancedReportHTML(title: string, dateRange: string, tableContent: string, summary: string): string {
        const companyName = localStorage.getItem("company_name") || "Company Name";
        const address = localStorage.getItem("address") || "Address";
        const gstNo = localStorage.getItem('gst_no') || "";
        const fssaiNo = localStorage.getItem('fssai_no') || "";

        return `
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>${title} - ${dateRange}</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        margin: 20px;
                        font-size: 12px;
                    }
                    .header {
                        text-align: center;
                        margin-bottom: 20px;
                        border-bottom: 2px solid #000;
                        padding-bottom: 10px;
                    }
                    .company-name {
                        font-size: 18px;
                        font-weight: bold;
                        margin-bottom: 5px;
                    }
                    .company-details {
                        font-size: 10px;
                        margin-bottom: 5px;
                    }
                    .report-title {
                        font-size: 16px;
                        font-weight: bold;
                        margin: 10px 0;
                    }
                    .date-range {
                        font-size: 12px;
                        margin-bottom: 10px;
                    }
                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-bottom: 20px;
                    }
                    th, td {
                        border: 1px solid #000;
                        padding: 5px;
                        text-align: left;
                    }
                    th {
                        background-color: #f0f0f0;
                        font-weight: bold;
                    }
                    .summary {
                        margin-top: 20px;
                        padding: 10px;
                        background-color: #f9f9f9;
                        border: 1px solid #ddd;
                        font-weight: bold;
                    }
                    @media print {
                        body {
                            margin: 0;
                        }
                        .no-print {
                            display: none;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="header">
                    <div class="company-name">${companyName}</div>
                    <div class="company-details">${address}</div>
                    <div class="company-details">GST: ${gstNo} | FSSAI: ${fssaiNo}</div>
                    <div class="report-title">${title}</div>
                    <div class="date-range">${dateRange}</div>
                </div>

                ${tableContent}

                <div class="summary">
                    ${summary}
                </div>
            </body>
            </html>
        `;
    }

    /**
     * Print enhanced report using browser print functionality
     * @param htmlContent - Complete HTML content to print
     * @param filename - Optional filename for the document
     */
    printEnhancedReport(htmlContent: string, filename: string = 'Report'): void {
        console.log(`Printing report: ${filename}`);
        this.printHtmlDocument(htmlContent);
    }

    /**
     * Add thermal printer header section
     * @param encoder - ESC/POS encoder instance
     * @param data - Invoice data
     * @param divider - Divider line string
     */
    private addThermalHeader(encoder: any, data: InvoiceData, divider: string): void {
        encoder.align('center')
            .bold(true)
            .text(localStorage.getItem("company_name") || "Company Name").newline()
            .bold(true)
            .text(localStorage.getItem("address") || "Address").newline()
            .bold(true)
            .text("GST:" + (localStorage.getItem("gst_no") || "")).newline()
            .text("FSSAI:" + (localStorage.getItem("fssai_no") || "")).newline()
            .text("Phone No:" + (localStorage.getItem("contact_no_left") || "") + "," + (localStorage.getItem("contact_no_right") || "")).newline()
            .text(divider).newline()
            .align('center').text(this.getInvoiceTitle(data)).bold(true).newline()
            .align('left')
            .bold(true)
            .text("Bill No:-" + data.id).newline()
            .text("Bill Date:-" + data.date).newline()
            .bold(false)
            .text(divider).newline();
    }

    /**
     * Add thermal printer customer information section
     * @param encoder - ESC/POS encoder instance
     * @param data - Invoice data
     * @param divider - Divider line string
     */
    private addThermalCustomerInfo(encoder: any, data: InvoiceData, divider: string): void {
        encoder.text("Customer Details").newline()
            .bold(true)
            .text("Name:-" + data.name).newline()
            .bold(false)
            .text("Number:-" + data.phone_no).newline()
            .text("Place:-" + data.place).newline()
            .text("Place of supply:- Tamilnadu (33)").newline()
            .text("GST:-" + (data.gst_no || "")).newline()
            .text(divider).newline()
            .bold(true);
    }

    /**
     * Add thermal printer items table with proper formatting
     * @param encoder - ESC/POS encoder instance
     * @param data - Invoice data
     * @param maxCharsPerLine - Maximum characters per line
     * @param divider - Divider line string
     * @param padString - String padding function
     */
    private addThermalItemsTable(encoder: any, data: InvoiceData, maxCharsPerLine: number, divider: string, padString: Function): void {
        const threeEqualLength = maxCharsPerLine / 3;
        const fourEqualsLength = maxCharsPerLine / 4;

        // Table headers with improved spacing
        const headers = [
            { text: 'Item', width: threeEqualLength + (threeEqualLength / 2) + (threeEqualLength / 3) },
            { text: 'Box', width: threeEqualLength / 3 },
            { text: 'Pcs', width: threeEqualLength / 3 },
            { text: 'Amt', width: (threeEqualLength / 2) + (threeEqualLength / 3) },
            { text: 'Rate', width: fourEqualsLength },
            { text: 'MRP', width: fourEqualsLength },
            { text: 'Tax', width: fourEqualsLength },
            { text: 'TAmt', width: fourEqualsLength }
        ];

        // Print headers
        encoder.align('left').text(headers.map(header => padString(header.text, header.width)).join('')).newline();
        encoder.align('left').text(divider).newline();

        // Print items with pagination support
        const itemsPerPage = 20; // Reasonable limit for thermal paper
        const pageBreak = this.calculatePageBreak(data.sales_invoice_items, itemsPerPage);

        // Print current page items
        pageBreak.currentPageItems.forEach((item: InvoiceItem) => {
            encoder.align('left').text(
                padString(item.product_name, headers[0].width) +
                padString(item.no, headers[1].width) +
                padString(item.weight, headers[2].width) +
                padString(item.line_total.toFixed(2), headers[3].width) +
                padString((item.rate - (item.rate * item.tax_rate) / (100 + item.tax_rate)).toFixed(2), headers[4].width) +
                padString(item.mrp, headers[5].width) +
                padString(item.tax_rate, headers[6].width) +
                padString(((item.line_total * item.tax_rate) / (100 + item.tax_rate)).toFixed(2), headers[7].width)
            ).newline();
        });

        // Handle remaining items if any (for future pagination implementation)
        if (pageBreak.hasMorePages) {
            encoder.align('center').text("... Continued on next page ...").newline();
        }

        // Print totals
        const totalValue = this.getTotalValue(data);
        encoder.align('left').text(
            padString("Total", fourEqualsLength) +
            padString("Box" + totalValue.totalNo, fourEqualsLength) +
            padString("Pcs" + totalValue.totalWeight, fourEqualsLength) +
            padString(totalValue.totalAmount.toString(), fourEqualsLength)
        ).newline();
        encoder.align('left').text(divider).newline();
    }

    /**
     * Add thermal printer summary section with tax details and totals
     * @param encoder - ESC/POS encoder instance
     * @param data - Invoice data
     * @param equalLength - Half of maximum characters per line
     * @param divider - Divider line string
     * @param padString - String padding function
     */
    private addThermalSummary(encoder: any, data: InvoiceData, equalLength: number, divider: string, padString: Function): void {
        const totalTaxAmount = this.getTaxDetails(data).totalTaxAmount;
        const taxDetails = this.getTaxDetails(data).taxDetails;
        const fourEqualsLength = equalLength / 2;

        // Tax Details Section
        const col1Width = fourEqualsLength / 2;
        const col2Width = fourEqualsLength + (fourEqualsLength / 2);
        const col3Width = fourEqualsLength;
        const col4Width = fourEqualsLength;

        encoder.align('center').text("Tax Details").newline();
        encoder.align('left').text(
            padString("Type", col1Width) +
            padString("Taxable", col2Width) +
            padString("CGST", col3Width) +
            padString("SGST", col4Width)
        ).newline();

        // Tax detail lines
        taxDetails.forEach((e: any) => {
            encoder.align('left').text(
                padString(e.value, col1Width) +
                padString(e.taxable_amount.toFixed(2), col2Width) +
                padString((e.tax_amount / 2).toFixed(2), col3Width) +
                padString((e.tax_amount / 2).toFixed(2), col4Width)
            ).newline();
        });

        encoder.align('left').text(divider).newline();

        // Invoice Totals with Rounding Support
        const totalValue = this.getTotalValue(data);
        encoder.align('left').text(padString("Subtotal", equalLength) + padString((totalValue.totalAmount - totalTaxAmount).toFixed(2), equalLength)).newline();
        encoder.align('left').text(padString("Tax Total", equalLength) + padString(totalTaxAmount.toFixed(2), equalLength)).newline();
        encoder.align('left').text(padString("Gross Total", equalLength) + padString((data.gross_total || data.bill_amount).toFixed(2), equalLength)).newline();

        // Add rounding line if enabled and adjustment is not zero
        if (data.rounding_enabled && data.rounding_adjustment !== 0) {
            encoder.align('left').text(padString("Rounding Off", equalLength) + padString((data.rounding_adjustment > 0 ? '+' : '') + data.rounding_adjustment.toFixed(2), equalLength)).newline();
        }

        encoder.align('left').text(padString("Net Amount", equalLength) + padString(data.bill_amount.toFixed(2), equalLength)).newline();
        encoder.align('left').text(divider).newline();

        // Additional Details
        encoder.align('center').text("Additional Details").newline();
        encoder.align('left').text(padString("Opening Balance", equalLength) + padString(data.previous_balance.toFixed(2), equalLength)).newline();
        encoder.align('left').text(padString("Bill Value", equalLength) + padString(data.bill_amount.toFixed(2), equalLength)).newline();
        encoder.align('left').text(padString("Received Amount", equalLength) + padString(data.received_amount.toFixed(2), equalLength)).newline();
        encoder.align('left').text(padString("Closing Balance", equalLength) + padString(data.current_balance.toFixed(2), equalLength)).newline();
        encoder.align('left').text(divider).newline();

        // Staff Information
        this.addThermalStaffInfo(encoder, data, equalLength, divider);

        // Footer
        encoder.align('center')
            .text("Thank You for Your Order!")
            .newline()
            .newline().newline().newline();

        encoder.newline().cut('full');
    }

    /**
     * Add thermal printer staff information section
     * @param encoder - ESC/POS encoder instance
     * @param data - Invoice data
     * @param equalLength - Half of maximum characters per line
     * @param divider - Divider line string
     */
    private addThermalStaffInfo(encoder: any, data: InvoiceData, equalLength: number, divider: string): void {
        if (data.billed_by || data.delivery_by || data.collected_by) {
            encoder.align('center').text("Staff Information").newline();

            if (data.billed_by) {
                encoder.align('center').text(`Billed by: ${data.billed_by}`).newline();
            }

            if (data.delivery_by) {
                encoder.align('center').text(`Delivery by: ${data.delivery_by}`).newline();
            }

            if (data.collected_by) {
                encoder.align('center').text(`Collected by: ${data.collected_by}`).newline();
            }

            encoder.align('left').text(divider).newline();
        }
    }

    /**
     * Print invoice to thermal printer via Bluetooth
     * @param data - Invoice data to print
     */
    async printBillSlip(data: InvoiceData): Promise<void> {
        const paperSizeStr = localStorage.getItem('paper_size') || '58';
        const paperSize = parseInt(paperSizeStr, 10);
        const maxCharsPerLine = this.getMaxCharsPerLine(paperSize);
        const divider = '*'.repeat(maxCharsPerLine);
        const equalLength = maxCharsPerLine / 2;

        function padString(input: any, length: number): string {
            // Convert input to a string to ensure .padEnd works correctly
            const str = String(input);
            if (str.length > length) {
                // Subtract 3 to accommodate the length of "..."
                return str.slice(0, length - 3) + '...';
            } else {
                // Pad the string if it's shorter than the desired length
                return str.padEnd(length, ' ');
            }
        }

        const encoder = new EscPosEncoder();
        encoder.initialize();

        // Header Section - Company Information
        this.addThermalHeader(encoder, data, divider);

        // Customer Information Section
        this.addThermalCustomerInfo(encoder, data, divider);

        // Items Section with Pagination
        this.addThermalItemsTable(encoder, data, maxCharsPerLine, divider, padString);

        // Summary Section
        this.addThermalSummary(encoder, data, equalLength, divider, padString);

        // Generate and send to printer
        const resultByte = encoder.encode();
        this.bluetoothService.printToDevice(resultByte);
    }

    /**
     * Generate sample invoice data for testing
     * @returns Sample invoice data
     */
    generateSampleInvoiceData(): InvoiceData {
        return {
            id: 6657,
            name: "kkp",
            date: "2025-06-14",
            phone_no: "9442969898",
            place: "kariyapatti",
            gst_no: "33BLYPS0673P1Z2",
            sales_invoice_items: [
                {
                    product_name: "Skol chocobar rs 5(40 N)",
                    no: 0,
                    weight: 3,
                    rate: 3.47,
                    line_total: 164.00,
                    mrp: 5,
                    tax_rate: 18,
                    remarks: ""
                },
                {
                    product_name: "lolly stick lazza(24 N)",
                    no: 1,
                    weight: 0,
                    rate: 3.47,
                    line_total: 98.40,
                    mrp: 5,
                    tax_rate: 18,
                    remarks: ""
                }
            ],
            bill_amount: 262.00,
            previous_balance: 15622.50,
            received_amount: 0.00,
            current_balance: 15884.50,
            rounding_enabled: true,
            rounding_adjustment: -0.40,
            gross_total: 262.40,
            delivery_challan: false,
            invoice_status: "completed",
            remarks: "Sample invoice for testing GST compliance",
            headerdata: [],
            metadata: []
        };
    }

    /**
     * Test invoice preview with sample data
     */
    testInvoicePreview(): void {
        const sampleData = this.generateSampleInvoiceData();
        this.previewInvoice(sampleData, false);
    }
}
