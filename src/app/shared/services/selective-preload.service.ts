import { Injectable } from '@angular/core';
import { PreloadingStrategy, Route } from '@angular/router';
import { Observable, of } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SelectivePreloadService implements PreloadingStrategy {
  
  // Define which routes should be preloaded
  private preloadedModules: string[] = [
    'tabs',      // Essential - main navigation
    'product',   // High frequency - often accessed
    'buyers',    // High frequency - often accessed
    'sales-bill' // High frequency - often accessed
  ];

  preload(route: Route, fn: () => Observable<any>): Observable<any> {
    // Check if this route should be preloaded
    const shouldPreload = this.shouldPreload(route);
    
    if (shouldPreload) {
      console.log(`Preloading module: ${route.path}`);
      return fn();
    } else {
      console.log(`Skipping preload for module: ${route.path}`);
      return of(null);
    }
  }

  private shouldPreload(route: Route): boolean {
    // Preload if route path is in our preload list
    if (route.path && this.preloadedModules.includes(route.path)) {
      return true;
    }

    // Preload if route data explicitly requests preload
    if (route.data && route.data['preload'] === true) {
      return true;
    }

    // Don't preload by default
    return false;
  }

  // Method to dynamically add routes to preload list
  addToPreload(routePath: string): void {
    if (!this.preloadedModules.includes(routePath)) {
      this.preloadedModules.push(routePath);
    }
  }

  // Method to remove routes from preload list
  removeFromPreload(routePath: string): void {
    const index = this.preloadedModules.indexOf(routePath);
    if (index > -1) {
      this.preloadedModules.splice(index, 1);
    }
  }

  // Get current preload configuration
  getPreloadList(): string[] {
    return [...this.preloadedModules];
  }
}