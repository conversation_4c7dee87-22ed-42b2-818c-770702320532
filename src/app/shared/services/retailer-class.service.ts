import { Injectable } from '@angular/core';


import { HttpClient } from "@angular/common/http";
import { environment } from "src/environments/environment";

@Injectable({
  providedIn: 'root'
})
export class RetailerClassService {



  constructor(private http: HttpClient) { }

  getBuyerClass() {
    return this.http.get(`${environment.apiUrl}/buyer_class/`).toPromise();
  }

  postBuyerClass(data) {
    return this.http.post(`${environment.apiUrl}/buyer_class/`, data).toPromise();
  }
  putBuyerClass(data) {
    return this.http.put(`${environment.apiUrl}/buyer_class/`, data).toPromise();
  }
  deleteBuyerClass(buyer_class_id) {
    return this.http.delete(`${environment.apiUrl}/buyer_class/?buyer_class_id=${buyer_class_id}`).toPromise();
  }
  getBuyerClassMargin(id) {
    return this.http.get(`${environment.apiUrl}/buyer_class_margin/?buyer_class_id=${id}`).toPromise();

  }
  postBuyerClassMargin(buyer_class_id, data) {
    return this.http.post(`${environment.apiUrl}/buyer_class_margin/?buyer_class_id=${buyer_class_id}`, data).toPromise();
  }

  // Import/Export methods for BuyerClassMargin
  exportBuyerClassMargin(buyer_class_id: number) {
    return this.http.get(`${environment.apiUrl}/buyer_class_margin/export/?buyer_class_id=${buyer_class_id}`, { responseType: 'blob' });
  }

  importBuyerClassMargin(buyer_class_id: number, file: File) {
    const formData = new FormData();
    formData.append('csv_file', file);
    return this.http.post(`${environment.apiUrl}/buyer_class_margin/import/?buyer_class_id=${buyer_class_id}`, formData);
  }
}
