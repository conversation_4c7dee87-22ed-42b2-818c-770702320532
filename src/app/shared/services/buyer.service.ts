import { Injectable } from '@angular/core';

import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { zip } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class BuyerService {
  putBuyerDepositData(data: any) {
    return this.http.put(`${environment.apiUrl}/buyer/?deposit=true`, data)
    .toPromise();
  }
  putBuyerAssetData(data: any) {
    return this.http.put(`${environment.apiUrl}/buyer/?asset=true`, data)
      .toPromise();
  }
  deleteBuyerAssetData(id: any) {
    return this.http.delete(`${environment.apiUrl}/buyer/?id=${id}&asset=true`)
      .toPromise();
  }
  deleteBuyerDepositData(id: any) {
    return this.http.delete(`${environment.apiUrl}/buyer/?id=${id}&deposit=true`)
      .toPromise();
  }
  postBuyerAssetData(id: any, data: any) {
    return this.http
    .post(`${environment.apiUrl}/buyer/?id=${id}&asset=true`, data)
    .toPromise();
  }
  postBuyerDepositData(id: any, data: any) {
    return this.http
    .post(`${environment.apiUrl}/buyer/?id=${id}&deposit=true`, data)
    .toPromise();
  }

  constructor(private http: HttpClient) { }
  getBuyer() {
    return this.http
      .get(`${environment.apiUrl}/buyer/`)
      .toPromise();
  }
  getBuyerById(id) {
    return this.http
      .get(`${environment.apiUrl}/buyer/?id=${id}`)
      .toPromise();
  }
  saveBuyer(data) {
    return this.http
      .post(`${environment.apiUrl}/buyer/`, data)
      .toPromise();
  }
  editBuyer(data) {
    return this.http.put(`${environment.apiUrl}/buyer/`, data)
      .toPromise();
  }
  editBuyerStatus(data) {
    return this.http.put(`${environment.apiUrl}/buyer/`, data)
      .toPromise();
  }
  deleteBuyer(data) {
    return this.http.delete(`${environment.apiUrl}/buyer/`, { body: data })
      .toPromise();
  }

  getRoute() {
    return this.http.get(`${environment.apiUrl}/route/`)
      .toPromise();
  }
  addRoute(data) {
    return this.http.post(`${environment.apiUrl}/route/`, { name: data })
      .toPromise();
  }
  geteditData() {
    return zip(
      this.http.get(`${environment.apiUrl}/route/`),
      this.http.get(`${environment.apiUrl}/buyer_class/`)
    );
  }

  getAllProductsAndBrandsForBuyer(buyerId: number) {
    return this.http.get(`${environment.apiUrl}/buyer_products/?buyer_id=${buyerId}&all_products=true`);
  }

  /**
   * Get buyers for a specific route
   */
  getBuyersByRoute(routeId: number): Promise<any> {
    return this.http
      .get(`${environment.apiUrl}/buyer/?route_id=${routeId}`)
      .toPromise();
  }

  /**
   * Get buyers for a specific route and weekday
   */
  getBuyersByRouteAndWeekday(routeId: number, weekday: string): Promise<any> {
    return this.http
      .get(`${environment.apiUrl}/buyer/?route_id=${routeId}&weekday=${weekday}`)
      .toPromise();
  }
}
