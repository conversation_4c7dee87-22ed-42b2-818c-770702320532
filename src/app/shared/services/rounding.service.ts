import { Injectable } from '@angular/core';

export interface RoundingConfig {
  enabled: boolean;
}

export interface RoundingResult {
  originalAmount: number;
  roundedAmount: number;
  adjustment: number;
  method: string;
}

@Injectable({
  providedIn: 'root'
})
export class RoundingService {

  constructor() { }

  /**
   * Apply rounding to nearest rupee (always round to nearest)
   * @param amount - Original amount to round
   * @returns RoundingResult with original amount, rounded amount, and adjustment
   */
  applyRoundingToNearest(amount: number): RoundingResult {
    if (!amount) {
      return {
        originalAmount: amount,
        roundedAmount: amount,
        adjustment: 0,
        method: 'nearest'
      };
    }

    const roundedAmount = Math.round(amount);
    // Round adjustment to 2 decimal places to avoid floating point precision issues
    const adjustment = Math.round((roundedAmount - amount) * 100) / 100;

    return {
      originalAmount: amount,
      roundedAmount: roundedAmount,
      adjustment: adjustment,
      method: 'nearest'
    };
  }

  /**
   * Calculate invoice totals with proper calculation order
   * @param subtotal - Original subtotal from items
   * @param taxAmount - Tax calculated from original subtotal
   * @param config - Rounding configuration
   * @returns Complete invoice calculation breakdown
   */
  calculateInvoiceTotals(subtotal: number, taxAmount: number, config: RoundingConfig) {
    // Calculate gross total (subtotal + tax)
    const grossTotal = subtotal + taxAmount;

    if (!config.enabled) {
      return {
        subtotal: subtotal,
        taxAmount: taxAmount,
        grossTotal: grossTotal,
        roundingAdjustment: 0,
        netAmount: grossTotal
      };
    }

    // Apply rounding only to the final gross total
    const roundingResult = this.applyRoundingToNearest(grossTotal);

    return {
      subtotal: subtotal,
      taxAmount: taxAmount,
      grossTotal: grossTotal,
      roundingAdjustment: roundingResult.adjustment,
      netAmount: roundingResult.roundedAmount
    };
  }

  /**
   * Calculate rounding adjustment for display
   * @param adjustment - Rounding adjustment amount
   * @returns Formatted adjustment string with sign
   */
  formatRoundingAdjustment(adjustment: number): string {
    if (adjustment === 0) {
      return '₹0.00';
    }

    if (adjustment > 0) {
      return `+₹${adjustment.toFixed(2)}`;
    } else {
      return `-₹${Math.abs(adjustment).toFixed(2)}`;
    }
  }

  /**
   * Validate if rounding is beneficial (saves decimal places)
   * @param amount - Amount to check
   * @returns True if rounding would change the amount
   */
  isRoundingBeneficial(amount: number): boolean {
    return amount !== Math.round(amount);
  }
}
