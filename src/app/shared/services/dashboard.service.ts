import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { BuyerService } from './buyer.service';
import { ProductService } from './product.service';
import { ReportsService } from './report.service';
import { SalesOrderService } from './sales-order.service';
import { BuyerManagementService, BuyerManagementResponse } from './user-management.service';

// API Response interface following the existing pattern in the codebase
interface ApiResponse {
  success: boolean;
  message?: string;
  data?: any;
}

// Buyer interface based on usage in existing components
interface Buyer {
  id: number;
  name: string;
  status: string;
  [key: string]: any; // Allow additional properties
}

// Product response interface based on existing usage
interface ProductResponse extends ApiResponse {
  data?: {
    data: any[];
    count: number;
  };
}

export interface DashboardStats {
  activeUsers: number;
  inactiveUsers: number;
  totalUsers: number;
  totalInvoices: number;
  totalProducts: number;
  recentActivities: any[];
  userDistribution: any;
  activityOverview: any[];
}

// New comprehensive dashboard data interface
export interface ComprehensiveDashboardData {
  summary_metrics: {
    total_products: number;
    active_products: number;
    total_buyers: number;
    active_buyers: number;
    total_suppliers: number;
    active_suppliers: number;
    low_stock_items: number;
  };
  financial_overview: {
    total_revenue: number;
    total_purchases: number;
    total_expenses: number;
    gross_profit: number;
    net_profit: number;
    monthly_sales: number;
    monthly_purchases: number;
    monthly_expenses: number;
    monthly_profit: number;
    financial_year_start?: string;
    financial_year_end?: string;
    financial_year_label?: string;
  };
  outstanding_balances: {
    total_receivables: number;
    total_payables: number;
    net_position: number;
  };
  activity_summary: {
    recent_sales_count: number;
    recent_purchase_count: number;
    total_transactions: number;
  };
  top_customers: Array<{
    buyer__name: string;
    total_amount: number;
  }>;
  generated_at: string;
}

// Sales trend by brand interfaces
export interface SalesTrendByBrandData {
  time_series_data: SalesTrendDataPoint[];
  brand_summary: BrandSummary[];
  brand_growth: { [brandId: number]: number };
  date_range: {
    from_date: string;
    to_date: string;
    time_period: string;
  };
  total_brands: number;
  generated_at: string;
}

export interface SalesTrendDataPoint {
  period: string;
  product__brand__id: number;
  product__brand__name: string;
  total_sales_amount: number;
  total_quantity: number;
  total_pieces: number;
  transaction_count: number;
}

export interface BrandSummary {
  product__brand__id: number;
  product__brand__name: string;
  total_sales_amount: number;
  total_quantity: number;
  total_pieces: number;
  transaction_count: number;
}

@Injectable({
  providedIn: 'root'
})
export class DashboardService {

  constructor(
    private http: HttpClient,
    private buyerService: BuyerService,
    private productService: ProductService,
    private reportsService: ReportsService,
    private salesOrderService: SalesOrderService,
    private buyerManagementService: BuyerManagementService
  ) { }

  // New method to get comprehensive dashboard data from backend
  async getComprehensiveDashboardData(): Promise<ComprehensiveDashboardData | null> {
    try {
      const response = await this.http.get(`${environment.apiUrl}/dashboard/`).toPromise() as ApiResponse;
      if (response && response.success) {
        return response.data as ComprehensiveDashboardData;
      }
      return null;
    } catch (error) {
      console.error('Error loading comprehensive dashboard data:', error);
      return null;
    }
  }

  // Updated method that uses real backend data when available, falls back to legacy approach
  async getDashboardStats(): Promise<DashboardStats> {
    const stats: DashboardStats = {
      activeUsers: 0,
      inactiveUsers: 0,
      totalUsers: 0,
      totalInvoices: 0,
      totalProducts: 0,
      recentActivities: [],
      userDistribution: { active: 0, inactive: 0 },
      activityOverview: []
    };

    try {
      // Try to get comprehensive data from new backend endpoint
      const comprehensiveData = await this.getComprehensiveDashboardData();

      if (comprehensiveData) {
        // Map comprehensive data to legacy DashboardStats format
        stats.totalUsers = comprehensiveData.summary_metrics.total_buyers;
        stats.activeUsers = comprehensiveData.summary_metrics.active_buyers;
        stats.inactiveUsers = stats.totalUsers - stats.activeUsers;
        stats.totalProducts = comprehensiveData.summary_metrics.total_products;
        stats.totalInvoices = comprehensiveData.activity_summary.total_transactions;
        stats.userDistribution = {
          active: stats.activeUsers,
          inactive: stats.inactiveUsers
        };

        // Generate activity overview based on real data
        stats.activityOverview = this.generateActivityOverviewFromData(comprehensiveData);

        // Generate recent activities based on real data
        stats.recentActivities = this.generateRecentActivitiesFromData(comprehensiveData);
      } else {
        // Fallback to legacy approach if backend endpoint fails
        console.warn('Falling back to legacy dashboard data loading');

        // Load user statistics
        const userStats = await this.getUserStats();
        stats.activeUsers = userStats.activeUsers;
        stats.inactiveUsers = userStats.inactiveUsers;
        stats.totalUsers = userStats.totalUsers;
        stats.userDistribution = userStats.userDistribution;

        // Load product statistics
        const productStats = await this.getProductStats();
        stats.totalProducts = productStats.totalProducts;

        // Load invoice statistics (mock data for now)
        stats.totalInvoices = 150;

        // Generate activity overview (mock data)
        stats.activityOverview = this.generateActivityOverview();

        // Generate recent activities (mock data)
        stats.recentActivities = this.generateRecentActivities();
      }

    } catch (error) {
      console.error('Error loading dashboard stats:', error);
    }

    return stats;
  }

  private async getUserStats() {
    try {
      const response = await this.buyerService.getBuyer() as ApiResponse;
      if (response && response.success) {
        const buyers: Buyer[] = response.data || [];
        const totalUsers = buyers.length;
        const activeUsers = buyers.filter((buyer: Buyer) => buyer.status === 'active').length;
        const inactiveUsers = totalUsers - activeUsers;

        return {
          totalUsers,
          activeUsers,
          inactiveUsers,
          userDistribution: { active: activeUsers, inactive: inactiveUsers }
        };
      }
    } catch (error) {
      console.error('Error loading user stats:', error);
    }

    return {
      totalUsers: 0,
      activeUsers: 0,
      inactiveUsers: 0,
      userDistribution: { active: 0, inactive: 0 }
    };
  }

  private async getProductStats() {
    try {
      const response = await this.productService.getProduct(1, 1000, '') as ProductResponse;
      if (response && response.success) {
        return {
          totalProducts: response.data?.data?.length || 0
        };
      }
    } catch (error) {
      console.error('Error loading product stats:', error);
    }

    return {
      totalProducts: 0
    };
  }

  private generateActivityOverviewFromData(data: ComprehensiveDashboardData) {
    // Generate activity overview based on real data
    const currentMonth = new Date().getMonth();
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    // For now, create a simple representation with current month data
    // In a real implementation, you'd want to fetch historical monthly data
    const overview = [];
    for (let i = 5; i >= 0; i--) {
      const monthIndex = (currentMonth - i + 12) % 12;
      const value = i === 0 ? data.activity_summary.total_transactions :
                   Math.floor(data.activity_summary.total_transactions * (0.7 + Math.random() * 0.6));
      overview.push({
        month: months[monthIndex],
        value: value
      });
    }

    return overview;
  }

  private generateRecentActivitiesFromData(data: ComprehensiveDashboardData) {
    const activities = [];

    // Add sales activity
    if (data.activity_summary.recent_sales_count > 0) {
      activities.push({
        title: 'Recent Sales',
        description: `${data.activity_summary.recent_sales_count} sales transactions in the last 30 days`,
        time: 'Last 30 days',
        icon: 'trending-up',
        amount: data.financial_overview.monthly_sales
      });
    }

    // Add purchase activity
    if (data.activity_summary.recent_purchase_count > 0) {
      activities.push({
        title: 'Recent Purchases',
        description: `${data.activity_summary.recent_purchase_count} purchase transactions in the last 30 days`,
        time: 'Last 30 days',
        icon: 'bag',
        amount: data.financial_overview.monthly_purchases
      });
    }

    // Add top customer activity
    if (data.top_customers && data.top_customers.length > 0) {
      const topCustomer = data.top_customers[0];
      activities.push({
        title: 'Top Customer',
        description: `${topCustomer.buyer__name} - highest sales this month`,
        time: 'This month',
        icon: 'person-circle',
        amount: topCustomer.total_amount
      });
    }

    // Add low stock alert if applicable
    if (data.summary_metrics.low_stock_items > 0) {
      activities.push({
        title: 'Low Stock Alert',
        description: `${data.summary_metrics.low_stock_items} products need restocking`,
        time: 'Current',
        icon: 'warning',
        amount: null
      });
    }

    return activities.slice(0, 5); // Limit to 5 activities
  }

  private generateActivityOverview() {
    // Mock data for activity overview chart (fallback)
    return [
      { month: 'Jan', value: 45 },
      { month: 'Feb', value: 52 },
      { month: 'Mar', value: 48 },
      { month: 'Apr', value: 61 },
      { month: 'May', value: 55 },
      { month: 'Jun', value: 67 }
    ];
  }

  private generateRecentActivities() {
    // Mock data for recent activities
    return [
      {
        type: 'invoice',
        title: 'Invoice Sent',
        description: 'Invoice #12345',
        time: '2 hours ago',
        icon: 'document-text'
      },
      {
        type: 'payment',
        title: 'Payment Received',
        description: 'Payment Receipt #67890',
        time: '4 hours ago',
        icon: 'card'
      },
      {
        type: 'user',
        title: 'New User Added',
        description: 'User registration completed',
        time: '6 hours ago',
        icon: 'person-add'
      }
    ];
  }

  async getInactiveBuyers() {
    // Get real inactive buyers based on purchasing patterns
    try {
      const buyerManagementData = await this.buyerManagementService.getInactiveBuyers();
      if (buyerManagementData && buyerManagementData.inactive_buyers) {
        return buyerManagementData.inactive_buyers.map(buyer => ({
          id: buyer.id,
          name: buyer.name,
          place: buyer.place,
          contact_person: buyer.contact_person,
          phone_no: buyer.phone_no,
          gst_no: buyer.gst_no,
          current_balance: buyer.current_balance,
          credit_limit: buyer.credit_limit,
          buyer_class: buyer.buyer_class,
          route: buyer.route,
          lastActive: this.buyerManagementService.formatDaysSinceLastPurchase(buyer.days_since_last_purchase),
          expected_frequency: buyer.expected_frequency,
          purchasing_status: buyer.purchasing_status,
          frequency_analysis: buyer.frequency_analysis,
          days_since_last_purchase: buyer.days_since_last_purchase,
          total_purchases: buyer.total_purchases,
          average_monthly_purchases: buyer.average_monthly_purchases,
          total_purchase_amount: buyer.total_purchase_amount,
          average_purchase_amount: buyer.average_purchase_amount,
          priority: this.buyerManagementService.getPriorityLevel(buyer.days_since_last_purchase),
          statusColor: this.buyerManagementService.getStatusColor(buyer.purchasing_status),
          frequencyColor: this.buyerManagementService.getFrequencyColor(buyer.expected_frequency),
          buyerIcon: this.buyerManagementService.getBuyerIcon(buyer.buyer_class),
          actionSuggestions: this.buyerManagementService.getActionSuggestions(buyer)
        }));
      }
    } catch (error) {
      console.error('Error loading inactive buyers:', error);
    }

    // Fallback to empty array if service fails
    return [];
  }

  getUserName(): string {
    // Try to get user name from localStorage or use default
    const companyName = localStorage.getItem('company_name');
    const role = localStorage.getItem('role');
    return companyName || role || 'User';
  }

  // New method to get sales trend by brand data
  async getSalesTrendByBrand(
    fromDate?: string,
    toDate?: string,
    timePeriod: string = 'monthly',
    brandIds?: number[]
  ): Promise<SalesTrendByBrandData | null> {
    try {
      let params: any = {
        sales_trend_by_brand: 'true',
        time_period: timePeriod
      };

      if (fromDate) params.from_date = fromDate;
      if (toDate) params.to_date = toDate;
      if (brandIds && brandIds.length > 0) {
        brandIds.forEach(id => {
          params[`brand_ids`] = brandIds;
        });
      }

      const response = await this.http.get(`${environment.apiUrl}/dashboard/`, { params }).toPromise() as ApiResponse;
      if (response && response.success) {
        return response.data as SalesTrendByBrandData;
      }
      return null;
    } catch (error) {
      console.error('Error loading sales trend by brand data:', error);
      return null;
    }
  }
}
