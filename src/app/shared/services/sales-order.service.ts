import { Injectable } from "@angular/core";

import { HttpClient } from "@angular/common/http";
import { environment } from "src/environments/environment";

@Injectable({
  providedIn: "root",
})
export class SalesOrderService {
  constructor(private http: HttpClient) {}

  getSalesOrder(salesPerson,route) {
    return this.http.get(`${environment.apiUrl}/sales_invoice/?route=${route? route.id : ''}&sales_person=${salesPerson}&sales_order=true${localStorage.getItem('role') == 'sales_officer'?'&sales_officer=true':''}`).toPromise();
  }
  getSalesInvoiceById(id) {
    return this.http
      .get(`${environment.apiUrl}/sales_invoice/?invoice_id=${id}`)
      .toPromise();
  }
  saveSalesInvoice(data) {
    return this.http
      .post(`${environment.apiUrl}/sales_invoice/`, data)
      .toPromise();
  }
  editSalesInvoice(data) {
    return this.http
      .put(`${environment.apiUrl}/sales_invoice/`, data)
      .toPromise();
  }
  createSalesInvoice() {
    return this.http
      .get(`${environment.apiUrl}/sales_invoice/?create_invoice=true`)
      .toPromise();
  }
  getBuyerSalesInvoice(id) {
    return this.http
      .get(`${environment.apiUrl}/sales_invoice/?buyer_id=${id}`)
      .toPromise();
  }

  deleteSalesInvoice(data) {
    return this.http
      .delete(`${environment.apiUrl}/sales_invoice/?create_invoice=true`, {
        body: data,
      })
      .toPromise();
  }
}
