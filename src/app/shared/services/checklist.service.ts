import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

export interface ChecklistItem {
  id?: number;
  checklist?: number;
  title: string;
  description?: string;
  is_mandatory: boolean;
  is_completed: boolean;
  image?: string;
  notes?: string;
  completed_at?: string;
  sort_order: number;
}

export interface Checklist {
  id?: number;
  title: string;
  description?: string;
  user?: number;
  route?: number;
  buyer?: number;
  status: 'pending' | 'in_progress' | 'completed' | 'overdue';
  priority: 'low' | 'medium' | 'high';
  due_date?: string;
  start_time?: string;
  submit_time?: string;
  created_at?: string;
  updated_at?: string;
  items?: ChecklistItem[];
  completion_percentage?: number;
  is_overdue?: boolean;
  route_name?: string;
  buyer_name?: string;
}

@Injectable({
  providedIn: 'root'
})
export class ChecklistService {

  constructor(private http: HttpClient) { }

  /**
   * Get all checklists with optional filters
   */
  getChecklists(filters?: {
    status?: string;
    route_id?: number;
    buyer_id?: number;
  }): Promise<any> {
    let params = '';
    if (filters) {
      const queryParams = new URLSearchParams();
      if (filters.status) queryParams.append('status', filters.status);
      if (filters.route_id) queryParams.append('route_id', filters.route_id.toString());
      if (filters.buyer_id) queryParams.append('buyer_id', filters.buyer_id.toString());
      params = queryParams.toString() ? `?${queryParams.toString()}` : '';
    }
    
    return this.http
      .get(`${environment.apiUrl}/checklist/${params}`)
      .toPromise();
  }

  /**
   * Create a new checklist
   */
  createChecklist(checklist: Checklist): Promise<any> {
    return this.http
      .post(`${environment.apiUrl}/checklist/`, checklist)
      .toPromise();
  }

  /**
   * Update an existing checklist
   */
  updateChecklist(checklist: Checklist): Promise<any> {
    return this.http
      .put(`${environment.apiUrl}/checklist/`, checklist)
      .toPromise();
  }

  /**
   * Delete a checklist
   */
  deleteChecklist(checklistId: number): Promise<any> {
    return this.http
      .delete(`${environment.apiUrl}/checklist/`, { body: { id: checklistId } })
      .toPromise();
  }

  /**
   * Create or update checklist item
   */
  saveChecklistItem(item: ChecklistItem, imageFile?: File): Promise<any> {
    const formData = new FormData();
    
    // Add item data
    Object.keys(item).forEach(key => {
      if (item[key] !== null && item[key] !== undefined) {
        formData.append(key, item[key].toString());
      }
    });
    
    // Add image file if provided
    if (imageFile) {
      formData.append('image', imageFile);
    }
    
    return this.http
      .post(`${environment.apiUrl}/checklist-item/`, formData)
      .toPromise();
  }

  /**
   * Update checklist item completion status
   */
  updateChecklistItem(item: ChecklistItem): Promise<any> {
    return this.http
      .put(`${environment.apiUrl}/checklist-item/`, item)
      .toPromise();
  }

  /**
   * Start checklist (update start_time)
   */
  startChecklist(checklistId: number): Promise<any> {
    const startTime = new Date().toISOString();
    return this.updateChecklist({
      id: checklistId,
      start_time: startTime,
      status: 'in_progress'
    } as Checklist);
  }

  /**
   * Submit checklist (update submit_time and status)
   */
  submitChecklist(checklistId: number): Promise<any> {
    const submitTime = new Date().toISOString();
    return this.updateChecklist({
      id: checklistId,
      submit_time: submitTime,
      status: 'completed'
    } as Checklist);
  }

  /**
   * Get checklist summary for dashboard
   */
  getChecklistSummary(): Promise<any> {
    return this.getChecklists().then(response => {
      if (response.success && response.data) {
        const checklists = response.data;
        return {
          total: checklists.length,
          pending: checklists.filter(c => c.status === 'pending').length,
          in_progress: checklists.filter(c => c.status === 'in_progress').length,
          completed: checklists.filter(c => c.status === 'completed').length,
          overdue: checklists.filter(c => c.is_overdue).length
        };
      }
      return {
        total: 0,
        pending: 0,
        in_progress: 0,
        completed: 0,
        overdue: 0
      };
    });
  }

  /**
   * Get pending checklists that need attention
   */
  getPendingChecklists(): Promise<any> {
    return this.getChecklists({ status: 'pending' });
  }

  /**
   * Get overdue checklists
   */
  getOverdueChecklists(): Promise<any> {
    return this.getChecklists().then(response => {
      if (response.success && response.data) {
        const overdueChecklists = response.data.filter(c => c.is_overdue);
        return {
          success: true,
          data: overdueChecklists
        };
      }
      return response;
    });
  }
}
