<app-header [title]="'Reports'" [returnUrl]="'tabs/home'"></app-header>
<ion-content class="reports-content">
  <!-- Summary Section - Added at Top
  <div class="summary-section">
    <h3 class="section-title">
      <ion-icon name="analytics-outline" class="section-icon"></ion-icon>
      Reports Overview
    </h3>

    <ion-row>
      <ion-col size="6">
        <ion-card class="summary-card reports-card">
          <ion-card-content>
            <div class="summary-content">
              <div class="summary-info">
                <h3>Available Reports</h3>
                <h1>{{getAvailableReportsCount()}}</h1>
                <p>Report types</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="document-text" class="summary-icon reports"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6">
        <ion-card class="summary-card period-card">
          <ion-card-content>
            <div class="summary-content">
              <div class="summary-info">
                <h3>Report Period</h3>
                <h1>{{getSelectedPeriodDays()}}</h1>
                <p>Days selected</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="calendar" class="summary-icon period"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col size="6">
        <ion-card class="summary-card gst-card">
          <ion-card-content>
            <div class="summary-content">
              <div class="summary-info">
                <h3>GST Reports</h3>
                <h1>{{gstReport ? 'Enabled' : 'Disabled'}}</h1>
                <p>Compliance status</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="receipt" class="summary-icon gst"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6">
        <ion-card class="summary-card formats-card">
          <ion-card-content>
            <div class="summary-content">
              <div class="summary-info">
                <h3>Export Formats</h3>
                <h1>3</h1>
                <p>TXT, CSV, Excel</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="download" class="summary-icon formats"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
  </div>
 -->
  <!-- Collapsible Date Filter Section -->
  <div class="filter-section">
    <div class="filter-header" (click)="toggleFilters()">
      <h3 class="section-title">
        <ion-icon name="calendar-outline" class="section-icon"></ion-icon>
        Date Range Selection
      </h3>
      <ion-button fill="clear" class="toggle-button">
        <ion-icon [name]="showFilters ? 'chevron-up' : 'chevron-down'" class="toggle-icon"></ion-icon>
      </ion-button>
    </div>

    <div class="filter-content" [class.expanded]="showFilters">
      <ion-card class="filter-card">
        <ion-card-content>
          <ion-row>
            <ion-col size="6">
              <ion-item lines="none" class="date-item">
                <ion-label position="stacked">From Date</ion-label>
                <ion-input
                  [(ngModel)]="from_date"
                  [value]="from_date | date : 'YYYY-MM-dd'"
                  placeholder="Select start date"
                  type="date"
                  class="date-input">
                </ion-input>
              </ion-item>
            </ion-col>
            <ion-col size="6">
              <ion-item lines="none" class="date-item">
                <ion-label position="stacked">To Date</ion-label>
                <ion-input
                  [(ngModel)]="to_date"
                  [value]="to_date | date : 'YYYY-MM-dd'"
                  placeholder="Select end date"
                  type="date"
                  class="date-input">
                </ion-input>
              </ion-item>
            </ion-col>
          </ion-row>
          
          <!-- Zero Sales Toggle -->
          <ion-row>
            <ion-col size="12">
              <ion-item lines="none" class="toggle-item">
                <ion-label>Include Zero Sales</ion-label>
                <ion-toggle
                  [(ngModel)]="includeZeroSales"
                  (ionChange)="toggleZeroSales()"
                  slot="end">
                </ion-toggle>
              </ion-item>
            </ion-col>
          </ion-row>
        </ion-card-content>
      </ion-card>
    </div>
  </div>
  <!-- Reports Section -->
  <div class="reports-section">
    <h3 class="section-title">
      <ion-icon name="document-text-outline" class="section-icon"></ion-icon>
      Available Reports
    </h3>

    <ion-card class="report-card" *ngIf="gstReport">
      <ion-card-content>
        <ion-item lines="none" class="report-item">
          <ion-icon name="receipt-outline" slot="start" class="report-icon gst"></ion-icon>
          <ion-label>
            <h3>GST Report</h3>
            <p>Generate GST compliance report</p>
          </ion-label>
          <ion-button
            (click)="generateGstReport()"
            fill="solid"
            size="small"
            class="export-button excel">
            <ion-icon name="document-outline" slot="start"></ion-icon>
            Excel
          </ion-button>
        </ion-item>
      </ion-card-content>
    </ion-card>

    <!-- Brand-wise Sales Report -->
    <ion-card class="report-card">
      <ion-card-content>
        <ion-item lines="none" class="report-item">
          <ion-icon name="pricetag-outline" slot="start" class="report-icon brand-sales"></ion-icon>
          <ion-label>
            <h3>Brand-wise Sales Report</h3>
            <p>Sales analysis by brand with item-level details</p>
          </ion-label>
          <div class="export-buttons" slot="end">
            <ion-button
              (click)="selectBrandsForReport('txt')"
              fill="outline"
              size="small"
              class="export-button txt">
              <ion-icon name="document-text-outline" slot="start"></ion-icon>
              TXT
            </ion-button>
            <ion-button
              (click)="selectBrandsForReport('csv')"
              fill="solid"
              size="small"
              class="export-button excel">
              <ion-icon name="document-outline" slot="start"></ion-icon>
              Excel
            </ion-button>
          </div>
        </ion-item>
      </ion-card-content>
    </ion-card>

    <!-- General Sales Report -->
    <ion-card class="report-card">
      <ion-card-content>
        <ion-item lines="none" class="report-item">
          <ion-icon name="trending-up-outline" slot="start" class="report-icon general-sales"></ion-icon>
          <ion-label>
            <h3>General Sales Report</h3>
            <p>Comprehensive sales analysis with item-level details</p>
          </ion-label>
          <div class="export-buttons" slot="end">
            <ion-button
              (click)="generateGeneralSalesReport('txt')"
              fill="outline"
              size="small"
              class="export-button txt">
              <ion-icon name="document-text-outline" slot="start"></ion-icon>
              TXT
            </ion-button>
            <ion-button
              (click)="generateGeneralSalesReport('csv')"
              fill="solid"
              size="small"
              class="export-button excel">
              <ion-icon name="document-outline" slot="start"></ion-icon>
              Excel
            </ion-button>
          </div>
        </ion-item>
      </ion-card-content>
    </ion-card>

    <ion-card class="report-card">
      <ion-card-content>
        <ion-item lines="none" class="report-item">
          <ion-icon name="arrow-down-circle-outline" slot="start" class="report-icon pay-in"></ion-icon>
          <ion-label>
            <h3>Pay IN Report</h3>
            <p>Incoming payment transactions</p>
          </ion-label>
          <div class="export-buttons" slot="end">
            <ion-button
              (click)="generatePayReport('pay-in','txt')"
              fill="outline"
              size="small"
              class="export-button txt">
              <ion-icon name="document-text-outline" slot="start"></ion-icon>
              TXT
            </ion-button>
            <ion-button
              (click)="generatePayReport('pay-in','csv')"
              fill="solid"
              size="small"
              class="export-button excel">
              <ion-icon name="document-outline" slot="start"></ion-icon>
              Excel
            </ion-button>
          </div>
        </ion-item>
      </ion-card-content>
    </ion-card>

    <ion-card class="report-card">
      <ion-card-content>
        <ion-item lines="none" class="report-item">
          <ion-icon name="arrow-up-circle-outline" slot="start" class="report-icon pay-out"></ion-icon>
          <ion-label>
            <h3>Pay Out Report</h3>
            <p>Outgoing payment transactions</p>
          </ion-label>
          <div class="export-buttons" slot="end">
            <ion-button
              (click)="generatePayReport('pay-out','txt')"
              fill="outline"
              size="small"
              class="export-button txt">
              <ion-icon name="document-text-outline" slot="start"></ion-icon>
              TXT
            </ion-button>
            <ion-button
              (click)="generatePayReport('pay-out','csv')"
              fill="solid"
              size="small"
              class="export-button excel">
              <ion-icon name="document-outline" slot="start"></ion-icon>
              Excel
            </ion-button>
          </div>
        </ion-item>
      </ion-card-content>
    </ion-card>

    <ion-card class="report-card">
      <ion-card-content>
        <ion-item lines="none" class="report-item">
          <ion-icon name="wallet-outline" slot="start" class="report-icon expense"></ion-icon>
          <ion-label>
            <h3>Expense Report</h3>
            <p>Business expense tracking</p>
          </ion-label>
          <div class="export-buttons" slot="end">
            <ion-button
              (click)="generatePayReport('expense','txt')"
              fill="outline"
              size="small"
              class="export-button txt">
              <ion-icon name="document-text-outline" slot="start"></ion-icon>
              TXT
            </ion-button>
            <ion-button
              (click)="generatePayReport('expense','csv')"
              fill="solid"
              size="small"
              class="export-button excel">
              <ion-icon name="document-outline" slot="start"></ion-icon>
              Excel
            </ion-button>
          </div>
        </ion-item>
      </ion-card-content>
    </ion-card>

    <ion-card class="report-card">
      <ion-card-content>
        <ion-item lines="none" class="report-item">
          <ion-icon name="card-outline" slot="start" class="report-icon payment"></ion-icon>
          <ion-label>
            <h3>Payment Mode Report</h3>
            <p>Payment method analysis</p>
          </ion-label>
          <div class="export-buttons" slot="end">
            <ion-button
              (click)="selectModeOfPayment('txt')"
              fill="outline"
              size="small"
              class="export-button txt">
              <ion-icon name="document-text-outline" slot="start"></ion-icon>
              TXT
            </ion-button>
            <ion-button
              (click)="selectModeOfPayment('csv')"
              fill="solid"
              size="small"
              class="export-button excel">
              <ion-icon name="document-outline" slot="start"></ion-icon>
              Excel
            </ion-button>
          </div>
        </ion-item>
      </ion-card-content>
    </ion-card>

    <ion-card class="report-card">
      <ion-card-content>
        <ion-item lines="none" class="report-item">
          <ion-icon name="trophy-outline" slot="start" class="report-icon incentive"></ion-icon>
          <ion-label>
            <h3>Incentive Report</h3>
            <p>Sales person incentive calculation</p>
          </ion-label>
          <div class="export-buttons" slot="end">
            <ion-button
              (click)="selectIncentiveReport('txt')"
              fill="outline"
              size="small"
              class="export-button txt">
              <ion-icon name="document-text-outline" slot="start"></ion-icon>
              TXT
            </ion-button>
            <ion-button
              (click)="selectIncentiveReport('csv')"
              fill="solid"
              size="small"
              class="export-button excel">
              <ion-icon name="document-outline" slot="start"></ion-icon>
              Excel
            </ion-button>
          </div>
        </ion-item>
      </ion-card-content>
    </ion-card>
  </div>

  <!-- Incentive Report Results -->
  <div class="incentive-results" *ngIf="selectIncentiveReportData">
    <h3 class="section-title">
      <ion-icon name="analytics-outline" class="section-icon"></ion-icon>
      Incentive Report Results
    </h3>

    <ion-card class="results-card">
      <ion-card-header>
        <ion-card-title class="results-title">
          <ion-icon name="person-circle-outline" class="results-icon"></ion-icon>
          {{selectedSalesPerson?.first_name}}
        </ion-card-title>
        <ion-card-subtitle>Sales Performance Summary</ion-card-subtitle>
      </ion-card-header>

      <ion-card-content>
        <ion-row>
          <ion-col size="6">
            <div class="result-item">
              <span class="result-label">Incentive Rate</span>
              <span class="result-value percentage">{{selectIncentiveReportData?.incentive_percentage}}%</span>
            </div>
          </ion-col>

          <ion-col size="6">
            <div class="result-item">
              <span class="result-label">Confirmed Sales</span>
              <span class="result-value sales">{{selectIncentiveReportData?.total_confirmed_sales | currency: 'INR':'symbol':'1.0-0'}}</span>
            </div>
          </ion-col>
        </ion-row>

        <ion-row>
          <ion-col size="12">
            <div class="result-item highlight">
              <span class="result-label">Total Incentive Amount</span>
              <span class="result-value amount">{{selectIncentiveReportData?.incentive_amount | currency: 'INR':'symbol':'1.0-0'}}</span>
            </div>
          </ion-col>
        </ion-row>
      </ion-card-content>
    </ion-card>
  </div>
</ion-content>
<ion-footer>
  <app-floating-menu></app-floating-menu>
</ion-footer>