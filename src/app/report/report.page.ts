import { Component, OnInit } from '@angular/core';
import { ReportsService } from '../shared/services/report.service';
import { IonLoaderService } from '../shared/services/ion-loader.service';
import { ToastService } from '../shared/services/toast.service';
import { PrintServiceService } from '../shared/services/print-service.service';
import { UtilService } from '../shared/services/util.service';
import * as <PERSON> from 'papaparse';
import exportFromJSON from 'export-from-json'
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { AlertController } from '@ionic/angular';

@Component({
  selector: 'app-report',
  templateUrl: './report.page.html',
  styleUrls: ['./report.page.scss'],
})
export class ReportPage implements OnInit {
  private apiUrl = environment.apiUrl;

  from_date: any;
  to_date: any;
  gstReport: boolean = JSON.parse(localStorage.getItem('metadata')).gstReport;
  modeOfPaymentSelectedOption: any;
  modeOfPaymentOptions: any = JSON.parse(localStorage.getItem('metadata')).modeOfPayment;
  salesPerson: any;
  selectedSalesPerson: any;
  selectIncentiveReportData: any;
  showFilters: boolean = false;

  // New properties for sales reports
  brands: any[] = [];
  selectedBrands: number[] = [];
  includeZeroSales: boolean = true;
  showBrandSelection: boolean = false;
  showZeroSalesToggle: boolean = false;

  constructor(
    private api: ReportsService,
    private toast: ToastService,
    public ionLoaderService: IonLoaderService,
    private http: HttpClient,
    public alertController: AlertController,
    private printService: PrintServiceService,
    private util: UtilService,
  ) { }

  ngOnInit() {
    console.log(this.modeOfPaymentOptions);
    this.loadBrands();
  }

  // Load brands for dropdown
  async loadBrands() {
    try {
      const response = await this.api.getBrands().toPromise();
      if (response && response.success) {
        this.brands = response.data || [];
      }
    } catch (error) {
      console.error('Error loading brands:', error);
    }
  }

  // Brand-wise Sales Report methods
  async selectBrandsForReport(export_type: string) {
    if (!this.from_date || !this.to_date) {
      this.toast.toastServices("Please select dates", 'danger', 'top');
      return;
    }

    const options = this.brands.map(brand => ({
      name: brand.name,
      type: 'checkbox' as const,
      label: brand.name,
      value: brand.id,
      checked: this.selectedBrands.includes(brand.id)
    }));

    const alert = await this.alertController.create({
      header: 'Select Brands',
      inputs: options,
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {
            console.log('Confirm Cancel');
          },
        },
        {
          text: 'Select All',
          handler: () => {
            this.selectedBrands = this.brands.map(brand => brand.id);
            this.generateBrandWiseSalesReport(export_type);
          },
        },
        {
          text: 'Ok',
          handler: (selectedBrandIds) => {
            this.selectedBrands = selectedBrandIds;
            this.generateBrandWiseSalesReport(export_type);
          },
        },
      ],
    });

    await alert.present();
  }

  async generateBrandWiseSalesReport(export_type: string) {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api.getBrandWiseSalesReport(
        this.from_date, 
        this.to_date, 
        this.selectedBrands.length > 0 ? this.selectedBrands : undefined,
        this.includeZeroSales
      ).subscribe(async (res: any) => {
        if (res.success) {
          this.toast.toastServices(res.message, 'success', 'top');
          if (res.data && res.data.length) {
            let fileName = this.getFilename('brand_wise_sales');
            switch (export_type) {
              case 'csv':
                this.generateCSV(res.data, fileName);
                break;
              case 'txt': {
                const tableContent = this.api.generatePrintableHTML(res.data);
                const dateRange = `${this.from_date} to ${this.to_date}`;
                const additionalInfo = this.selectedBrands.length > 0 ?
                  `Brands: ${this.brands.filter(b => this.selectedBrands.includes(b.id)).map(b => b.name).join(', ')}` :
                  'All Brands';
                const htmlContent = this.printService.generateEnhancedReportHTML(
                  fileName,
                  dateRange,
                  tableContent,
                  additionalInfo
                );
                this.printService.printEnhancedReport(htmlContent, fileName);
                break;
              }
              default:
                break;
            }
          } else {
            this.toast.toastServices("No Data to export", 'danger', 'top');
          }
        } else {
          this.toast.toastServices(res.message, 'danger', 'top');
        }
        this.ionLoaderService.dismissLoader();
      }, err => {
        this.ionLoaderService.dismissLoader();
        this.toast.toastServices("Error generating report", 'danger', 'top');
      });
    });
  }

  // General Sales Report methods
  async generateGeneralSalesReport(export_type: string) {
    if (!this.from_date || !this.to_date) {
      this.toast.toastServices("Please select dates", 'danger', 'top');
      return;
    }

    await this.ionLoaderService.startLoader().then(async () => {
      await this.api.getGeneralSalesReport(
        this.from_date, 
        this.to_date, 
        this.includeZeroSales
      ).subscribe(async (res: any) => {
        if (res.success) {
          this.toast.toastServices(res.message, 'success', 'top');
          if (res.data && res.data.length) {
            let fileName = this.getFilename('general_sales');
            switch (export_type) {
              case 'csv':
                this.generateCSV(res.data, fileName);
                break;
              case 'txt': {
                const tableContent = this.api.generatePrintableHTML(res.data);
                const dateRange = `${this.from_date} to ${this.to_date}`;
                const htmlContent = this.printService.generateEnhancedReportHTML(
                  fileName,
                  dateRange,
                  tableContent
                );
                this.printService.printEnhancedReport(htmlContent, fileName);
                break;
              }
              default:
                break;
            }
          } else {
            this.toast.toastServices("No Data to export", 'danger', 'top');
          }
        } else {
          this.toast.toastServices(res.message, 'danger', 'top');
        }
        this.ionLoaderService.dismissLoader();
      }, err => {
        this.ionLoaderService.dismissLoader();
        this.toast.toastServices("Error generating report", 'danger', 'top');
      });
    });
  }

  // Toggle zero sales inclusion
  toggleZeroSales() {
    this.includeZeroSales = !this.includeZeroSales;
  }

  async selectModeOfPayment(export_type) {

    const options = this.modeOfPaymentOptions.map(item => ({
      name: item.name,
      type: 'radio',
      label: item.name,
      value: item.slug,
      checked: false
    }));
    const alert = await this.alertController.create({
      header: 'Select Mode of Payment',
      inputs: options,
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {
            console.log('Confirm Cancel');
          },
        },
        {
          text: 'Ok',
          handler: (mode) => {
            this.modeOfPaymentSelectedOption = mode;
            this.modeOfPaymentReport(export_type)
          },
        },
      ],
    });

    await alert.present();
  }

  async selectIncentiveReport(export_type) {
    let salesperson : any;
    this.api.getSalesPerson().subscribe(async (res: any) => {
      salesperson = res.data
      console.log(res);
      if (res.data.length < 1) {
        this.toast.toastServices("No sales person to be selected","danger","bottom")
      }
      this.salesPerson = res.data
      const options = salesperson.map(item => ({
        name: item.first_name,
        type: 'radio',
        label: item.first_name,
        value: item.id,
        checked: false
      }));
      const alert = await this.alertController.create({
        header: 'Select Sales Person',
        inputs: options,
        buttons: [
          {
            text: 'Cancel',
            role: 'cancel',
            cssClass: 'secondary',
            handler: () => {
              console.log('Confirm Cancel');
            },
          },
          {
            text: 'Ok',
            handler: (data) => {
              console.log(data);
              this.generateIncentiveReport(data,export_type)
            },
          },
        ],
      });
  
      await alert.present();
      
    });
   
  }
  async generateIncentiveReport(sales_person,export_type) {
    if (!this.from_date || !this.to_date) {
      this.toast.toastServices("please select dates", 'danger', 'top')
      return
    }
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api.getIncentiveReport(this.from_date, this.to_date,sales_person).subscribe(async (res: any) => {
        if (res.success) {
          this.toast.toastServices(res.message, 'success', 'top');
          console.log(res.data);
          this.selectIncentiveReportData = res.data
          this.selectedSalesPerson = this.salesPerson.find(e=> e.id == res.data.salesman_id)
        }
        else {
          this.toast.toastServices(res.message, 'danger', 'top')
        }
        this.ionLoaderService.dismissLoader();

      },err=>{
        this.ionLoaderService.dismissLoader();
      });
    });
  }
  async generateGstReport() {
    try {
      await this.ionLoaderService.startLoader();

      // Make the API call and set responseType to 'text' to handle CSV
      const response = await this.http
        .get(`${this.apiUrl}/sales_invoice_report/?from_date=${this.from_date}&to_date=${this.to_date}`, { responseType: 'text' })
        .toPromise();


      if (response) {
        this.toast.toastServices("Fetched successfully", 'success', 'top');

        // Create a Blob from the CSV response
        const blob = new Blob([response], { type: 'text/csv' });

        // Generate a download link and trigger it
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = `GST_Report_${this.from_date}_to_${this.to_date}.csv`;
        link.click();

        // Clean up the URL object
        window.URL.revokeObjectURL(link.href);
      } else {
        this.toast.toastServices("Unexpected empty response", 'danger', 'top');
      }
    } catch (error) {
      console.error("Error fetching GST report:", error);
      this.toast.toastServices("Failed to fetch GST report. Please try again.", 'danger', 'top');
    } finally {
      this.ionLoaderService.dismissLoader();
    }
  }

  async generatePayReport(report_type, export_type) {
    if (!this.from_date || !this.to_date) {
      this.toast.toastServices("please select dates", 'danger', 'top')
      return
    }
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api.getPayInReport(this.from_date, this.to_date, report_type).subscribe(async (res: any) => {
        if (res.success) {
          this.toast.toastServices(res.message, 'success', 'top');
          if (res.data.length) {
            let fileName = this.getFilename(report_type)
            switch (export_type) {
              case 'csv':
                this.generateCSV(res.data, fileName)
                break;
              case 'txt':
                this.api.getPrint(this.api.generatePrintableHTML(res.data), fileName)
                break;
              default:
                break;
            }
          } else {
            this.toast.toastServices("No Data to export", 'danger', 'top')
          }
        }
        else {
          this.toast.toastServices(res.message, 'danger', 'top')
        }
        this.ionLoaderService.dismissLoader();

      });
    });
  }
  async modeOfPaymentReport(export_type) {


    if (!this.from_date || !this.to_date) {
      this.toast.toastServices("please select dates", 'danger', 'top')
      return
    }
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api.getModeOfPaymentReport(this.from_date, this.to_date, 'mode_of_payment', this.modeOfPaymentSelectedOption).subscribe(async (res: any) => {
        if (res.success) {
          this.toast.toastServices(res.message, 'success', 'top');
          if (res.data.length) {
            let fileName = this.getFilename('mode_of_payment')
            switch (export_type) {
              case 'csv':
                this.generateCSV(res.data, fileName)
                break;
              case 'txt':
                this.api.getPrint(this.api.generatePrintableHTML(res.data), fileName)
                break;
              default:
                break;
            }
          } else {
            this.toast.toastServices("No Data to export", 'danger', 'top')
          }
        }
        else {
          this.toast.toastServices(res.message, 'danger', 'top')
        }
        this.ionLoaderService.dismissLoader();

      });
    });

  }
  generateCSV(data, fileName) {
    // Check if running on mobile (Cordova/Capacitor)
    if (this.util.isCordova()) {
      this.generateCSVForMobile(data, fileName);
    } else {
      // Web browser - use existing method
      let columns = Object.keys(data[0]);
      let export_data = JSON.stringify(data, columns);
      let exportType = exportFromJSON.types.csv;
      exportFromJSON({ data: JSON.parse(export_data), fileName, exportType });
    }
  }

  async generateCSVForMobile(data, fileName) {
    try {
      // Import Capacitor Filesystem dynamically
      const { Filesystem } = await import('@capacitor/filesystem');
      
      // Convert data to CSV format
      let columns = Object.keys(data[0]);
      let csvContent = columns.join(',') + '\n';
      
      data.forEach(row => {
        let rowData = columns.map(col => {
          let value = row[col] || '';
          // Escape quotes and wrap in quotes if contains comma
          if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
            value = '"' + value.replace(/"/g, '""') + '"';
          }
          return value;
        });
        csvContent += rowData.join(',') + '\n';
      });

      // Write file to device
      const result = await Filesystem.writeFile({
        path: `${fileName}.csv`,
        data: csvContent,
        directory: 'DOCUMENTS' as any,
        encoding: 'utf8' as any
      });

      this.toast.toastServices(`File saved as ${fileName}.csv`, 'success', 'top');
      console.log('File saved:', result.uri);
      
    } catch (error) {
      console.error('Error saving CSV file:', error);
      this.toast.toastServices('Failed to save file. Please try again.', 'danger', 'top');
      
      // Fallback to web method if mobile fails
      try {
        let columns = Object.keys(data[0]);
        let export_data = JSON.stringify(data, columns);
        let exportType = exportFromJSON.types.csv;
        exportFromJSON({ data: JSON.parse(export_data), fileName, exportType });
      } catch (fallbackError) {
        console.error('Fallback also failed:', fallbackError);
        this.toast.toastServices('Unable to download file', 'danger', 'top');
      }
    }
  }

  getFilename(report_type) {
    let title = "Report ";
    switch (report_type) {
      case 'pay-in':
        title = 'Pay In Report '
        break;
      case 'pay-out':
        title = 'Pay Out Report '
        break;
      case 'expense':
        title = 'Expense Report '
        break;
      case 'mode_of_payment':
        title = 'Payment Mode Report'
        break;
      case 'incentive_report':
        title = 'Incentive Report'
        break;
      case 'brand_wise_sales':
        title = 'Brand-wise Sales Report '
        break;
      case 'general_sales':
        title = 'General Sales Report '
        break;
      default:
        break;
    }
    return title + this.from_date + '-' + this.to_date;
  }

  toggleFilters() {
    this.showFilters = !this.showFilters;
  }

  getAvailableReportsCount(): number {
    let count = 7; // Base reports: Pay IN, Pay Out, Expense, Payment Mode, Incentive, Brand-wise Sales, General Sales
    if (this.gstReport) {
      count += 1; // Add GST report if enabled
    }
    return count;
  }

  getSelectedPeriodDays(): number {
    if (!this.from_date || !this.to_date) {
      return 0;
    }
    const fromDate = new Date(this.from_date);
    const toDate = new Date(this.to_date);
    const timeDiff = toDate.getTime() - fromDate.getTime();
    const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1; // +1 to include both start and end dates
    return daysDiff > 0 ? daysDiff : 0;
  }
}
