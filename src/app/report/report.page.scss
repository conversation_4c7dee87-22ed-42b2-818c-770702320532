/* Reports Page Styling */
.reports-content {
  --background: #f8f9fa;
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 16px;
  --padding-bottom: 16px;
}

/* Section Styling */
.summary-section,
.filter-section,
.reports-section,
.incentive-results {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.section-icon {
  font-size: 24px;
  margin-right: 8px;
  color: var(--ion-color-primary);
}

/* Summary Cards */
.summary-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.summary-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-info h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
}

.summary-info h1 {
  margin: 0 0 4px 0;
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
}

.summary-info p {
  margin: 0;
  font-size: 12px;
  color: #6c757d;
}

.summary-icon {
  font-size: 48px;
  opacity: 0.8;
}

.summary-icon.reports {
  color: #007bff;
}

.summary-icon.period {
  color: #28a745;
}

.summary-icon.gst {
  color: #6f42c1;
}

.summary-icon.formats {
  color: #fd7e14;
}

.reports-card {
  background: linear-gradient(135deg, #e3f2fd 0%, #f1f8ff 100%);
}

.period-card {
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
}

.gst-card {
  background: linear-gradient(135deg, #f3e5f5 0%, #faf2fb 100%);
}

.formats-card {
  background: linear-gradient(135deg, #fff3e0 0%, #fef8f0 100%);
}

/* Collapsible Filter Section */
.filter-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.filter-header:hover {
  background-color: #f8f9fa;
}

.toggle-button {
  --padding-start: 8px;
  --padding-end: 8px;
  margin: 0;
}

.toggle-icon {
  font-size: 20px;
  color: #6c757d;
  transition: transform 0.3s ease;
}

.filter-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.filter-content.expanded {
  max-height: 200px;
}

.filter-card {
  margin: 0;
  border-radius: 0;
  box-shadow: none;
  border-top: 1px solid #e9ecef;
}

.date-item {
  --padding-start: 0;
  --padding-end: 0;
  --background: transparent;
}

.date-input {
  --padding-start: 12px;
  --padding-end: 12px;
  --background: #f8f9fa;
  border-radius: 8px;
  margin-top: 4px;
}

/* Toggle Item Styling */
.toggle-item {
  --padding-start: 0;
  --padding-end: 0;
  --background: transparent;
  margin-top: 8px;
}

.toggle-item ion-label {
  font-weight: 500;
  color: #495057;
}

/* Reports Section */
.reports-section {
  margin-top: 24px;
}

.report-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 16px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.report-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.report-item {
  --min-height: 80px;
  --padding-start: 16px;
  --padding-end: 16px;
}

.report-icon {
  font-size: 32px;
  margin-right: 16px;
  border-radius: 8px;
  padding: 8px;
  background: rgba(0, 123, 255, 0.1);
}

.report-icon.gst {
  color: #6f42c1;
  background: rgba(111, 66, 193, 0.1);
}

.report-icon.pay-in {
  color: #28a745;
  background: rgba(40, 167, 69, 0.1);
}

.report-icon.pay-out {
  color: #dc3545;
  background: rgba(220, 53, 69, 0.1);
}

.report-icon.expense {
  color: #fd7e14;
  background: rgba(253, 126, 20, 0.1);
}

.report-icon.payment {
  color: #17a2b8;
  background: rgba(23, 162, 184, 0.1);
}

.report-icon.incentive {
  color: #ffc107;
  background: rgba(255, 193, 7, 0.1);
}

.report-icon.brand-sales {
  color: #e83e8c;
  background: rgba(232, 62, 140, 0.1);
}

.report-icon.general-sales {
  color: #20c997;
  background: rgba(32, 201, 151, 0.1);
}

.report-item ion-label h3 {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 4px 0;
}

.report-item ion-label p {
  font-size: 14px;
  color: #6c757d;
  margin: 0;
  line-height: 1.4;
}

.export-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.export-button {
  height: 36px;
  font-size: 12px;
  font-weight: 500;
  min-width: 80px;
  --border-radius: 8px;
  --padding-start: 12px;
  --padding-end: 12px;
}

.export-button.txt {
  --color: #6c757d;
  --border-color: #6c757d;
  --background: transparent;
}

.export-button.excel {
  --color: #ffffff;
  --background: #28a745;
  --border-color: #28a745;
}

/* Incentive Results Section */
.incentive-results {
  margin-top: 32px;
}

.results-card {
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.results-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.results-icon {
  font-size: 24px;
  margin-right: 12px;
  color: var(--ion-color-primary);
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #e9ecef;
}

.result-item:last-child {
  border-bottom: none;
}

.result-item.highlight {
  background: linear-gradient(135deg, #e3f2fd 0%, #f1f8ff 100%);
  margin: 0 -16px;
  padding: 20px 16px;
  border-radius: 8px;
}

.result-label {
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
}

.result-value {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.result-value.percentage {
  color: #28a745;
}

.result-value.sales {
  color: #007bff;
}

.result-value.amount {
  font-size: 24px;
  color: #28a745;
}

/* Responsive Design */
@media (max-width: 576px) {
  .reports-content {
    --padding-start: 12px;
    --padding-end: 12px;
  }

  .section-title {
    font-size: 18px;
  }

  .section-icon {
    font-size: 20px;
  }

  .report-item {
    --min-height: 70px;
  }

  .report-icon {
    font-size: 24px;
    margin-right: 12px;
  }

  .report-item ion-label h3 {
    font-size: 14px;
  }

  .report-item ion-label p {
    font-size: 12px;
  }

  .export-buttons {
    flex-direction: column;
    gap: 6px;
  }

  .export-button {
    height: 32px;
    font-size: 11px;
    min-width: 70px;
  }

  .result-item {
    padding: 12px 8px;
  }

  .result-value {
    font-size: 16px;
  }

  .result-value.amount {
    font-size: 20px;
  }

  .results-title {
    font-size: 16px;
  }

  .results-icon {
    font-size: 20px;
  }
}

@media (min-width: 768px) {
  .reports-content {
    --padding-start: 24px;
    --padding-end: 24px;
  }

  .section-title {
    font-size: 22px;
  }

  .section-icon {
    font-size: 26px;
  }

  .report-icon {
    font-size: 36px;
  }

  .export-button {
    height: 40px;
    font-size: 13px;
    min-width: 90px;
  }

  .result-value {
    font-size: 20px;
  }

  .result-value.amount {
    font-size: 28px;
  }
}

@media (min-width: 1024px) {
  .reports-content {
    --padding-start: 32px;
    --padding-end: 32px;
  }

  .section-title {
    font-size: 24px;
  }

  .section-icon {
    font-size: 28px;
  }

  .report-icon {
    font-size: 40px;
  }

  .export-button {
    height: 44px;
    font-size: 14px;
    min-width: 100px;
  }

  .result-value {
    font-size: 22px;
  }

  .result-value.amount {
    font-size: 32px;
  }
}

.report-content {
  --background: var(--ion-background-color);
  --padding-start: 0;
  --padding-end: 0;
}