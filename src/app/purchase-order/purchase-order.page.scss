.purchase-order-content {
  --background: var(--ion-background-color);
  --padding-start: 0;
  --padding-end: 0;
}

// View Toggle Section
.view-toggle-section {
  padding: 16px;
  background: white;
  margin-bottom: 8px;

  ion-segment {
    --background: #f1f3f4;
    border-radius: 8px;

    ion-segment-button {
      --color: #5f6368;
      --color-checked: #1976d2;
      --background-checked: white;
      --border-radius: 6px;
      margin: 2px;
      font-weight: 500;
    }
  }
}

// Summary Section
.summary-section {
  margin: 8px 16px;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .summary-header {
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    border-bottom: 1px solid #e0e0e0;

    .section-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #1976d2;
      display: flex;
      align-items: center;

      .section-icon {
        margin-right: 8px;
        font-size: 20px;
      }
    }

    .toggle-button {
      --color: #666;
      margin: 0;
    }
  }

  .summary-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;

    &.expanded {
      max-height: 500px;
    }

    .summary-card {
      margin: 8px;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

      &.total-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }

      &.approved-card {
        background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
        color: white;
      }

      &.pending-card {
        background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
        color: white;
      }

      &.draft-card {
        background: linear-gradient(135deg, #9e9e9e 0%, #757575 100%);
        color: white;
      }

      .summary-card-content {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .summary-info {
          h3 {
            margin: 0 0 4px 0;
            font-size: 14px;
            opacity: 0.9;
          }

          h1 {
            margin: 0 0 4px 0;
            font-size: 28px;
            font-weight: bold;
          }

          p {
            margin: 0;
            font-size: 12px;
            opacity: 0.8;
          }
        }

        .summary-illustration {
          .summary-icon {
            font-size: 40px;
            opacity: 0.3;
          }
        }
      }
    }
  }
}

// Filters Section
.filters-section {
  margin: 8px 16px;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .filters-header {
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    border-bottom: 1px solid #e0e0e0;

    .section-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #1976d2;
      display: flex;
      align-items: center;

      .section-icon {
        margin-right: 8px;
        font-size: 20px;
      }
    }

    .toggle-button {
      --color: #666;
      margin: 0;
    }
  }

  .filters-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;

    &.expanded {
      max-height: 300px;
    }

    .filter-actions {
      padding: 16px;
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
  }
}

// No Data Section
.no-data-section {
  padding: 16px;

  .no-data-card {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .no-data-content {
      text-align: center;
      padding: 32px 16px;

      .no-data-icon {
        font-size: 64px;
        color: #ccc;
        margin-bottom: 16px;
      }

      h3 {
        margin: 0 0 8px 0;
        color: #333;
        font-weight: 600;
      }

      p {
        margin: 0 0 24px 0;
        color: #666;
        font-size: 14px;
      }
    }
  }
}

// Purchase Orders List
.po-list, .draft-list {
  padding: 0 16px 16px 16px;

  .po-item, .draft-item {
    background: white;
    border-radius: 12px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    cursor: pointer;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    .po-icon, .draft-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      background: linear-gradient(135deg, #1976d2, #1565c0);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;

      .document-icon {
        color: white;
        font-size: 24px;
      }
    }

    .po-details, .draft-details {
      .supplier-name {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin: 0 0 4px 0;
      }

      .po-meta, .draft-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .po-number, .po-date, .draft-date {
          font-size: 12px;
          color: #666;
        }

        .po-number {
          font-weight: 600;
          color: #1976d2;
        }

        .forecast-based {
          display: flex;
          align-items: center;
          font-size: 12px;
          color: #4caf50;

          .forecast-icon {
            margin-right: 4px;
            font-size: 14px;
          }
        }
      }

      .po-status-amount, .draft-amount-items {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .status-chip {
          height: 24px;
          font-size: 11px;
          font-weight: 600;
        }

        .po-amount, .draft-amount {
          font-size: 16px;
          font-weight: 600;
          color: #1976d2;
        }

        .items-count {
          font-size: 12px;
          color: #666;
        }
      }

      .po-additional-info, .draft-badges {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .items-count {
          font-size: 12px;
          color: #666;
        }

        .delivery-date {
          display: flex;
          align-items: center;
          font-size: 11px;
          color: #4caf50;

          .delivery-icon {
            margin-right: 4px;
            font-size: 12px;
          }
        }

        .auto-generated {
          display: flex;
          align-items: center;
          font-size: 11px;
          color: #ff9800;

          .auto-icon {
            margin-right: 4px;
            font-size: 12px;
          }
        }

        .auto-chip {
          height: 20px;
          font-size: 10px;
        }
      }

      .po-order-details {
        margin-top: 8px;
        padding-top: 8px;
        border-top: 1px solid #f0f0f0;

        .remarks, .terms {
          display: flex;
          align-items: flex-start;
          margin-bottom: 4px;
          font-size: 11px;
          color: #666;

          .remarks-icon, .terms-icon {
            margin-right: 6px;
            margin-top: 1px;
            font-size: 12px;
            color: #999;
            flex-shrink: 0;
          }

          .remarks-text, .terms-text {
            line-height: 1.3;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
        }
      }
    }

    .po-actions, .draft-actions {
      display: flex;
      flex-direction: column;
      gap: 4px;

      ion-button {
        --padding-start: 8px;
        --padding-end: 8px;
        height: 32px;
        width: 32px;
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .summary-content {
    .summary-card {
      .summary-card-content {
        .summary-info {
          h1 {
            font-size: 24px;
          }
        }

        .summary-illustration {
          .summary-icon {
            font-size: 32px;
          }
        }
      }
    }
  }

  .po-list, .draft-list {
    .po-item, .draft-item {
      .po-actions, .draft-actions {
        flex-direction: row;
        gap: 2px;

        ion-button {
          height: 28px;
          width: 28px;
        }
      }
    }
  }
}



.order-title {
  color: var(--ion-text-color);
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 4px;
}

.order-subtitle {
  color: var(--ion-text-color-step-600);
  font-size: 14px;
  margin-bottom: 4px;
}

.status-pending {
  color: var(--ion-color-warning);
}