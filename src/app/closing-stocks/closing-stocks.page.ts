import { Component, OnInit } from '@angular/core';
import { Platform } from '@ionic/angular';
import { AlertService } from '../shared/services/alert.service';
import { IonLoaderService } from '../shared/services/ion-loader.service';
import { PrintServiceService } from '../shared/services/print-service.service';
import { PurchasePaymentService } from '../shared/services/purchase-payment.service';
import { SuppliersService } from '../shared/services/supplier.service';
import { ToastService } from '../shared/services/toast.service';
import { UtilService } from '../shared/services/util.service';
import { UserApiService } from '../shared/services/user.service';
import * as moment from 'moment';

@Component({
  selector: 'app-closing-stocks',
  templateUrl: './closing-stocks.page.html',
  styleUrls: ['./closing-stocks.page.scss'],
})
export class ClosingStocksPage implements OnInit {

  from_date: any = moment().subtract(30, 'days').format("YYYY-MM-DD");
  to_date: any = moment().format("YYYY-MM-DD");
  closingStocks: any;
  constructor(
    private api: UserApiService,
    private toast: ToastService,
    public ionLoaderService: IonLoaderService,
    public platform: Platform,
    public alertService: AlertService,
    private printService: PrintServiceService,
    private util: UtilService
  ) { }

  ngOnInit() {
  }

  ionViewWillEnter(){
    this.getData()
  }

  async getData(){
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api.getClosingStock( this.from_date, this.to_date).then(async (res: any) => {
        if (res.success) {
          this.toast.toastServices(res.message, 'success', 'top');
          this.closingStocks = res.data;
          console.log(res);
          
        }
        else {
          this.toast.toastServices(res.message, 'danger', 'top')
        }
        this.ionLoaderService.dismissLoader();

      }).catch((err) => {
        this.toast.toastServices(err, 'danger', 'top')
        this.ionLoaderService.dismissLoader();
      });
    });
  }


  printStatement() {
    const items = this.closingStocks.map((d, index) => {
      return `
        <tr>
          <td class="name-cell">${d.product}</td>
          <td class="number-cell">${d.opening_qty}.${Math.abs(d.opening_pcs)}</td>
          <td class="number-cell">${d.total_purchased_qty}.${Math.abs(d.total_purchased_pcs)}</td>
          <td class="number-cell">${d.total_sold_qty}.${Math.abs(d.total_sold_pcs)}</td>
          <td class="number-cell">${d.closing_qty}.${Math.abs(d.closing_pcs)}</td>
        </tr>
      `;
    }).join("");

    const dateRange = `${this.from_date} to ${this.to_date}`;

    // Calculate totals
    const totals = this.closingStocks.reduce((acc, stock) => {
      acc.totalOpening += stock.opening_qty;
      acc.totalPurchased += stock.total_purchased_qty;
      acc.totalSold += stock.total_sold_qty;
      acc.totalClosing += stock.closing_qty;
      return acc;
    }, { totalOpening: 0, totalPurchased: 0, totalSold: 0, totalClosing: 0 });

    const tableContent = `
      <table>
        <thead>
          <tr>
            <th>Product</th>
            <th>Opening Stock</th>
            <th>Purchased</th>
            <th>Sold</th>
            <th>Closing Stock</th>
          </tr>
        </thead>
        <tbody>
          ${items}
          <tr class="total-row">
            <td><strong>Total</strong></td>
            <td><strong>${totals.totalOpening}</strong></td>
            <td><strong>${totals.totalPurchased}</strong></td>
            <td><strong>${totals.totalSold}</strong></td>
            <td><strong>${totals.totalClosing}</strong></td>
          </tr>
        </tbody>
      </table>
    `;

    const htmlContent = this.printService.generateEnhancedReportHTML(
      'Closing Stock Report',
      dateRange,
      tableContent,
      `Total Products: ${this.closingStocks.length} | Total Closing Stock: ${totals.totalClosing} units`
    );

    this.printService.printEnhancedReport(htmlContent, `Closing Stock Report - ${dateRange}`);
  }

}
