<app-header [title]="'Closing Stock'" [returnUrl]="'tabs/home'"></app-header>

<ion-content>
  <ion-row size="small">
    <ion-col size="5">
      <ion-item lines="none">
        <ion-label position="stacked">From Date</ion-label>
        <ion-input [(ngModel)]="from_date" [value]="from_date | date : 'YYYY-MM-dd' " placeholder="Enter Date"
          type="date">
        </ion-input>
      </ion-item>
    </ion-col>
    <ion-col size="5">
      <ion-item lines="none">
        <ion-label position="stacked">To Date</ion-label>
        <ion-input [(ngModel)]="to_date" [value]="to_date | date : 'YYYY-MM-dd' " placeholder="Enter Date" type="date">
        </ion-input>
      </ion-item>
    </ion-col>
    <ion-col size="2">
      <ion-item lines="none">
        <ion-button (click)="getData()" fill="solid" shape="round">
          <ion-icon name="filter"></ion-icon>
        </ion-button>
      </ion-item>
    </ion-col>
  </ion-row>
  <ion-card *ngIf="closingStocks" style="max-width: 100%;overflow-x:scroll;padding: 8px;" color="light">
    <table>
      <tr>
        <th>Product</th>
        <th>Opening</th>
        <th>Purchase</th>
        <th>Sales</th>
        <th>Closing</th>
      </tr>
      <tr *ngFor="let closingStock of closingStocks">
        <td>{{closingStock.product}}</td>
        <td>{{closingStock.opening_qty}}.{{-closingStock.opening_pcs}}</td>
        <td>{{closingStock.total_purchased_qty}}.{{-closingStock.total_purchased_pcs}}</td>
        <td>{{closingStock.total_sold_qty}}.{{-closingStock.total_sold_pcs}}</td>
        <td>{{closingStock.closing_qty}}.{{-closingStock.closing_pcs}}</td>
      </tr>
    </table>
  </ion-card>
</ion-content>
<ion-footer>
  <ion-fab *ngIf="closingStocks" vertical="bottom" horizontal="start">
    <ion-fab-button (click)="printStatement()">
      <ion-icon name="print"></ion-icon>
    </ion-fab-button>
  </ion-fab>
  <app-floating-menu></app-floating-menu>
</ion-footer>