/* Sales Payment Page Styling */
.sales-payment-content {
  --background: var(--ion-background-color);
  --padding-start: 0;
  --padding-end: 0;
  --padding-top: 16px;
  --padding-bottom: 16px;
}

/* Section Styling */
.filter-section,
.payment-data-section,
.no-data-section {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.section-icon {
  font-size: 24px;
  margin-right: 8px;
  color: var(--ion-color-primary);
}

/* Filter Card */
.filter-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --background: white;
}

.date-item,
.buyer-item {
  --background: transparent;
  --padding-start: 0;
  --padding-end: 0;
  margin-bottom: 8px;
}

.date-input {
  --background: #f8f9fa;
  --border-radius: 8px;
  --padding-start: 12px;
  --padding-end: 12px;
}

.buyer-selector {
  --background: #f8f9fa;
  --border-radius: 8px;
}

.filter-button {
  --border-radius: 12px;
  height: 44px;
  margin-top: 20px;
}

/* Summary Cards */
.summary-row {
  margin-bottom: 16px;
}

.summary-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 8px;
  --background: white;
  transition: transform 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-2px);
}

.summary-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-info h3 {
  margin: 0 0 4px 0;
  font-size: 12px;
  font-weight: 500;
  color: #6c757d;
  text-transform: uppercase;
}

.summary-info h1 {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  color: #2c3e50;
}

.summary-icon {
  font-size: 24px;
  opacity: 0.7;
}

.total-bills .summary-icon {
  color: #6f42c1;
}

.bill-amount .summary-icon {
  color: #007bff;
}

.received-amount .summary-icon {
  color: #28a745;
}

/* Data Table Card */
.data-table-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --background: white;
}

.table-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.table-icon {
  font-size: 20px;
  margin-right: 8px;
  color: var(--ion-color-primary);
}

.table-container {
  overflow-x: auto;
  border-radius: 8px;
}

.payment-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.table-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.table-header th {
  padding: 12px 8px;
  text-align: left;
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
  white-space: nowrap;
}

.table-row {
  transition: background-color 0.3s ease;
}

.table-row:hover {
  background-color: #f8f9fa;
}

.table-row td {
  padding: 12px 8px;
  border-bottom: 1px solid #dee2e6;
  vertical-align: middle;
}

.bill-id {
  font-weight: 600;
  color: var(--ion-color-primary);
}

.buyer-name {
  font-weight: 500;
  color: #2c3e50;
}

.bill-date {
  color: #6c757d;
  font-size: 13px;
}

.old-balance,
.bill-amount,
.paid-amount,
.current-balance {
  font-weight: 500;
  text-align: right;
}

.bill-amount {
  color: #007bff;
}

.paid-amount {
  color: #28a745;
}

.current-balance {
  color: #dc3545;
}

.table-footer {
  background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%);
  font-weight: 600;
}

.table-footer td {
  padding: 16px 8px;
  border-top: 2px solid #dee2e6;
  border-bottom: none;
}

.total-label {
  text-align: center;
  color: #495057;
  font-size: 16px;
}

.total-bill {
  color: #007bff;
  text-align: right;
}

.total-received {
  color: #28a745;
  text-align: right;
}

.total-balance {
  color: #dc3545;
  text-align: right;
}

/* No Data Section */
.no-data-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --background: white;
}

.no-data-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.no-data-icon {
  font-size: 64px;
  color: #dee2e6;
  margin-bottom: 16px;
}

.no-data-content h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #6c757d;
}

.no-data-content p {
  margin: 0;
  font-size: 14px;
  color: #adb5bd;
}

/* Responsive Design */
@media (max-width: 576px) {
  .sales-payment-content {
    --padding-start: 12px;
    --padding-end: 12px;
  }

  .section-title {
    font-size: 18px;
  }

  .section-icon {
    font-size: 20px;
  }

  .filter-button {
    height: 40px;
    font-size: 12px;
  }

  .summary-info h1 {
    font-size: 16px;
  }

  .summary-icon {
    font-size: 20px;
  }

  .payment-table {
    font-size: 12px;
  }

  .table-header th,
  .table-row td,
  .table-footer td {
    padding: 8px 4px;
  }

  .table-title {
    font-size: 16px;
  }

  .table-icon {
    font-size: 18px;
  }

  .no-data-icon {
    font-size: 48px;
  }

  .no-data-content h3 {
    font-size: 16px;
  }

  .no-data-content p {
    font-size: 12px;
  }
}

@media (min-width: 768px) {
  .sales-payment-content {
    --padding-start: 24px;
    --padding-end: 24px;
  }

  .section-title {
    font-size: 22px;
  }

  .section-icon {
    font-size: 26px;
  }

  .summary-info h1 {
    font-size: 20px;
  }

  .summary-icon {
    font-size: 28px;
  }

  .payment-table {
    font-size: 15px;
  }

  .table-header th,
  .table-row td,
  .table-footer td {
    padding: 14px 10px;
  }
}

@media (min-width: 1024px) {
  .sales-payment-content {
    --padding-start: 32px;
    --padding-end: 32px;
  }

  .section-title {
    font-size: 24px;
  }

  .section-icon {
    font-size: 28px;
  }

  .summary-info h1 {
    font-size: 22px;
  }

  .summary-icon {
    font-size: 32px;
  }

  .payment-table {
    font-size: 16px;
  }

  .table-header th,
  .table-row td,
  .table-footer td {
    padding: 16px 12px;
  }
}

.payment-item {
  --background: var(--ion-item-background);
  --border-radius: 12px;
  --padding-start: 16px;
  --padding-end: 16px;
  margin: 0 12px 8px 12px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.payment-modal {
  --background: var(--ion-card-background);
  --border-radius: 16px;
  --width: 95%;
  --max-width: 600px;
  --height: 90%;
  --max-height: 800px;
}

.payment-form {
  --background: var(--ion-card-background);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}