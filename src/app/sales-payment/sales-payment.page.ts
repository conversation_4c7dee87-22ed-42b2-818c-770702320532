import { SalesPaymentService } from './../shared/services/sales-payment.service';
import { Component, OnInit } from '@angular/core';
import { Platform } from '@ionic/angular';
import { AlertService } from '../shared/services/alert.service';
import { IonLoaderService } from '../shared/services/ion-loader.service';

import { ToastService } from '../shared/services/toast.service';
import { BuyerService } from '../shared/services/buyer.service';
import { PrintServiceService } from '../shared/services/print-service.service';
import { h } from 'ionicons/dist/types/stencil-public-runtime';
import { UtilService } from '../shared/services/util.service';

@Component({
  selector: 'app-sales-payment',
  templateUrl: './sales-payment.page.html',
  styleUrls: ['./sales-payment.page.scss'],
})
export class SalesPaymentPage implements OnInit {
  buyer_data: any;
  selectedBuyer = null;
  from_date: any;
  to_date: any;
  salesPaymentData: any;
  previous_balance_total: number;
  bill_amount_total: number;

  received_amount_total: number;
  current_balance_total: number;
  lineAccountData: any;

  constructor(
    private api: SalesPaymentService,
    private toast: ToastService,
    public ionLoaderService: IonLoaderService,
    public platform: Platform,
    public alertService: AlertService,
    private buyer_api: BuyerService,
    private printService: PrintServiceService,
    private util : UtilService,

  ) { }

  ngOnInit() {

  }
  ionViewWillEnter() {
    this.getBuyer()
  }
  async getBuyer() {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.buyer_api.getBuyer().then(async (res: any) => {
        if (res.success) {
          this.toast.toastServices(res.message, 'success', 'top');
          this.buyer_data = res.data;
        }
        else {
          this.toast.toastServices(res.message, 'danger', 'top')
        }
        this.ionLoaderService.dismissLoader();

      }).catch((err) => {
        this.toast.toastServices(err, 'danger', 'top')
        this.ionLoaderService.dismissLoader();
      });
    });
  }
  async filterList() {
    console.log(this.selectedBuyer, this.from_date, this.to_date);
    let requiredBuyerList = this.selectedBuyer?.map((e) => e.id) || [];
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api.getSalesPayment({ buyer: requiredBuyerList, from_date: this.from_date, to_date: this.to_date }).then(async (res: any) => {
        if (res.success) {
          this.salesPaymentData = res.data
          this.getTotal()
          this.toast.toastServices(res.message, 'success', 'top')
        }
        else {
          this.toast.toastServices(res.message, 'danger', 'top')
        }
        this.ionLoaderService.dismissLoader();
      }).catch((err) => {
        this.toast.toastServices(err, 'danger', 'top')
        this.ionLoaderService.dismissLoader();
      })
    })

  }
  async getTotal() {

    this.bill_amount_total = 0;

    this.received_amount_total = 0;
    this.current_balance_total = 0;
    this.salesPaymentData.forEach((element) => {

      this.bill_amount_total += element.bill_amount;

      this.received_amount_total += element.received_amount;

    });
    this.current_balance_total = this.salesPaymentData[this.salesPaymentData.length - 1].current_balance

  }
  printStatement() {
    const items = this.salesPaymentData.map((d, index) => {
      return `
        <tr>
          <td class="number-cell">${d.id}</td>
          <td class="name-cell">${d.date}</td>
          <td class="name-cell">${d.name}</td>
          <td class="amount-cell">₹${d.previous_balance.toFixed(2)}</td>
          <td class="amount-cell">₹${d.bill_amount.toFixed(2)}</td>
          <td class="amount-cell">₹${d.received_amount.toFixed(2)}</td>
          <td class="amount-cell">₹${d.current_balance.toFixed(2)}</td>
        </tr>
      `;
    }).join("");

    const dateRange = `${this.from_date} to ${this.to_date}`;

    const tableContent = `
      <table>
        <thead>
          <tr>
            <th>Bill ID</th>
            <th>Bill Date</th>
            <th>Buyer</th>
            <th>Previous Balance</th>
            <th>Bill Amount</th>
            <th>Paid Amount</th>
            <th>Current Balance</th>
          </tr>
        </thead>
        <tbody>
          ${items}
          <tr class="total-row">
            <td><strong>Total</strong></td>
            <td></td>
            <td></td>
            <td></td>
            <td><strong>₹${this.bill_amount_total.toFixed(2)}</strong></td>
            <td><strong>₹${this.received_amount_total.toFixed(2)}</strong></td>
            <td><strong>₹${this.current_balance_total.toFixed(2)}</strong></td>
          </tr>
        </tbody>
      </table>
    `;

    const htmlContent = this.printService.generateEnhancedReportHTML(
      'Sales Payment Statement',
      dateRange,
      tableContent,
      `Total Bill Amount: ₹${this.bill_amount_total.toFixed(2)} | Total Received: ₹${this.received_amount_total.toFixed(2)} | Outstanding: ₹${this.current_balance_total.toFixed(2)}`
    );

    this.printService.printEnhancedReport(htmlContent, `Sales Statement - ${dateRange}`);
  }
}
