/* Login Page Styling with Theme Support */
.login-content {
  --background: var(--app-background);
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 16px;
  --padding-bottom: 16px;
}

.login-container {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  min-height: 100vh;
  padding: 20px 0;
}

/* Login Image Container */
.login-image-container {
  margin-bottom: 32px;
  text-align: center;
}

.login-image {
  width: 280px;
  height: auto;
  max-width: 100%;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Login Card */
.login-card {
  --background: var(--card-background);
  min-width: 320px;
  max-width: 400px;
  width: 100%;
  margin: 0 auto;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  padding: 0;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* <PERSON>gin Header */
.login-header {
  text-align: center;
  padding: 32px 24px 16px 24px;
  background: linear-gradient(135deg, var(--ion-color-primary) 0%, var(--ion-color-primary-shade) 100%);
  color: white;
}

.login-title {
  font-weight: 700;
  font-size: 28px;
  margin: 0 0 8px 0;
  color: white;
}

.login-subtitle {
  font-size: 14px;
  font-weight: 500;
  opacity: 0.9;
  margin: 0;
  color: white;
}

/* Form Content */
.login-form-content {
  padding: 32px 24px 16px 24px;
  background: var(--form-background);
}

/* Error Message */
.error-message {
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.3);
  border-radius: 12px;
  padding: 12px 16px;
  margin-bottom: 24px;
  text-align: center;
}

.error-message h5 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

/* Input Groups */
.input-group {
  margin-bottom: 24px;
}

.login-input-item {
  --background: var(--item-background);
  --border-radius: 12px;
  --padding-start: 16px;
  --padding-end: 16px;
  --min-height: 56px;
  border: 2px solid var(--ion-color-light);
  border-radius: 12px;
  transition: all 0.3s ease;
  margin: 0;
}

.login-input-item:focus-within {
  border-color: var(--ion-color-primary);
  box-shadow: 0 0 0 3px rgba(var(--ion-color-primary-rgb), 0.1);
}

.input-icon {
  color: var(--ion-color-medium);
  font-size: 20px;
  margin-right: 12px;
  transition: color 0.3s ease;
}

.login-input-item:focus-within .input-icon {
  color: var(--ion-color-primary);
}

.login-input {
  --color: var(--ion-text-color);
  --placeholder-color: var(--ion-color-medium);
  font-size: 16px;
  font-weight: 500;
}

.password-toggle-icon {
  color: var(--ion-color-medium);
  font-size: 20px;
  cursor: pointer;
  transition: color 0.3s ease;
  margin-left: 8px;
}

.password-toggle-icon:hover {
  color: var(--ion-color-primary);
}

/* Validation Error */
.validation-error {
  color: var(--ion-color-danger);
  font-size: 12px;
  font-weight: 500;
  margin-top: 8px;
  margin-left: 16px;
}

/* Login Button Container */
.login-button-container {
  padding: 16px 24px 32px 24px;
  background: var(--form-background);
}

.login-button {
  --background: var(--ion-color-primary);
  --background-hover: var(--ion-color-primary-shade);
  --background-activated: var(--ion-color-primary-shade);
  --border-radius: 12px;
  --box-shadow: 0 4px 12px rgba(var(--ion-color-primary-rgb), 0.3);
  height: 56px;
  font-weight: 600;
  font-size: 16px;
  text-transform: none;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.login-button:hover {
  transform: translateY(-2px);
  --box-shadow: 0 6px 20px rgba(var(--ion-color-primary-rgb), 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
  .login-content {
    --padding-start: 8px;
    --padding-end: 8px;
  }

  .login-container {
    padding: 16px 0;
  }

  .login-image {
    width: 240px;
  }

  .login-card {
    min-width: 280px;
    max-width: 350px;
    margin: 0 8px;
  }

  .login-header {
    padding: 24px 20px 12px 20px;
  }

  .login-title {
    font-size: 24px;
  }

  .login-form-content,
  .login-button-container {
    padding-left: 20px;
    padding-right: 20px;
  }

  .login-input-item {
    --min-height: 52px;
  }

  .login-button {
    height: 52px;
    font-size: 15px;
  }
}


