<ion-content class="login-content">
  <div class="login-container">
    <div class="login-image-container">
      <img src="/assets/images/Login-rafiki.png" alt="Login Illustration" class="login-image" />
    </div>
    <ion-card class="login-card">
      <form [formGroup]="myForm" (ngSubmit)="onSubmit(myForm)" (keyup.enter)="onSubmit(myForm)">
        <ion-card-header class="login-header">
          <ion-card-title class="login-title">Login</ion-card-title>
          <ion-card-subtitle class="login-subtitle">Kingwizard Techsolutions</ion-card-subtitle>
        </ion-card-header>
        <ion-card-content class="login-form-content">
          <div *ngIf="loginError" class="error-message">
            <ion-text color="danger">
              <h5>Please check your username / password.</h5>
            </ion-text>
          </div>

          <div class="input-group">
            <ion-item class="login-input-item">
              <ion-icon name="call" class="input-icon"></ion-icon>
              <ion-input
                formControlName="username"
                type="text"
                placeholder="Enter Username"
                class="login-input">
              </ion-input>
            </ion-item>
            <div class="validation-error"
              *ngIf="myForm.get('username').invalid && (myForm.get('username').dirty || myForm.get('username').touched)">
              Username is required
            </div>
          </div>

          <div class="input-group">
            <ion-item class="login-input-item">
              <ion-icon name="lock-closed" class="input-icon"></ion-icon>
              <ion-input
                formControlName="password"
                placeholder="Enter Password"
                [type]="passwordType"
                clearOnEdit="false"
                class="login-input">
              </ion-input>
              <ion-icon
                [name]="passwordIcon"
                class="password-toggle-icon"
                (click)="hideShowPassword()">
              </ion-icon>
            </ion-item>
            <div class="validation-error"
              *ngIf="myForm.get('password').invalid && (myForm.get('password').dirty || myForm.get('password').touched)">
              Password is required
            </div>
          </div>
        </ion-card-content>
        <div class="login-button-container">
          <ion-button type="submit" class="login-button" expand="block">
            <ion-icon name="log-in" slot="start"></ion-icon>
            Login
          </ion-button>
        </div>
      </form>
    </ion-card>
  </div>
</ion-content>