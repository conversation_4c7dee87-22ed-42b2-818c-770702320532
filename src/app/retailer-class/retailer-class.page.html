<app-header [title]="'Retailer Class Management'" [returnUrl]="'tabs/home'"></app-header>

<ion-content class="retailer-class-content">
  <!-- Summary Section -->
  <div class="summary-section" *ngIf="data && data.length > 0">
    <h3 class="section-title">
      <ion-icon name="analytics-outline" class="section-icon"></ion-icon>
      Class Summary
    </h3>

    <ion-row>
      <ion-col size="6">
        <ion-card class="summary-card total-card">
          <ion-card-content>
            <div class="summary-content">
              <div class="summary-info">
                <h3>Total Classes</h3>
                <h1>{{data.length}}</h1>
                <p>Retailer categories</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="business" class="summary-icon total"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6">
        <ion-card class="summary-card active-card">
          <ion-card-content>
            <div class="summary-content">
              <div class="summary-info">
                <h3>Active Classes</h3>
                <h1>{{data.length}}</h1>
                <p>Currently in use</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="checkmark-circle" class="summary-icon active"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
  </div>

  <!-- Retailer Classes List -->
  <div class="classes-section" *ngIf="data && data.length > 0">
    <h3 class="section-title">
      <ion-icon name="list-outline" class="section-icon"></ion-icon>
      Retailer Classes
    </h3>

    <div class="classes-list">
      <ion-card
        *ngFor="let item of data"
        class="class-card"
        (click)="viewClassMargins(item.id)">
        <ion-card-content>
          <div class="class-header">
            <div class="class-info">
              <div class="class-icon-wrapper">
                <ion-icon name="business-outline" class="class-icon"></ion-icon>
              </div>
              <div class="class-details">
                <h3 class="class-name">{{item.name}}</h3>
                <p class="class-type">Retailer Classification</p>
              </div>
            </div>
            <div class="class-actions" (click)="$event.stopPropagation()">
              <ion-button
                (click)="viewClassMargins(item.id)"
                fill="clear"
                size="small"
                class="action-button view-button">
                <ion-icon name="eye-outline" slot="icon-only"></ion-icon>
              </ion-button>
              <ion-button
                (click)="setEditOpen(true, item)"
                fill="clear"
                size="small"
                class="action-button edit-button">
                <ion-icon name="create-outline" slot="icon-only"></ion-icon>
              </ion-button>
              <ion-button
                (click)="delete(item.id)"
                fill="clear"
                size="small"
                class="action-button delete-button">
                <ion-icon name="trash-outline" slot="icon-only"></ion-icon>
              </ion-button>
            </div>
          </div>

          <div class="class-meta">
            <div class="meta-item">
              <ion-icon name="settings-outline" class="meta-icon"></ion-icon>
              <span class="meta-text">Manage Margins</span>
            </div>
          </div>
        </ion-card-content>
      </ion-card>
    </div>
  </div>

  <!-- No Data Message -->
  <div class="no-data-section" *ngIf="!data || data.length === 0">
    <ion-card class="no-data-card">
      <ion-card-content>
        <div class="no-data-content">
          <ion-icon name="business-outline" class="no-data-icon"></ion-icon>
          <h3>No Retailer Classes Found</h3>
          <p>Start by creating your first retailer class to organize your customers.</p>
          <ion-button
            (click)="setOpen(true)"
            fill="solid"
            class="add-first-button">
            <ion-icon name="add-circle-outline" slot="start"></ion-icon>
            Add First Class
          </ion-button>
        </div>
      </ion-card-content>
    </ion-card>
  </div>
  <ion-modal [isOpen]="isModalOpen">
    <ng-template>
      <ion-header translucent>
        <ion-toolbar>
          <ion-title>Add Product</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="setOpen(false)">Close</ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <ion-content class="ion-padding">
        <form #form="ngForm" (ngSubmit)="addBuyerClass(form.value)">
          <ion-item>
            <ion-label position="stacked">Name
              <ion-text color="danger">*</ion-text>
            </ion-label>
            <ion-input ngModel type="text" name="name" required="true"></ion-input>
          </ion-item>
          <ion-row>
            <ion-col size="12" class="ion-margin-vertical">
              <ion-button type="submit" [disabled]="form.invalid" expand="block" fill="solid" shape="round">
                Add
              </ion-button>
            </ion-col>
          </ion-row>
        </form>
      </ion-content>
    </ng-template>
  </ion-modal>
  <ion-modal [isOpen]="isEditModalOpen">
    <ng-template>
      <ion-header translucent>
        <ion-toolbar>
          <ion-title>Edit Product</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="setEditOpen(false, null)">Close</ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <ion-content class="ion-padding" *ngIf="selectedData">
        <form #form="ngForm" (ngSubmit)="editBuyerClass(form.value)">
          <input type="hidden" name="id" [(ngModel)]="selectedData.id">
          <ion-item>
            <ion-label position="stacked">Name
              <ion-text color="danger">*</ion-text>
            </ion-label>
            <ion-input [(ngModel)]="selectedData.name" type="text" name="name" required="true"></ion-input>
          </ion-item>
          <ion-row>
            <ion-col size="12" class="ion-margin-vertical">
              <ion-button type="submit" [disabled]="form.invalid" expand="block" fill="solid" shape="round">
                Add
              </ion-button>
            </ion-col>
          </ion-row>
        </form>
      </ion-content>
    </ng-template>
  </ion-modal>
</ion-content>
<ion-footer>
  <ion-fab vertical="bottom" horizontal="start">
    <ion-fab-button (click)="setOpen(true)" class="add-fab">
      <ion-icon name="add"></ion-icon>
    </ion-fab-button>
  </ion-fab>
  <app-floating-menu></app-floating-menu>
</ion-footer>
