/* Retailer Class Page Styling */
.retailer-class-content {
  --background: var(--ion-background-color);
  --padding-start: 0;
  --padding-end: 0;
  --padding-top: 16px;
  --padding-bottom: 16px;
}

/* Section Styling */
.summary-section,
.classes-section,
.no-data-section {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.section-icon {
  font-size: 24px;
  margin-right: 8px;
  color: var(--ion-color-primary);
}

/* Summary Cards */
.summary-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.summary-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-info h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
}

.summary-info h1 {
  margin: 0 0 4px 0;
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
}

.summary-info p {
  margin: 0;
  font-size: 12px;
  color: #6c757d;
}

.summary-icon {
  font-size: 48px;
  opacity: 0.8;
}

.summary-icon.total {
  color: #6f42c1;
}

.summary-icon.active {
  color: #28a745;
}

.total-card {
  background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
}

.active-card {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
}

/* Classes List */
.classes-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.class-card {
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  --background: var(--ion-card-background);
  transition: all 0.3s ease;
  cursor: pointer;
}

.class-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.class-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.class-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.class-icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.class-icon {
  font-size: 24px;
  color: var(--ion-color-primary);
}

.class-details h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.class-details p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
}

.class-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  --border-radius: 8px;
  width: 36px;
  height: 36px;
}

.view-button {
  --color: var(--ion-color-primary);
  --background: #e3f2fd;
}

.edit-button {
  --color: #ff9800;
  --background: #fff3e0;
}

.delete-button {
  --color: #f44336;
  --background: #ffebee;
}

.class-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.meta-icon {
  font-size: 14px;
  color: #6c757d;
}

.meta-text {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

/* No Data Section */
.no-data-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --background: var(--ion-card-background);
}

.no-data-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.no-data-icon {
  font-size: 64px;
  color: #dee2e6;
  margin-bottom: 16px;
}

.no-data-content h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #6c757d;
}

.no-data-content p {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #adb5bd;
}

.add-first-button {
  --border-radius: 12px;
  --background: var(--ion-color-success);
}

/* FAB Styling */
.add-fab {
  --background: var(--ion-color-success);
  --color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Responsive Design */
@media (max-width: 576px) {
  .retailer-class-content {
    --padding-start: 12px;
    --padding-end: 12px;
  }

  .section-title {
    font-size: 18px;
  }

  .section-icon {
    font-size: 20px;
  }

  .summary-content {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .summary-info h3 {
    font-size: 12px;
  }

  .summary-info h1 {
    font-size: 24px;
  }

  .summary-info p {
    font-size: 10px;
  }

  .summary-icon {
    font-size: 32px;
  }

  .class-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .class-actions {
    align-self: flex-end;
  }

  .class-icon-wrapper {
    width: 40px;
    height: 40px;
  }

  .class-icon {
    font-size: 20px;
  }

  .class-details h3 {
    font-size: 16px;
  }

  .class-details p {
    font-size: 12px;
  }

  .no-data-icon {
    font-size: 48px;
  }

  .no-data-content h3 {
    font-size: 16px;
  }

  .no-data-content p {
    font-size: 12px;
  }
}

@media (min-width: 768px) {
  .retailer-class-content {
    --padding-start: 24px;
    --padding-end: 24px;
  }

  .section-title {
    font-size: 22px;
  }

  .section-icon {
    font-size: 26px;
  }

  .summary-info h1 {
    font-size: 36px;
  }

  .summary-icon {
    font-size: 56px;
  }

  .classes-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .class-icon-wrapper {
    width: 56px;
    height: 56px;
  }

  .class-icon {
    font-size: 28px;
  }

  .class-details h3 {
    font-size: 20px;
  }

  .class-details p {
    font-size: 16px;
  }
}

@media (min-width: 1024px) {
  .retailer-class-content {
    --padding-start: 32px;
    --padding-end: 32px;
  }

  .section-title {
    font-size: 24px;
  }

  .section-icon {
    font-size: 28px;
  }

  .summary-info h1 {
    font-size: 40px;
  }

  .summary-icon {
    font-size: 64px;
  }

  .classes-list {
    grid-template-columns: repeat(3, 1fr);
  }

  .class-icon-wrapper {
    width: 64px;
    height: 64px;
  }

  .class-icon {
    font-size: 32px;
  }

  .class-details h3 {
    font-size: 22px;
  }

  .class-details p {
    font-size: 18px;
  }
}

.class-title {
  color: var(--ion-text-color);
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 4px;
}

.class-subtitle {
  color: var(--ion-text-color-step-600);
  font-size: 14px;
  margin-bottom: 4px;
}

.status-active {
  color: var(--ion-color-success);
}

.status-inactive {
  color: var(--ion-color-danger);
}