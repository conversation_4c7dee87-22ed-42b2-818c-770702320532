import { Component, OnInit } from "@angular/core";
import { Platform } from "@ionic/angular";
import { AlertService } from "../shared/services/alert.service";
import { IonLoaderService } from "../shared/services/ion-loader.service";
import { LineAccountsService } from "../shared/services/line-accounts.service";
import { ToastService } from "../shared/services/toast.service";
import * as moment from "moment";

@Component({
  selector: "app-line-accounts",
  templateUrl: "./line-accounts.page.html",
  styleUrls: ["./line-accounts.page.scss"],
})
export class LineAccountsPage implements OnInit {
  suplier_data: any;
  isModalOpen = false;
  isEditModalOpen = false;
  supplierData: any;
  date: any;
  // date = moment().format("YYYY-MM-DD");
  lineAccountData: any;
  editData: any;
  selectedSupplier: any;
  bill_amount_total: any;
  receivable_amount_total: any;
  received_amount_total: any;

  constructor(
    private api: LineAccountsService,
    private toast: ToastService,
    public ionLoaderService: IonLoaderService,
    public platform: Platform,
    public alertService: AlertService
  ) {
    this.date = moment().format("YYYY-MM-DD");
  }

  ngOnInit() {}
  ionViewWillEnter() {
    this.getData(this.date);
  }
  filterData() {
    this.getData(this.date);
  }

  async getData(date) {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .getPurchaseInvoice(date)
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, "success", "top");
            this.lineAccountData = res.data;
            this.getTotal();
          } else {
            this.toast.toastServices(res.message, "danger", "top");
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err.error.message, "danger", "top");
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }
  async getTotal() {

    this.bill_amount_total=0;
    this.receivable_amount_total=0;
    this.received_amount_total=0;
    this.lineAccountData.forEach((element) => {
      this.bill_amount_total += element.bill_amount;
      this.receivable_amount_total += element.receivable_amount;
      this.received_amount_total += element.received_amount;
    });
    
  }

  async getSupplierData() {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .getLineAccountSupplierData()
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, "success", "top");
            this.suplier_data = res.data;
          } else {
            this.toast.toastServices(res.message, "danger", "top");
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err.error.message, "danger", "top");
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }
  setOpen(isOpen: boolean) {
    this.isModalOpen = isOpen;
    if (isOpen) {
      this.getSupplierData();
    }
  }
  edit(data) {
    this.editData = data;
    this.setEditOpen(true);
  }
  setEditOpen(isOpen: boolean) {
    this.isEditModalOpen = isOpen;
  }
  setSupplier(data) {
    console.log(data);
    this.supplierData = data;
    this.getInvoiceForSelectedSupplier(data.id);
  }
  async getInvoiceForSelectedSupplier(id) {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .getInvoiceForSelectedSupplier(id)
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, "success", "top");
          } else {
            this.toast.toastServices(res.message, "danger", "top");
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err.error.message, "danger", "top");
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }
  async addLineAccount(obj) {
    console.log(obj, this.date);
    let data = {
      bill_date: moment(this.date).format("YYYY-MM-DD"),
      suplier_id: obj.agent.id,
      bill_amount: obj.bill_amount,
      no_3_amount: obj.receivable_amount,
      paid_amount: obj.received_amount,
    };

    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .savePurchaseInvoice(data)
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, "success", "top");
            this.setOpen(false);
            this.ionViewWillEnter();
          } else {
            this.toast.toastServices(res.message, "danger", "top");
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err.error.message, "danger", "top");
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }
  async editLineAccount(obj) {
    console.log(obj);
    let data = {
      id: obj.purchase_invoice_id,
      bill_amount: obj.bill_amount,
      no_3_amount: obj.receivable_amount,
      paid_amount: obj.received_amount,
      
    };

    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .editPurchaseInvoice(data)
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, "success", "top");
            this.setEditOpen(false);
            this.ionViewWillEnter();
          } else {
            this.toast.toastServices(res.message, "danger", "top");
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err.error.message, "danger", "top");
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }
  delete(data) {
    this.alertService
      .alertConfirm(
        "Alert",
        "Are you sure you want to delete the Purchase Invoice !!!",
        "yes",
        "no"
      )
      .then((res) => {
        if (res) {
          this.deletePurchaseInvoice(data);
        }
      });
  }
  async deletePurchaseInvoice(id) {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .deletePurchaseInvoice(id)
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, "success", "top");
            this.getData(this.date);
          } else {
            this.toast.toastServices(res.message, "danger", "top");
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err, "danger", "top");
          this.ionLoaderService.dismissLoader();
        });
    });
  }
}
