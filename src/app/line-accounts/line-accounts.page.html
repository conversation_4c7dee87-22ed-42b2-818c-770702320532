<app-header [title]="'Line Accounts'" [returnUrl]="'tabs/home'"></app-header>
  
<ion-content>
  <!-- Top -->
  <ion-grid>
    <ion-row>
      <ion-col size="12">
        <ion-card class="ion-padding">
          <ion-item class="ion-margin-bottom ion-no-padding">
            <ion-label position="stacked"> Select Date </ion-label>
            <ion-input
              [(ngModel)]="date"
              [value]="date | date : 'YYYY-MM-dd' "
              placeholder="Enter Date"
              type="date"
            >
            </ion-input>
          </ion-item>
          <ion-button color="primary" (click)="filterData()" >
            Filter 
            <ion-icon name="filter" class="ion-padding-start"></ion-icon>
          </ion-button>
        </ion-card>
      </ion-col>
    </ion-row>
  </ion-grid>
  <!-- Start : Main  -->
  <ion-grid class="ion-text-center" style="margin-bottom: 50px">
    <ion-grid class="selection">
      <ion-row>
        <ion-col size="12">
          <div style="max-width: 100%; overflow-y: scroll">
            <ion-grid
              class="ion-no-padding main-content"
              style="min-width: 900px"
            >
              <ion-row class="header-row">
                <ion-col size="1">
                  <ion-text>Id</ion-text>
                </ion-col>
                <ion-col size="2">
                  <ion-text>Name</ion-text>
                </ion-col>
                <ion-col size="1">
                  <ion-text>Bill_Amount</ion-text>
                </ion-col>
                <ion-col size="1"> 
                  <ion-text>Reduced Amount</ion-text>
                </ion-col>
                <ion-col size="1"> 
                  <ion-text>Paid Amount</ion-text>
                </ion-col>
                <ion-col size="1.5">
                  <ion-text>Edit</ion-text>
                </ion-col>
                <ion-col size="1.5">
                  <ion-text>Delete</ion-text>
                </ion-col>
                 
              </ion-row>
              <ion-row *ngFor="let d of lineAccountData">
                <ion-col size="1">
                  <ion-text>{{d.id}}</ion-text>
                </ion-col>
                <ion-col size="2">
                  <ion-text>{{d.name}}</ion-text>
                </ion-col>
                <ion-col size="1">
                  <ion-text>{{d.bill_amount | currency: 'INR'}}</ion-text>
                </ion-col>
                <ion-col size="1">
                  <ion-text> {{d.receivable_amount | currency: 'INR'}} </ion-text>
                </ion-col>
                <ion-col size="1">
                  <ion-text> {{d.received_amount | currency: 'INR'}} </ion-text>
                </ion-col>
                <ion-col size="1.5" class="button-font">
                  <ion-buttons>
                    <ion-button   color="primary" (click)="edit(d)" fill="outline">
                      <span class="visible-on-mobile-only">
                        <ion-icon name="create" class="icon-style"></ion-icon>
                      </span>
                      <span class="hidden-on-mobile"> Edit </span>
                    </ion-button>  
                  </ion-buttons>
                </ion-col>
                <ion-col size="1.5" class="button-font">
                  <ion-buttons>   
                    <ion-button   color="danger" (click)="delete(d.id)" fill="outline">
                      <span class="visible-on-mobile-only">
                        <ion-icon name="trash" class="icon-style"></ion-icon>
                      </span>
                      <span class="hidden-on-mobile"> Delete </span>
                    </ion-button>
                  </ion-buttons>  
                </ion-col>
              </ion-row>
              <ion-row class="header-row total">
                <ion-col size="3"> <ion-text>Total</ion-text> </ion-col>
                <ion-col size="1">
                  <ion-text>{{bill_amount_total | currency: 'INR'}}</ion-text>
                </ion-col>
                <ion-col size="1">
                  <ion-text>{{receivable_amount_total | currency: 'INR'}}</ion-text>
                </ion-col>
                <ion-col size="1">
                  <ion-text>{{received_amount_total | currency: 'INR'}}</ion-text>
                </ion-col>
                <ion-col size="3"> </ion-col>
                 
              </ion-row>
            </ion-grid>
          </div>
        </ion-col>
         
      </ion-row>
    </ion-grid>
  </ion-grid>  
  <!-- Ends : Main  -->
  
  <ion-modal [isOpen]="isModalOpen">
    <ng-template>
      <ion-header>
        <ion-toolbar>
          <ion-title>Add Suppliers</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="setOpen(false)">Close</ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <ion-content class="ion-padding">
        <form #form="ngForm" (ngSubmit)="addLineAccount(form.value)">
          <ion-item class="ion-margin-bottom ion-no-padding">
            <ion-label position="stacked"> Select Date </ion-label>
            <ion-input
              [(ngModel)]="date"
              [value]="date | date : 'YYYY-MM-dd' "
              placeholder="Enter Date"
              type="date"
            >
            </ion-input>
          </ion-item>
          <ion-item>
            <!-- <ion-label position="floating">Name</ion-label> -->
              <ionic-selectable name="agent" [shouldFocusSearchbar]="true" [(ngModel)]="selectedSupplier"
              placeholder="Select an Supplier" [items]="suplier_data" itemValueField="id" itemTextField="name"
              [canSearch]="true" (ngModelChange)="setSupplier($event)">
            </ionic-selectable>
          </ion-item>
 
          <ion-item>
            <ion-label position="floating">Bill Amount</ion-label>
            <ion-input ngModel type="number" name="bill_amount" required="true"></ion-input>
          </ion-item>
          <ion-item>
            <ion-label position="floating">Reduced Amount</ion-label>
            <ion-input ngModel type="number" name="receivable_amount" required="true"></ion-input>
          </ion-item>
          <ion-item>
            <ion-label position="stacked">Paid Amount
              <ion-text color="danger">*</ion-text>
            </ion-label>
            <ion-input ngModel type="number" name="received_amount" required="true"></ion-input>
          </ion-item>
          <ion-row>
            <ion-col size="12" class="ion-margin-vertical">
              <ion-button type="submit" [disabled]="form.invalid" expand="block" fill="solid" shape="round">
                Add
              </ion-button>
            </ion-col>
          </ion-row>
        </form>
      </ion-content>
    </ng-template>
  </ion-modal>
  <ion-modal [isOpen]="isEditModalOpen">
    <ng-template>
      <ion-header translucent>
        <ion-toolbar>
          <ion-title>Edit Suppliers</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="setEditOpen(false)">Close</ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <ion-content class="ion-padding">
        <form #editForm="ngForm" (ngSubmit)="editLineAccount(editForm.value)">
          <input type="hidden" [(ngModel)]="editData.id" name="purchase_invoice_id">
          <ion-item>
            <ion-label position="floating">Bill Amount</ion-label>
            <ion-input [(ngModel)]="editData.bill_amount" type="number" name="bill_amount" required="true"></ion-input>
          </ion-item>
          <ion-item>
            <ion-label position="floating">Reduced Amount</ion-label>
            <ion-input [(ngModel)]="editData.no_3_amount" type="number" name="receivable_amount" required="true">
            </ion-input>
          </ion-item>
          <ion-item>
            <ion-label position="floating">Paid Amount</ion-label>
            <ion-input [(ngModel)]="editData.paid_amount" type="number" name="received_amount" required="true">
            </ion-input>
          </ion-item>
          <ion-row>
            <ion-col size="12" class="ion-margin-vertical">
              <ion-button type="submit" [disabled]="editForm.invalid" expand="block" fill="solid" shape="round">
                Edit
              </ion-button>
            </ion-col>
          </ion-row>
        </form>
      </ion-content>
    </ng-template>
  </ion-modal>

</ion-content> 


<ion-footer>
  <ion-fab vertical="bottom" horizontal="start">
    <ion-fab-button (click)="setOpen(true)">
      <ion-icon name="add"></ion-icon>
    </ion-fab-button>
  </ion-fab>
  <app-floating-menu></app-floating-menu>
</ion-footer>