import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { LineAccountsPageRoutingModule } from './line-accounts-routing.module';

import { LineAccountsPage } from './line-accounts.page';
import { SharedModule } from '../shared/modules/shared/shared.module';
import { IonicSelectableModule } from 'ionic-selectable';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    LineAccountsPageRoutingModule,
    SharedModule,
    IonicSelectableModule,
  ],
  declarations: [LineAccountsPage]
})
export class LineAccountsPageModule { }
