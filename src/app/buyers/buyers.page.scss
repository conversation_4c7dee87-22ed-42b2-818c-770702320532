/* Buyers Page Styling */
.buyers-content {
  --background: var(--ion-background-color);
  --padding-start: 0;
  --padding-end: 0;
}

/* Collapsible Summary Section - Now at Top */
.summary-section {
  background: var(--section-background);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin: 0 16px 16px 16px;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: linear-gradient(135deg, var(--ion-card-background) 0%, var(--ion-background-color) 100%);
  border-bottom: 1px solid var(--ion-border-color);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.summary-header:hover {
  background: #f8f9fa;
}

.toggle-button {
  --color: var(--ion-color-primary);
  --background: transparent;
  --padding-start: 8px;
  --padding-end: 8px;
  margin: 0;
}

.toggle-icon {
  font-size: 20px;
  transition: transform 0.3s ease;
}

.summary-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  padding: 0 16px;
}

.summary-content.expanded {
  max-height: 400px;
  padding: 16px;
}

/* Collapsible Search Section */
.search-section {
  margin: 0 16px 16px 16px;
  background: var(--section-background);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: linear-gradient(135deg, var(--ion-card-background) 0%, var(--ion-background-color) 100%);
  border-bottom: 1px solid var(--ion-border-color);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.search-header:hover {
  background: #f8f9fa;
}

.section-title {
  display: flex;
  align-items: center;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.section-icon {
  font-size: 20px;
  margin-right: 8px;
  color: var(--ion-color-primary);
}

.toggle-button {
  --color: var(--ion-color-primary);
  --background: transparent;
  --padding-start: 8px;
  --padding-end: 8px;
  margin: 0;
}

.toggle-icon {
  font-size: 20px;
  transition: transform 0.3s ease;
}

.search-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  padding: 0 16px;
}

.search-content.expanded {
  max-height: 200px;
  padding: 16px;
}

.custom-searchbar {
  --background: #f5f5f5;
  --border-radius: 12px;
  --box-shadow: none;
  --placeholder-color: #666;
  --color: #333;
}

/* Filter Components */
.filter-item {
  --background: transparent;
  --border-radius: 8px;
  --padding-start: 0;
  --padding-end: 0;
  margin-bottom: 8px;
}

.filter-item ion-label {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.route-filter {
  --background: #f5f5f5;
  --border-radius: 8px;
  --padding-start: 12px;
  --padding-end: 12px;
  margin-top: 4px;
  min-height: 44px;
}

.filter-button {
  --border-radius: 8px;
  --border-color: #ddd;
  --color: #666;
  --background: var(--item-background);
  height: 44px;
  font-size: 14px;
  font-weight: 500;
  margin: 0;
  transition: all 0.3s ease;
}

.filter-button:hover {
  --border-color: var(--ion-color-primary);
  --color: var(--ion-color-primary);
  --background: #f0f8ff;
}

/* No Data Item */
.no-data-item {
  --background: var(--item-background);
  --padding-start: 16px;
  --padding-end: 16px;
  margin: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Buyers List */
.buyers-list {
  padding: 8px 16px;
  margin-bottom: 80px; /* Space for floating menu */
}

/* Buyer Item */
.buyer-item {
  --background: var(--item-background);
  --border-radius: 12px;
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 12px;
  --padding-bottom: 12px;
  --min-height: 120px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.buyer-item:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* Buyer Icon */
.buyer-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;
}

.person-icon {
  font-size: 28px;
  color: white;
}

/* Buyer Details */
.buyer-details {
  flex: 1;
  margin: 0;
}

.buyer-name {
  color: var(--ion-text-color);
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 4px;
}

.buyer-phone {
  color: var(--ion-text-color-step-600);
  font-size: 14px;
  margin-bottom: 4px;
}

.buyer-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.location-info,
.contact-info {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #666;
}

.meta-icon {
  font-size: 14px;
  color: #999;
}

.location-text,
.contact-text {
  font-weight: 500;
}

.buyer-details-row {
  display: flex;
  gap: 16px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.buyer-detail {
  font-size: 12px;
  color: #666;
  background: #f0f0f0;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
}

/* Balance Details */
.balance-details {
  margin-bottom: 8px;
}

.balance-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.balance-label {
  color: #666;
  font-weight: 500;
}

.balance-value {
  font-weight: 700;
  font-size: 15px;
}

.balance-positive {
  color: #4caf50;
}

.balance-zero {
  color: #666;
}

.balance-negative {
  color: #f44336;
}

/* Status Row */
.status-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 4px;
  flex-wrap: wrap;
}

.status-badge {
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Profile Completion Styles */
.profile-completion {
  display: flex;
  align-items: center;
}

.completion-badge {
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  display: flex;
  align-items: center;
  gap: 4px;

  ion-icon {
    font-size: 12px;
  }
}

.missing-modules {
  margin-top: 4px;
  width: 100%;
}

.missing-text {
  font-size: 11px;
  color: #f44336;
  font-style: italic;
  font-weight: 500;
}

/* Buyer Actions */
.buyer-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  min-width: 60px;
}

.action-button {
  --border-radius: 8px;
  --padding-start: 8px;
  --padding-end: 8px;
  width: 40px;
  height: 40px;
}

.view-button {
  --color: #2196f3;
  --background: #e3f2fd;
}

.edit-button {
  --color: #ff9800;
  --background: #fff3e0;
}

.delete-button {
  --color: #f44336;
  --background: #ffebee;
}

.status-toggle {
  margin-top: 8px;
  --handle-width: 20px;
  --handle-height: 20px;
  --track-width: 40px;
  --track-height: 24px;
}

/* Summary Section */
.summary-section {
  padding: 16px;
  // margin-bottom: 80px; /* Space for floating menu */
}

.summary-card {
  --background: var(--card-background);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin: 0;
}

.summary-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-icon {
  font-size: 20px;
  color: #667eea;
}

/* Section Title */
.section-title {
  display: flex;
  align-items: center;
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.section-icon {
  font-size: 24px;
  margin-right: 8px;
  color: var(--ion-color-primary);
}

/* Summary Cards */
.summary-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.summary-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-info h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
}

.summary-info h1 {
  margin: 0 0 4px 0;
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
}

.summary-info p {
  margin: 0;
  font-size: 12px;
  color: #6c757d;
}

.summary-icon {
  font-size: 48px;
  opacity: 0.8;
}

.summary-icon.total {
  color: #6f42c1;
}

.summary-icon.active {
  color: #28a745;
}

.summary-icon.outstanding {
  color: #dc3545;
}

.summary-icon.average {
  color: #007bff;
}

.total-card {
  background: linear-gradient(135deg, #f3e5f5 0%, #faf2fb 100%);
}

.active-card {
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 100%);
}

.outstanding-card {
  background: linear-gradient(135deg, #ffebee 0%, #fef5f5 100%);
}

.average-card {
  background: linear-gradient(135deg, #e3f2fd 0%, #f1f8ff 100%);
}

/* Responsive Design */
@media (max-width: 576px) {
  .section-title {
    font-size: 18px;
  }

  .section-icon {
    font-size: 20px;
  }

  .summary-content {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .summary-info h3 {
    font-size: 12px;
  }

  .summary-info h1 {
    font-size: 24px;
  }

  .summary-info p {
    font-size: 10px;
  }

  .summary-icon {
    font-size: 32px;
  }
}

@media (min-width: 768px) {
  .section-title {
    font-size: 22px;
  }

  .section-icon {
    font-size: 26px;
  }

  .summary-info h1 {
    font-size: 36px;
  }

  .summary-icon {
    font-size: 56px;
  }
}

@media (min-width: 1024px) {
  .section-title {
    font-size: 24px;
  }

  .section-icon {
    font-size: 28px;
  }

  .summary-info h1 {
    font-size: 40px;
  }

  .summary-icon {
    font-size: 64px;
  }
}

/* Modal Styling */
.buyer-modal,
.asset-modal,
.deposit-modal,
.buyer-details-modal,
.brand-modal {
  --width: 90%;
  --max-width: 600px;
  --height: 100vh;
  --max-height: 90%;
}

.modal-content {
  --background: #f8f9fa;
}

.form-section {
  background: var(--form-background);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e0e0e0;
}

.form-item {
  --background: transparent;
  --border-radius: 8px;
  --padding-start: 0;
  --padding-end: 0;
  margin-bottom: 16px;
}

.form-item ion-label {
  font-weight: 500;
  color: #333;
}

.form-item ion-input,
.form-item ionic-selectable {
  --background: #f5f5f5;
  --border-radius: 8px;
  --padding-start: 12px;
  --padding-end: 12px;
  margin-top: 8px;
}

.form-actions {
  padding: 16px 0;
}

.submit-button {
  --border-radius: 12px;
  height: 48px;
  font-weight: 600;
  text-transform: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .search-section {
    padding: 12px;
    top: 56px; /* Adjust for mobile header height */
  }

  .buyers-list {
    padding: 8px 12px;
    // margin-top: 140px; /* Adjust for mobile fixed sections */
  }

  .no-data-item {
    margin: 12px;
    margin-top: 120px;
  }

  .buyer-item {
    --padding-start: 12px;
    --padding-end: 12px;
    --padding-top: 10px;
    --padding-bottom: 10px;
    --min-height: 100px;
  }

  .buyer-icon {
    width: 50px;
    height: 50px;
    margin-right: 12px;
  }

  .person-icon {
    font-size: 24px;
  }

  .buyer-name {
    font-size: 16px;
  }

  .buyer-meta {
    font-size: 13px;
    gap: 12px;
  }

  .buyer-details-row {
    font-size: 11px;
    gap: 8px;
  }

  .balance-row {
    font-size: 13px;
  }

  .buyer-actions {
    gap: 6px;
    min-width: 50px;
  }

  .action-button {
    width: 36px;
    height: 36px;
  }

  .summary-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .buyer-modal,
  .asset-modal,
  .deposit-modal,
  .buyer-details-modal {
    --width: 95%;
    --max-width: none;
  }

  .form-section {
    padding: 12px;
    margin-bottom: 12px;
  }

  .section-title {
    font-size: 14px;
  }
}

.status-active {
  color: var(--ion-color-success);
}

.status-pending {
  color: var(--ion-color-warning);
}

.status-inactive {
  color: var(--ion-color-danger);
}

/* Brand Modal Styling */
.brand-modal-content {
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 16px;
  --padding-bottom: 16px;
}

.brand-section {
  .section-header {
    margin-bottom: 24px;
    text-align: center;

    h3 {
      color: var(--ion-color-secondary);
      font-weight: 600;
      margin-bottom: 8px;
    }

    p {
      color: #666;
      font-size: 14px;
      margin: 0;
    }
  }

  .brands-list {
    .brand-item {
      --border-color: #e0e0e0;
      margin: 8px 0;
      border-radius: 8px;
      transition: all 0.3s ease;

      &.selected {
        --background: #f3e5f5;
        --border-color: var(--ion-color-secondary);
        border: 2px solid var(--border-color);
      }

      &:hover {
        --background: #f5f5f5;
      }

      ion-label h3 {
        font-weight: 500;
        color: #333;
      }

      ion-checkbox {
        --color-checked: var(--ion-color-secondary);
      }
    }
  }

  .no-brands {
    text-align: center;
    padding: 40px 20px;
    color: #666;

    .no-brands-icon {
      font-size: 64px;
      color: #ccc;
      margin-bottom: 16px;
    }

    h3 {
      margin: 16px 0 8px 0;
      color: #999;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }

  .selected-summary {
    margin-top: 24px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e0e0e0;

    h4 {
      color: var(--ion-color-secondary);
      margin: 0 0 12px 0;
      font-weight: 600;
    }

    .selected-brands {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      ion-chip {
        --background: var(--ion-color-secondary);
        --color: white;
        font-size: 12px;
      }
    }
  }
}

.brand-actions {
  margin-top: 24px;
  display: flex;
  gap: 12px;

  .cancel-button {
    --border-color: #ddd;
    --color: #666;
    flex: 1;
  }

  .save-button {
    flex: 1;
  }
}