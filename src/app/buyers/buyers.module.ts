
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { BuyersPageRoutingModule } from './buyers-routing.module';

import { BuyersPage } from './buyers.page';
import { SharedModule } from '../shared/modules/shared/shared.module';
import { IonicSelectableModule } from 'ionic-selectable';

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    IonicModule,
    BuyersPageRoutingModule,
    SharedModule,
    IonicSelectableModule,
    
  ],
  declarations: [BuyersPage,]
})
export class BuyersPageModule { }
