import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { Router } from '@angular/router';
import { ActionSheetController, Alert<PERSON>ontroller, ModalController } from '@ionic/angular';
import { RouteBillingService, RouteBillingSummary } from '../shared/services/route-billing.service';
import { BuyerService } from '../shared/services/buyer.service';
import { RouteManagementService } from '../shared/services/route-management.service';
import { ToastService } from '../shared/services/toast.service';
import { IonLoaderService } from '../shared/services/ion-loader.service';
import { ShopImageUploadModalComponent, ShopImageData } from '../shared/components/shop-image-upload-modal/shop-image-upload-modal.component';

export interface StoreVisitShop {
  id: number;
  name: string;
  route_id: number;
  route_name?: string;
  place?: string;
  phone_no?: string;
  active?: boolean;
  has_photos: boolean;
  photo_count: number;
  last_photo_date?: string;
  has_today_photos: boolean;
  today_photo_count: number;
  visit_status: 'pending' | 'visited' | 'completed';
  visit_date?: string;
  visit_notes?: string;
}

export interface RouteSchedule {
  id: number;
  route: number;
  route_name: string;
  weekday: string;
  is_active: boolean;
  expected_billing_time?: string;
  notes?: string;
}

@Component({
  selector: 'app-store-visit',
  templateUrl: './store-visit.page.html',
  styleUrls: ['./store-visit.page.scss'],
})
export class StoreVisitPage implements OnInit {
  @ViewChild('fileInput', { static: false }) fileInput!: ElementRef<HTMLInputElement>;
  @ViewChild('cameraInput', { static: false }) cameraInput!: ElementRef<HTMLInputElement>;

  // Route selection
  selectedWeekday: string = '';
  selectedRoute: number | null = null;
  availableRoutes: RouteSchedule[] = [];
  
  // Store data
  stores: StoreVisitShop[] = [];
  filteredStores: StoreVisitShop[] = [];
  selectedStore: StoreVisitShop | null = null;
  
  // Summary statistics
  summaryStats = {
    totalStores: 0,
    storesWithPhotos: 0,
    storesWithoutPhotos: 0,
    storesVisited: 0,
    storesPending: 0,
    storesCompleted: 0,
    completionPercentage: 0
  };
  
  // UI states
  loading = false;
  showRouteSelection = true;
  showStoreList = false;
  showStoreDetails = false;
  
  // Search and filter
  searchTerm = '';
  filterStatus: 'all' | 'pending' | 'visited' | 'completed' = 'all';
  
  // Photo capture
  selectedFile: File | null = null;
  previewUrl: string | null = null;
  captureMode: 'camera' | 'gallery' = 'camera';
  photoNotes = '';

  // Weekday options
  weekdays = [
    { value: 'monday', label: 'Monday', icon: 'calendar-outline' },
    { value: 'tuesday', label: 'Tuesday', icon: 'calendar-outline' },
    { value: 'wednesday', label: 'Wednesday', icon: 'calendar-outline' },
    { value: 'thursday', label: 'Thursday', icon: 'calendar-outline' },
    { value: 'friday', label: 'Friday', icon: 'calendar-outline' },
    { value: 'saturday', label: 'Saturday', icon: 'calendar-outline' },
    { value: 'sunday', label: 'Sunday', icon: 'calendar-outline' }
  ];

  constructor(
    private router: Router,
    private routeBillingService: RouteBillingService,
    private buyerService: BuyerService,
    private routeManagementService: RouteManagementService,
    private toast: ToastService,
    private ionLoaderService: IonLoaderService,
    private actionSheetController: ActionSheetController,
    private alertController: AlertController,
    private modalController: ModalController
  ) { }

  ngOnInit() {
    this.initializePage();
  }

  async initializePage() {
    try {
      this.loading = true;
      
      // Set default weekday to today
      const today = new Date().toLocaleDateString('en-US', { weekday: 'long' });
      this.selectedWeekday = today.toLowerCase();
      
      // Load available routes for the selected weekday
      await this.loadAvailableRoutes();
      
    } catch (error) {
      console.error('Error initializing store visit page:', error);
      this.toast.toastServices('Error loading store visit data', 'danger', 'top');
    } finally {
      this.loading = false;
    }
  }

  async loadAvailableRoutes() {
    try {
      const routesForWeekday = await this.routeManagementService.getRoutesForWeekday(this.selectedWeekday);
      
      if (routesForWeekday.length > 0) {
        this.availableRoutes = routesForWeekday.map((route: any) => ({
          id: route.id,
          route: route.id,
          route_name: route.name,
          weekday: this.selectedWeekday,
          is_active: true
        }));
      } else {
        this.availableRoutes = [];
      }
    } catch (error) {
      console.error('Error loading available routes:', error);
      this.toast.toastServices('Error loading routes', 'danger', 'top');
    }
  }

  async selectRoute(route: RouteSchedule) {
    try {
      this.selectedRoute = route.route;
      this.loading = true;
      
      // Load stores for the selected route
      await this.loadStoresForRoute();
      
      this.showRouteSelection = false;
      this.showStoreList = true;
      
    } catch (error) {
      console.error('Error selecting route:', error);
      this.toast.toastServices('Error loading stores for route', 'danger', 'top');
    } finally {
      this.loading = false;
    }
  }

  async loadStoresForRoute() {
    if (!this.selectedRoute) return;

    try {
      this.loading = true;
      
      // Get all buyers and filter by route on frontend
      const response: any = await this.buyerService.getBuyer();
      
      if (response.success) {
        // Filter buyers by route - check multiple possible field names
        const routeBuyers = response.data.filter((buyer: any) => {
          // Try different possible field names for route
          const buyerRouteId = buyer.route || buyer.route_id;
          const buyerRouteName = buyer.route_name;
          const selectedRouteName = this.availableRoutes.find(r => r.route === this.selectedRoute)?.route_name;
          
          return buyerRouteId === this.selectedRoute || buyerRouteName === selectedRouteName;
        });
        
        if (routeBuyers.length === 0) {
          this.toast.toastServices(`No stores found for the selected route`, 'warning', 'top');
        }
        
        this.stores = routeBuyers.map((buyer: any) => ({
          id: buyer.id,
          name: buyer.name,
          route_id: this.selectedRoute!,
          route_name: this.availableRoutes.find(r => r.route === this.selectedRoute)?.route_name,
          place: buyer.place,
          phone_no: buyer.phone_no,
          active: buyer.active !== false, // Default to true if not specified
          has_photos: false,
          photo_count: 0,
          last_photo_date: null,
          has_today_photos: false,
          today_photo_count: 0,
          visit_status: 'pending',
          visit_date: null,
          visit_notes: ''
        }));

        // Load photo data for stores
        await this.loadPhotoDataForStores();
        
        this.filteredStores = [...this.stores];
        
        // Calculate summary statistics
        this.calculateSummaryStats();
        
        this.toast.toastServices(`Loaded ${this.stores.length} stores for the selected route`, 'success', 'top');
      } else {
        this.toast.toastServices('Failed to load stores for route', 'danger', 'top');
      }
    } catch (error) {
      console.error('Error loading stores for route:', error);
      this.toast.toastServices('Error loading stores', 'danger', 'top');
    } finally {
      this.loading = false;
    }
  }

  async loadPhotoDataForStores() {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      // Get photo data for all stores
      const photoResponse = await this.routeBillingService.getFreezerPhotos({
        route_id: this.selectedRoute,
        date: today
      });

      if (photoResponse.success && photoResponse.data) {
        // Create a map of store photos
        const storePhotosMap = new Map();
        
        photoResponse.data.forEach((photo: any) => {
          const storeId = photo.buyer || photo.buyer_id;
          const photoDate = photo.date_taken;
          
          if (!storePhotosMap.has(storeId)) {
            storePhotosMap.set(storeId, {
              count: 0,
              lastDate: null,
              todayCount: 0
            });
          }
          
          const existing = storePhotosMap.get(storeId);
          existing.count++;
          
          if (photoDate === today) {
            existing.todayCount++;
          }
          
          if (!existing.lastDate || photoDate > existing.lastDate) {
            existing.lastDate = photoDate;
          }
        });

        // Update stores with photo information
        this.stores = this.stores.map(store => {
          const photoData = storePhotosMap.get(store.id);
          return {
            ...store,
            has_photos: photoData ? photoData.count > 0 : false,
            photo_count: photoData ? photoData.count : 0,
            last_photo_date: photoData ? photoData.lastDate : null,
            has_today_photos: photoData ? photoData.todayCount > 0 : false,
            today_photo_count: photoData ? photoData.todayCount : 0,
            visit_status: photoData && photoData.todayCount > 0 ? 'completed' : 'pending'
          };
        });
        
        // Recalculate summary statistics
        this.calculateSummaryStats();
      } else {
        // No photos found, set default values
        this.stores = this.stores.map(store => ({
          ...store,
          has_photos: false,
          photo_count: 0,
          last_photo_date: null,
          has_today_photos: false,
          today_photo_count: 0,
          visit_status: 'pending'
        }));
        
        // Recalculate summary statistics
        this.calculateSummaryStats();
      }
    } catch (error) {
      console.error('Error loading photo data:', error);
      // Set default values on error
      this.stores = this.stores.map(store => ({
        ...store,
        has_photos: false,
        photo_count: 0,
        last_photo_date: null,
        has_today_photos: false,
        today_photo_count: 0,
        visit_status: 'pending'
      }));
    }
  }

  onWeekdayChange() {
    this.selectedRoute = null;
    this.stores = [];
    this.filteredStores = [];
    this.showRouteSelection = true;
    this.showStoreList = false;
    this.showStoreDetails = false;
    this.loadAvailableRoutes();
  }

  onSearchChange() {
    this.filterStores();
  }

  onFilterChange() {
    this.filterStores();
  }

  filterStores() {
    let filtered = [...this.stores];
    
    // Apply search filter
    if (this.searchTerm.trim()) {
      const search = this.searchTerm.toLowerCase();
      filtered = filtered.filter(store => 
        store.name.toLowerCase().includes(search) ||
        (store.place && store.place.toLowerCase().includes(search))
      );
    }
    
    // Apply status filter
    if (this.filterStatus !== 'all') {
      filtered = filtered.filter(store => store.visit_status === this.filterStatus);
    }
    
    this.filteredStores = filtered;
    
    // Recalculate summary statistics when filtering
    this.calculateSummaryStats();
  }

  selectStore(store: StoreVisitShop) {
    this.selectedStore = store;
    this.showStoreDetails = true;
  }

  backToStoreList() {
    this.selectedStore = null;
    this.showStoreDetails = false;
    this.clearPhotoSelection();
  }

  backToRouteSelection() {
    this.selectedRoute = null;
    this.stores = [];
    this.filteredStores = [];
    this.showRouteSelection = true;
    this.showStoreList = false;
    this.showStoreDetails = false;
  }

  async openShopImageModal(store: StoreVisitShop) {
    const shopImageData: ShopImageData = {
      id: store.id,
      name: store.name,
      route_id: store.route_id,
      route_name: store.route_name,
      place: store.place,
      phone_no: store.phone_no
    };

    const modal = await this.modalController.create({
      component: ShopImageUploadModalComponent,
      componentProps: {
        shop: shopImageData
      },
      cssClass: 'shop-image-upload-modal'
    });

    await modal.present();

    // Refresh data after modal closes
    const { data } = await modal.onWillDismiss();
    if (data) {
      await this.loadPhotoDataForStores();
      this.filterStores();
    }
  }

  async presentImageOptions() {
    const actionSheet = await this.actionSheetController.create({
      header: 'Add Photo',
      cssClass: 'image-options-action-sheet',
      buttons: [
        {
          text: 'Take Photo',
          icon: 'camera-outline',
          handler: () => {
            this.openCamera();
          }
        },
        {
          text: 'Choose from Gallery',
          icon: 'images-outline',
          handler: () => {
            this.openGallery();
          }
        },
        {
          text: 'Cancel',
          icon: 'close-outline',
          role: 'cancel'
        }
      ]
    });

    await actionSheet.present();
  }

  openCamera() {
    this.captureMode = 'camera';
    if (this.cameraInput) {
      this.cameraInput.nativeElement.click();
    }
  }

  openGallery() {
    this.captureMode = 'gallery';
    if (this.fileInput) {
      this.fileInput.nativeElement.click();
    }
  }

  onFileSelected(event: any) {
    const file: File = event.target.files[0];
    if (file) {
      this.handleFileSelection(file);
    }
  }

  handleFileSelection(file: File) {
    // Validate file type
    if (!file.type.startsWith('image/')) {
      this.toast.toastServices('Please select a valid image file', 'danger', 'top');
      return;
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      this.toast.toastServices('Image size should not exceed 10MB', 'danger', 'top');
      return;
    }

    this.selectedFile = file;
    
    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      this.previewUrl = e.target?.result as string;
    };
    reader.readAsDataURL(file);

    this.toast.toastServices('Image selected successfully', 'success', 'top');
  }

  clearPhotoSelection() {
    this.selectedFile = null;
    this.previewUrl = null;
    this.photoNotes = '';
    
    // Reset file inputs
    if (this.fileInput) {
      this.fileInput.nativeElement.value = '';
    }
    if (this.cameraInput) {
      this.cameraInput.nativeElement.value = '';
    }
  }

  async uploadImage() {
    if (!this.selectedFile || !this.selectedStore) {
      this.toast.toastServices('Please select an image and store first', 'danger', 'top');
      return;
    }

    try {
      await this.ionLoaderService.startLoader();

      // Get current location if available
      let location = null;
      try {
        location = await this.routeBillingService.getCurrentLocation();
      } catch (error) {
        console.log('Location not available:', error);
      }

      const response = await this.routeBillingService.uploadFreezerPhoto(
        this.selectedStore.id,
        this.selectedStore.route_id,
        this.selectedFile,
        this.photoNotes || `Store visit photo for ${this.selectedStore.name}`,
        location
      );

      if (response.success) {
        this.toast.toastServices('Image uploaded successfully', 'success', 'top');
        this.clearPhotoSelection();
        
        // Update store visit status
        this.selectedStore.visit_status = 'completed';
        this.selectedStore.has_today_photos = true;
        this.selectedStore.today_photo_count++;
        
        // Refresh photo data
        await this.loadPhotoDataForStores();
        this.filterStores();
        
        // Recalculate summary statistics
        this.calculateSummaryStats();
      } else {
        this.toast.toastServices('Failed to upload image', 'danger', 'top');
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      this.toast.toastServices('Error uploading image', 'danger', 'top');
    } finally {
      this.ionLoaderService.dismissLoader();
    }
  }

  markStoreAsVisited(store: StoreVisitShop) {
    store.visit_status = 'visited';
    store.visit_date = new Date().toISOString().split('T')[0];
    this.filterStores();
    
    // Recalculate summary statistics
    this.calculateSummaryStats();
  }

  getVisitStatusColor(status: string): string {
    switch (status) {
      case 'completed': return 'success';
      case 'visited': return 'warning';
      case 'pending': return 'medium';
      default: return 'medium';
    }
  }

  getVisitStatusIcon(status: string): string {
    switch (status) {
      case 'completed': return 'checkmark-circle';
      case 'visited': return 'time';
      case 'pending': return 'ellipse-outline';
      default: return 'ellipse-outline';
    }
  }

  getCompletionPercentage(): number {
    if (this.stores.length === 0) return 0;
    const completed = this.stores.filter(store => store.visit_status === 'completed').length;
    return Math.round((completed / this.stores.length) * 100);
  }

  calculateSummaryStats() {
    // Filter active stores for statistics (exclude inactive shops from stats)
    const activeStores = this.stores.filter(store => store.active !== false);
    
    this.summaryStats = {
      totalStores: this.stores.length, // Total stores including inactive
      storesWithPhotos: activeStores.filter(store => store.has_today_photos).length,
      storesWithoutPhotos: activeStores.filter(store => !store.has_today_photos).length,
      storesVisited: activeStores.filter(store => store.visit_status === 'visited').length,
      storesPending: activeStores.filter(store => store.visit_status === 'pending').length,
      storesCompleted: activeStores.filter(store => store.visit_status === 'completed').length,
      completionPercentage: activeStores.length > 0 ? Math.round((activeStores.filter(store => store.visit_status === 'completed').length / activeStores.length) * 100) : 0
    };
  }

  trackByStoreId(index: number, store: StoreVisitShop): number {
    return store.id;
  }

  trackByRouteId(index: number, route: RouteSchedule): number {
    return route.id;
  }
} 