import { RouteService } from './../shared/services/route.service';
import { Component, OnInit, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { ConstantsService } from '../shared/services/constants.service';
import { AuthenticationService } from '../shared/services/authentication.service';
import { ToastService } from '../shared/services/toast.service';
import { IonLoaderService } from '../shared/services/ion-loader.service';
import { DashboardService, DashboardStats, ComprehensiveDashboardData } from '../shared/services/dashboard.service';

import { TranslationService } from '../shared/services/translation.service';

// Import Chart.js
declare var Chart: any;



@Component({
  selector: 'app-menu',
  templateUrl: './menu.page.html',
  styleUrls: ['./menu.page.scss'],
})
export class MenuPage implements OnInit, AfterViewInit {
  @ViewChild('userDistributionChart', { static: false }) userDistributionChart!: ElementRef<HTMLCanvasElement>;
  role: string;
  component: any = [];
  userName: string = 'User';
  isLoading: boolean = false;
  private isComponentActive: boolean = true; // Track if component is still active

  // Dashboard data
  dashboardStats: DashboardStats = {
    activeUsers: 0,
    inactiveUsers: 0,
    totalUsers: 0,
    totalInvoices: 0,
    totalProducts: 0,
    recentActivities: [],
    userDistribution: { active: 0, inactive: 0 },
    activityOverview: []
  };

  // Business metrics for enhanced dashboard
  businessMetrics = {
    totalRevenue: 0,
    monthlySales: 0,
    activeCustomers: 0,
    totalProducts: 0,
    pendingOrders: 0,
    lowStockItems: 0
  };

  // Financial year label for revenue display clarity
  financialYearLabel: string = 'FY 2024-25';



  // Quick access items (preserving original menu functionality)
  quickAccessItems: any[] = [];

  // Recent activities and inactive buyers (loaded from service)
  recentActivities: any[] = [];
  inactiveBuyers: any[] = [];

  // Comprehensive dashboard data from backend
  comprehensiveDashboardData: ComprehensiveDashboardData | null = null;

  constructor(
    public constant: ConstantsService,
    public routerService: RouteService,
    public authService: AuthenticationService,
    private dashboardService: DashboardService,
    private toast: ToastService,
    private ionLoaderService: IonLoaderService,

    public translate: TranslationService
  ) {
    this.role = localStorage.getItem('role');
    this.initializeComponent();
    this.setUserName();
  }

  ngOnInit() {
    console.log('MenuPage ngOnInit - Starting dashboard data load');
    // Force cleanup any existing loaders to prevent hanging
    this.ionLoaderService.forceCleanup();
    
    // Check for any hanging loaders and clean them up
    this.checkAndCleanupLoaders();
    
    // Add a small delay to ensure any existing loaders are properly cleaned up
    setTimeout(async () => {
      await this.ionLoaderService.forceCleanup();
      this.loadDashboardData();
    }, 100);
  }

  private async checkAndCleanupLoaders() {
    try {
      const hasActiveLoaders = await this.ionLoaderService.hasActiveLoaders();
      if (hasActiveLoaders) {
        console.warn('MenuPage checkAndCleanupLoaders - Found active loaders, cleaning up');
        await this.ionLoaderService.forceCleanup();
      }
    } catch (error) {
      console.error('MenuPage checkAndCleanupLoaders - Error checking loaders:', error);
      // Force cleanup anyway
      await this.ionLoaderService.forceCleanup();
    }
  }

  ionViewWillEnter() {
    console.log('MenuPage ionViewWillEnter - Page entering');
    this.isComponentActive = true;
    // Force cleanup any existing loaders to prevent hanging
    this.ionLoaderService.forceCleanup();
  }

  ionViewDidEnter() {
    console.log('MenuPage ionViewDidEnter - Page entered');
  }

  ionViewWillLeave() {
    console.log('MenuPage ionViewWillLeave - Page leaving');
    this.isComponentActive = false;
    // Force cleanup when leaving to prevent hanging loaders
    this.ionLoaderService.forceCleanup();
  }

  ionViewDidLeave() {
    console.log('MenuPage ionViewDidLeave - Page left');
  }

  ngOnDestroy() {
    console.log('MenuPage ngOnDestroy - Page destroyed');
    this.isComponentActive = false;
    // Force cleanup when destroyed to prevent hanging loaders
    this.ionLoaderService.forceCleanup();
  }

  ngAfterViewInit() {
    // Initialize charts after view is ready
    setTimeout(() => {
      this.initializeCharts();
    }, 500);
  }

  private initializeComponent() {
    try {
      const metadata = localStorage.getItem('metadata');
      if (metadata) {
        this.component = JSON.parse(metadata).component || [];
        this.component.sort((b: any, a: any) => b.sort_order - a.sort_order);

        // Set up quick access items (preserving original menu functionality)
        this.quickAccessItems = this.component.filter((item: any) => item.active);
      }
    } catch (error) {
      console.error('Error parsing metadata:', error);
      this.component = [];
      this.quickAccessItems = [];
    }
  }

  private setUserName() {
    this.userName = this.dashboardService.getUserName();
  }

  async loadDashboardData() {
    console.log('MenuPage loadDashboardData - Starting');
    
    // Check if component is still active
    if (!this.isComponentActive) {
      console.log('MenuPage loadDashboardData - Component no longer active, skipping data load');
      return;
    }
    
    this.isLoading = true;
    
    // Set a timeout to force dismiss loader if it takes too long
    const loaderTimeout = setTimeout(async () => {
      console.warn('MenuPage loadDashboardData - Timeout reached, force dismissing loader');
      if (this.isComponentActive) {
        await this.ionLoaderService.forceCleanup();
        this.isLoading = false;
      }
    }, 15000); // 15 second timeout
    
    try {
      await this.ionLoaderService.withLoader(async () => {
        // Check again if component is still active
        if (!this.isComponentActive) {
          console.log('MenuPage loadDashboardData - Component no longer active, stopping data load');
          return;
        }
        
        console.log('MenuPage loadDashboardData - Loader started, running API calls');
        
        // Run API calls with individual error handling
        let dashboardStats, inactiveBuyers;
        
        try {
          dashboardStats = await this.dashboardService.getDashboardStats();
        } catch (error) {
          console.error('Error loading dashboard stats:', error);
          dashboardStats = {
            activeUsers: 0,
            inactiveUsers: 0,
            totalUsers: 0,
            totalInvoices: 0,
            totalProducts: 0,
            recentActivities: [],
            userDistribution: { active: 0, inactive: 0 },
            activityOverview: []
          };
        }
        
        try {
          inactiveBuyers = await this.dashboardService.getInactiveBuyers();
        } catch (error) {
          console.error('Error loading inactive buyers:', error);
          inactiveBuyers = [];
        }
        
        try {
          await this.loadBusinessMetrics();
        } catch (error) {
          console.error('Error loading business metrics:', error);
        }
        
        // Check if component is still active before updating data
        if (!this.isComponentActive) {
          console.log('MenuPage loadDashboardData - Component no longer active, not updating data');
          return;
        }
        
        console.log('MenuPage loadDashboardData - API calls completed');
        
        this.dashboardStats = dashboardStats;
        this.recentActivities = this.dashboardStats.recentActivities;
        this.inactiveBuyers = inactiveBuyers;
        
        console.log('MenuPage loadDashboardData - Data assigned, loader will dismiss');
      }, 'Loading dashboard data...');
      
      console.log('MenuPage loadDashboardData - Loader dismissed successfully');
    } catch (error) {
      console.error('MenuPage loadDashboardData - Error:', error);
      if (this.isComponentActive) {
        this.toast.toastServices('Error loading dashboard data', 'danger', 'top');
      }
      
      // Set default values on error only if component is still active
      if (this.isComponentActive) {
        this.dashboardStats = {
          activeUsers: 0,
          inactiveUsers: 0,
          totalUsers: 0,
          totalInvoices: 0,
          totalProducts: 0,
          recentActivities: [],
          userDistribution: { active: 0, inactive: 0 },
          activityOverview: []
        };
        this.recentActivities = [];
        this.inactiveBuyers = [];
      }
    } finally {
      // Clear the timeout
      clearTimeout(loaderTimeout);
      
      console.log('MenuPage loadDashboardData - Finally block, setting isLoading to false');
      if (this.isComponentActive) {
        this.isLoading = false;
      }
      
      // Force cleanup any remaining loaders
      await this.ionLoaderService.forceCleanup();
    }
  }

  private async loadBusinessMetrics() {
    try {
      // Try to get comprehensive dashboard data from backend
      this.comprehensiveDashboardData = await this.dashboardService.getComprehensiveDashboardData();

      if (this.comprehensiveDashboardData) {
        // Use real data from backend
        this.businessMetrics = {
          totalRevenue: this.comprehensiveDashboardData.financial_overview.total_revenue,
          monthlySales: this.comprehensiveDashboardData.financial_overview.monthly_sales,
          activeCustomers: this.comprehensiveDashboardData.summary_metrics.active_buyers,
          totalProducts: this.comprehensiveDashboardData.summary_metrics.total_products,
          pendingOrders: this.comprehensiveDashboardData.activity_summary.total_transactions,
          lowStockItems: this.comprehensiveDashboardData.summary_metrics.low_stock_items
        };
        
        // Update financial year label from backend data
        if (this.comprehensiveDashboardData.financial_overview.financial_year_label) {
          this.financialYearLabel = this.comprehensiveDashboardData.financial_overview.financial_year_label;
        }
      }
    } catch (error) {
      console.error('Error loading business metrics:', error);
      // Set default values on error
      this.businessMetrics = {
        totalRevenue: 0,
        monthlySales: 0,
        activeCustomers: 0,
        totalProducts: 0,
        pendingOrders: 0,
        lowStockItems: 0
      };
    }
  }

  

  // Quick access navigation (preserving original functionality)
 async navigateToQuickAccess(item: any) {
    if (await this.authService.checkPermission('view', item.slug)) {
      const mappedUrl = this.mapUrlToTabRoute(item.url);
      this.routerService.routerFunction(mappedUrl);
    }
  }

  // Map old individual routes to new tab routes
  private mapUrlToTabRoute(url: string): string {
    const urlMapping: { [key: string]: string } = {
      'tabs/home': 'tabs/home',
      'product': 'tabs/product',
      'sales-bill': 'tabs/sales-bill',
      'report': 'report',
      'settings': 'tabs/settings'
    };

    return urlMapping[url] || url;
  }

  // New dashboard-specific navigation methods
  createNewInvoice() {
    this.routerService.routerFunction('create-invoice');
  }

  viewInvoices() {
    this.routerService.routerFunction('tabs/sales-bill');
  }

  manageCustomers() {
    this.routerService.routerFunction('tabs/buyers');
  }

  manageProducts() {
    this.routerService.routerFunction('tabs/product');
  }

  receivePayment() {
    this.routerService.routerFunction('sales-payment');
  }

  viewChecklists() {
    this.routerService.routerFunction('checklist');
  }

  manageUsers() {
    this.routerService.routerFunction('buyers');
  }

  viewReports() {
    this.routerService.routerFunction('report');
  }

  viewLedger() {
    this.routerService.routerFunction('ledger');
  }

  manageSettings() {
    this.routerService.routerFunction('tabs/settings');
  }

  viewStoreVisits() {
    this.routerService.routerFunction('store-visit');
  }

  // Buyer Management Methods
  // Contact buyer using phone dialer
  contactBuyer(buyer: any) {
    if (buyer && buyer.phone_no) {
      window.location.href = `tel:${buyer.phone_no}`;
    } else {
      this.toast.toastServices('No contact number available', 'warning', 'top');
    }
  }

  viewBuyerDetails(buyer: any) {
    // Implement view buyer details functionality
    console.log('Viewing buyer details:', buyer);
    this.toast.toastServices(`Viewing details for ${buyer.name}`, 'primary', 'top');
    // You can navigate to a detailed buyer view or show a modal
  }

  // Initialize charts with Chart.js
  private initializeCharts() {
    // Load Chart.js dynamically and then create charts
    this.loadChartJS().then(() => {
      this.createUserDistributionChart();
    }).catch(() => {
      // Fallback to simple canvas implementation if Chart.js fails to load
      this.drawSimplePieChart();
    });
  }

  private async loadChartJS(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (typeof Chart !== 'undefined') {
        resolve();
        return;
      }

      // Try to load Chart.js from CDN as fallback
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js';
      script.onload = () => resolve();
      script.onerror = () => reject();
      document.head.appendChild(script);
    });
  }

  private createUserDistributionChart() {
    if (!this.userDistributionChart?.nativeElement) return;

    const ctx = this.userDistributionChart.nativeElement.getContext('2d');
    if (!ctx) return;

    new Chart(ctx, {
      type: 'doughnut',
      data: {
        labels: ['Active Users', 'Inactive Users'],
        datasets: [{
          data: [this.dashboardStats.activeUsers, this.dashboardStats.inactiveUsers],
          backgroundColor: ['#28a745', '#ffc107'],
          borderWidth: 2,
          borderColor: '#ffffff'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              padding: 20,
              usePointStyle: true
            }
          }
        }
      }
    });
  }



  private drawSimplePieChart() {
    if (!this.userDistributionChart?.nativeElement) return;

    const canvas = this.userDistributionChart.nativeElement;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = 300;
    canvas.height = 300;

    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = 80;

    const total = this.dashboardStats.activeUsers + this.dashboardStats.inactiveUsers;
    const activePercentage = total > 0 ? this.dashboardStats.activeUsers / total : 0;

    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Active users slice
    ctx.beginPath();
    ctx.moveTo(centerX, centerY);
    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI * activePercentage);
    ctx.closePath();
    ctx.fillStyle = '#28a745';
    ctx.fill();

    // Inactive users slice
    ctx.beginPath();
    ctx.moveTo(centerX, centerY);
    ctx.arc(centerX, centerY, radius, 2 * Math.PI * activePercentage, 2 * Math.PI);
    ctx.closePath();
    ctx.fillStyle = '#ffc107';
    ctx.fill();

    // Labels
    ctx.fillStyle = '#2c3e50';
    ctx.font = '14px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(`Active: ${this.dashboardStats.activeUsers}`, centerX, centerY + radius + 30);
    ctx.fillText(`Inactive: ${this.dashboardStats.inactiveUsers}`, centerX, centerY + radius + 50);
  }



}
