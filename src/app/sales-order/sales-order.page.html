<app-header [title]="'Sales Order'" [returnUrl]="'tabs/home'"></app-header>

<ion-content>
  <!-- Top -->
  <ion-grid  *ngIf="role != 'sales_officer'">
    <ion-row>
      <ion-col size="12">
        <ion-card class="ion-padding">
          <ion-row>
            <ion-col size="6">
              <ion-item class="ion-margin-bottom ion-no-padding">
                <ion-label position="stacked"> Select Route </ion-label>
                <ionic-selectable name="route" [shouldFocusSearchbar]="true" [(ngModel)]="selectedRoute"
                  placeholder="Route" [items]="routes" itemValueField="id" itemTextField="name" [canSearch]="true">
                </ionic-selectable>
              </ion-item>
            </ion-col>
            <ion-col size="6">
              <ion-item class="ion-margin-bottom ion-no-padding">
                <ion-label position="stacked"> Select SalesPerson </ion-label>
                <ionic-selectable name="salesPerson" [shouldFocusSearchbar]="true" [(ngModel)]="selectedSalesPerson"
                  placeholder="Sales Person" [items]="salesPersons" itemValueField="id" itemTextField="name"
                  [canSearch]="true">
                </ionic-selectable>
              </ion-item>
            </ion-col>
          </ion-row>
          <ion-item>
            <ion-button color="primary" (click)="getData()">
              Filter
              <ion-icon name="filter" class="ion-padding-start"></ion-icon>
            </ion-button>
          </ion-item>
        </ion-card>
      </ion-col>
    </ion-row>
  </ion-grid>
  <!-- Start : Main  -->
  <ion-grid class="ion-text-center" style="margin-bottom: 50px">
    <ion-grid class="selection">
      <ion-row>
        <ion-col size="12">
          <div style="max-width: 100%; overflow-y: scroll">
            <ion-grid class="ion-no-padding main-content" style="min-width: 900px">
              <ion-row class="header-row">
                <ion-col size="1">
                  <ion-text>Id</ion-text>
                </ion-col>
                <ion-col size="1.5">
                  <ion-text>Name</ion-text>
                </ion-col>
                <ion-col size="1.5">
                  <ion-text>Previous Balance</ion-text>
                </ion-col>
                <ion-col size="1">
                  <ion-text>Bill Amount</ion-text>
                </ion-col>
                <ion-col size="1">
                  <ion-text>Receivable Amount</ion-text>
                </ion-col>
                <ion-col size="1">
                  <ion-text>Received Amount</ion-text>
                </ion-col>
                <ion-col size="1">
                  <ion-text>Current Balance</ion-text>
                </ion-col>
                <ion-col size="1">
                  <ion-text>Date</ion-text>
                </ion-col>
                <ion-col size="0.5">
                  <ion-text>Print</ion-text>
                </ion-col>
                <ion-col size="2.5">
                  <ion-text>Actions</ion-text>
                </ion-col>
              </ion-row>
              <ion-row *ngFor="let d of data">
                <ion-col size="1">
                  <ion-text>{{d.id}}</ion-text>
                </ion-col>
                <ion-col size="1.5">
                  <ion-text>{{d.name}}</ion-text>
                </ion-col>
                <ion-col size="1.5">
                  <ion-text>{{d.previous_balance | currency: 'INR'}}</ion-text>
                </ion-col>
                <ion-col size="1">
                  <ion-text>{{d.bill_amount | currency: 'INR'}}</ion-text>
                </ion-col>
                <ion-col size="1">
                  <ion-text> {{d.receivable_amount | currency: 'INR'}} </ion-text>
                </ion-col>
                <ion-col size="1">
                  <ion-text> {{d.received_amount | currency: 'INR'}} </ion-text>
                </ion-col>
                <ion-col size="1">
                  <ion-text>{{d.current_balance | currency: 'INR'}}</ion-text>
                </ion-col>
                <ion-col size="1">
                  <ion-text>{{d.date}}</ion-text>
                </ion-col>
                <ion-col size="0.5">
                  <ion-text (click)="printInvoice(d)">
                    <ion-badge color="primary" mode="ios"><ion-icon name="print"></ion-icon></ion-badge>
                  </ion-text>
                </ion-col>
                <ion-col size="2.5" class="button-font">
                  <ion-buttons *ngIf="role != 'sales_officer'">
                    <ion-button style="margin-right: 10px" color="primary" (click)="editInvoice(d.id)" fill="outline">
                      <span class="visible-on-mobile-only">
                        <ion-icon name="git-compare" class="icon-style"></ion-icon>
                      </span>
                      <span class="hidden-on-mobile"> Create Invoice </span>
                    </ion-button>
                  </ion-buttons>
                  &nbsp;<ion-buttons>
                    <ion-button style="margin-right: 10px" color="primary" (click)="editInvoiceSO(d.id)" fill="outline">
                      <span class="visible-on-mobile-only">
                        <ion-icon name="pencil" class="icon-style"></ion-icon>
                      </span>
                      <span class="hidden-on-mobile"> Edit SO </span>
                    </ion-button>
                  </ion-buttons>&nbsp;
                  <ion-buttons>
                    <ion-button style="margin-right: 10px" color="danger" (click)="delete(d.id)" fill="outline">
                      <span class="visible-on-mobile-only">
                        <ion-icon name="trash" class="icon-style"></ion-icon>
                      </span>
                      <span class="hidden-on-mobile"> Delete </span>
                    </ion-button>
                  </ion-buttons>

                </ion-col>
              </ion-row>
              <ion-row class="header-row total">
                <ion-col size="2.5"> <ion-text>Total</ion-text> </ion-col>
                <ion-col size="1.5">
                  <ion-text>{{previous_balance_total | currency: 'INR'}}</ion-text>
                </ion-col>
                <ion-col size="1">
                  <ion-text>{{bill_amount_total | currency: 'INR'}}</ion-text>
                </ion-col>
                <ion-col size="1">
                  <ion-text>{{receivable_amount_total | currency: 'INR'}}</ion-text>
                </ion-col>
                <ion-col size="1">
                  <ion-text>{{received_amount_total | currency: 'INR'}}</ion-text>
                </ion-col>
                <ion-col size="1">
                  <ion-text>{{current_balance_total | currency: 'INR'}}</ion-text>
                </ion-col>
                <ion-col size="4"> </ion-col>

              </ion-row>
            </ion-grid>
          </div>
        </ion-col>

      </ion-row>
    </ion-grid>
  </ion-grid>


  <!-- Ends : Main  -->
</ion-content>

<ion-footer>
  <ion-fab vertical="bottom" horizontal="start">
    <ion-fab-button (click)="addSalesOrder()">
      <ion-icon name="add"></ion-icon>
    </ion-fab-button>
  </ion-fab>
  <app-floating-menu></app-floating-menu>
</ion-footer>