import { Component, OnInit } from '@angular/core';
import { BluetoothService } from '../shared/services/bluetooth.service';
import { AlertController } from '@ionic/angular';
import { UserApiService } from '../shared/services/user.service';
import { IonLoaderService } from '../shared/services/ion-loader.service';
import { ToastService } from '../shared/services/toast.service';
import { PrintServiceService } from '../shared/services/print-service.service';
import { Router } from '@angular/router';

import { TranslationService, LanguageConfig } from '../shared/services/translation.service';

@Component({
  selector: 'app-settings',
  templateUrl: './settings.page.html',
  styleUrls: ['./settings.page.scss'],
})
export class SettingsPage implements OnInit {
  selectedDevice: any;
  metadata: any = JSON.parse(localStorage.getItem('metadata'));
  component: any = this.metadata.component.filter(item => item.active);
  bill_type: any = this.metadata.bill_type.filter(item => item.active);
  paperType: any = this.bill_type.find(item => item.selected).type;
  paperSize: any = this.bill_type.find(item => item.selected)?.size?.find(item => item.selected)?.size;
  modeOfPayment: any = this.metadata.modeOfPayment || [];
  componentSorting: any = this.metadata.componentSorting || false;

  // Print Configuration
  upiId: string = '';
  companyCode: string = '';

  // Language Configuration
  availableLanguages: LanguageConfig[] = [];
  currentLanguage: string = '';

  constructor(
    public bluetoothService: BluetoothService,
    private alertController: AlertController,
    private userApiService: UserApiService,
    private toast: ToastService,
    public ionLoaderService: IonLoaderService,
    private printerService: PrintServiceService,
    private router: Router,

    public translationService: TranslationService
  ) {
    this.selectedDevice = this.bluetoothService.getSelectedDevice();
  }

  ngOnInit() {
    this.loadPrintConfiguration();
    this.loadLanguageConfiguration();
  }

  loadPrintConfiguration() {
    this.upiId = this.printerService.getUpiConfiguration() || '';
    this.companyCode = this.printerService.getCompanyCode() || '';
  }

  loadLanguageConfiguration() {
    this.availableLanguages = this.translationService.getAvailableLanguages();
    
    // Check if language preference exists in metadata
    if (this.metadata?.userPreferences?.language) {
      // Apply language from metadata
      this.translationService.setLanguage(this.metadata.userPreferences.language);
      this.currentLanguage = this.metadata.userPreferences.language;
    } else {
      // Use current language (default or from localStorage)
      this.currentLanguage = this.translationService.getCurrentLanguageCode();
    }
  }
  ionViewWillEnter() {
  }

  async selectDevice() {
    this.bluetoothService.getList();
  }

  scanPrinter() {
    this.bluetoothService.scanForPrinters().then(() => {
      console.log('Device requested');
    });
  }

  connectToPrinter() {
    // After device selection, connect to the device
    this.bluetoothService.requestDevice().then(() => {
      this.bluetoothService.connectToDevice();
    });
  }

  async selectPaperSize() {
    let activeSizes = this.bill_type.find(item => item.type === this.paperType).size.filter(item => item.active)
    const alertSizes = activeSizes.map(item => ({
      name: item.name,
      type: 'radio',
      label: item.name,
      value: item.size,
      checked: false
    }));
    const alert = await this.alertController.create({
      header: 'Select a Printer Size',
      inputs: alertSizes,
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {
            console.log('Confirm Cancel');
          },
        },
        {
          text: 'Ok',
          handler: (paper_size) => {
            localStorage.setItem('paper_size', paper_size);
            this.paperSize = localStorage.getItem('paper_size');
            // Deselect all items
            this.bill_type.filter(item => item.selected === true).forEach(item => item.selected = false);

            // Find the item by `type` and set it selected
            let paperType = this.bill_type.find(item => item.type === this.paperType);
            console.log(paperType);

            if (paperType) {
              paperType.selected = true;
              paperType.size.forEach(item => item.selected = false);
              // Find the item by `name` and set it selected
              let paperSize = paperType.size.find(item => item.size === this.paperSize);
              if (paperSize) {
                paperSize.selected = true;
              }
            }

            // Call updateMetadata function to update
            this.updateMetadata();
          },
        },
      ],
    });

    await alert.present();
  }
  async selectPaperSettings() {
    const alertTypes = this.bill_type.filter(item => item.active).map(item => ({
      name: item.type,
      type: 'radio',
      label: item.type,
      value: item.type,
      checked: false
    }));
    const alert = await this.alertController.create({
      header: 'Select a Printer Type',
      inputs: alertTypes,
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {
            console.log('Confirm Cancel');
          },
        },
        {
          text: 'Ok',
          handler: (paper_type) => {
            localStorage.setItem('paper_type', paper_type);
            this.paperType = localStorage.getItem('paper_type');
            this.selectPaperSize();
          },
        },
      ],
    });

    await alert.present();
  }
  // Update the sort order of items
  async updateSortOrder() {
    this.component.sort((a, b) => a.sort_order - b.sort_order);
    this.metadata.component = this.component;
    this.updateMetadata();
  }
  toggleActive(item: any) {
    this.component.active = !this.component.active;
    this.metadata.component = this.component;
    this.updateMetadata();
  }

  async updateMetadata() {
    localStorage.setItem('metadata', JSON.stringify(this.metadata))
    await this.ionLoaderService.startLoader().then(async () => {
      await this.userApiService.updateMetadata(this.metadata).subscribe(async (res: any) => {
        if (res.success) {
          this.toast.toastServices(res.message, 'success', 'top');
        }
        else {
          this.toast.toastServices(res.message, 'danger', 'top')
        }
        this.ionLoaderService.dismissLoader();
      });
    });
  }

  // Move item up in the order
  moveUp(item: any) {
    if (item.sort_order > 1) {
      const prevItem = this.component.find(i => i.sort_order === item.sort_order - 1);
      if (prevItem) prevItem.sort_order++;
      item.sort_order--;
      this.updateSortOrder();
    }
  }

  // Move item down in the order
  moveDown(item: any) {
    if (item.sort_order < this.component.length) {
      const nextItem = this.component.find(i => i.sort_order === item.sort_order + 1);
      if (nextItem) nextItem.sort_order--;
      item.sort_order++;
      this.updateSortOrder();
    }
  }
  async addModeOfPayment() {

    const alert = await this.alertController.create({
      header: 'Enter Mode Of Payment',
      inputs: [
        {
          name: 'mode_of_payment',
          type: 'text',
          placeholder: 'Enter your mode of payment here...',
          attributes: {
            rows: 5, // Optional: Defines the visible height of the textarea
            maxlength: 500, // Optional: Limits the input characters
          },
        },
      ],
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel',
          handler: () => {
            console.log('User canceled');
          },
        },
        {
          text: 'Submit',
          handler: (data) => {
            let mode_of_payment = {
              "name":data.mode_of_payment,
              "slug":this.generateSlug(data.mode_of_payment),
            }
            this.modeOfPayment.push(mode_of_payment);
            this.metadata.modeOfPayment = this.modeOfPayment;
            this.updateMetadata();
          },
        },
      ],
    });

    await alert.present();


  }
  async modifyModeOfPayment(mode_payment, idx) {

    const alert = await this.alertController.create({
      header: 'Enter Mode Of Payment',
      inputs: [
        {
          name: 'mode_of_payment',
          type: 'text',
          placeholder: 'Enter your mode of payment here...',
          value: mode_payment,
          attributes: {
            rows: 5, // Optional: Defines the visible height of the textarea
            maxlength: 500, // Optional: Limits the input characters
          },
        },
      ],
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel',
          handler: () => {
            console.log('User canceled');
          },
        },
        {
          text: 'Submit',
          handler: (data) => {
            let mode_of_payment = {
              "name":data.mode_of_payment,
              "slug":this.generateSlug(data.mode_of_payment),
            };
            this.modeOfPayment[idx] = mode_of_payment;
            this.metadata.modeOfPayment = this.modeOfPayment;
            this.updateMetadata();
          },
        },
      ],
    });

    await alert.present();


  }
  deleteModeOfPayment(idx){
    this.modeOfPayment.splice(idx,1)
    this.metadata.modeOfPayment = this.modeOfPayment;
    this.updateMetadata();
  }
  generateSlug(value: string): string {
    return value
      .toLowerCase()
      .trim()
      .replace(/[\s\W-]+/g, '-') // Replace spaces and non-alphanumeric characters with hyphens
      .replace(/^-+|-+$/g, '');  // Remove leading and trailing hyphens
  }

  // Print Configuration Methods
  async configureUpi() {
    const alert = await this.alertController.create({
      header: 'Configure UPI Settings',
      message: 'Enter your UPI ID to enable QR code payments on bills',
      inputs: [
        {
          name: 'upiId',
          type: 'text',
          placeholder: 'yourname@upi',
          value: this.upiId
        }
      ],
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Save',
          handler: (data) => {
            try {
              if (data.upiId && data.upiId.trim()) {
                const upiId = data.upiId.trim();
                // Basic UPI ID validation (contains @ symbol)
                if (upiId.includes('@')) {
                  this.printerService.setUpiConfiguration(upiId);
                  this.upiId = upiId;
                  this.toast.toastServices('UPI configuration saved successfully', 'success', 'top');
                } else {
                  this.toast.toastServices('Please enter a valid UPI ID (e.g., yourname@upi)', 'danger', 'top');
                  return false;
                }
              } else {
                // Allow clearing the UPI ID
                this.printerService.setUpiConfiguration('');
                this.upiId = '';
                this.toast.toastServices('UPI configuration cleared', 'success', 'top');
              }
            } catch (error) {
              console.error('Error saving UPI configuration:', error);
              this.toast.toastServices('Error saving configuration. Please try again.', 'danger', 'top');
              return false;
            }
          }
        }
      ]
    });

    await alert.present();
  }

  async configureBarcode() {
    const alert = await this.alertController.create({
      header: 'Configure Barcode Settings',
      message: 'Enter your company code for barcode generation (e.g., ABC, XYZ)',
      inputs: [
        {
          name: 'companyCode',
          type: 'text',
          placeholder: 'ABC',
          value: this.companyCode,
          attributes: {
            maxlength: 5
          }
        }
      ],
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Save',
          handler: (data) => {
            try {
              if (data.companyCode && data.companyCode.trim()) {
                const code = data.companyCode.trim().toUpperCase();
                if (code.length <= 5 && /^[A-Z0-9]+$/.test(code)) {
                  this.printerService.setCompanyCode(code);
                  this.companyCode = code;
                  this.toast.toastServices('Barcode configuration saved successfully', 'success', 'top');
                } else if (code.length > 5) {
                  this.toast.toastServices('Company code must be 5 characters or less', 'danger', 'top');
                  return false;
                } else {
                  this.toast.toastServices('Company code must contain only letters and numbers', 'danger', 'top');
                  return false;
                }
              } else {
                // Allow clearing the company code
                this.printerService.setCompanyCode('');
                this.companyCode = '';
                this.toast.toastServices('Barcode configuration cleared', 'success', 'top');
              }
            } catch (error) {
              console.error('Error saving barcode configuration:', error);
              this.toast.toastServices('Error saving configuration. Please try again.', 'danger', 'top');
              return false;
            }
          }
        }
      ]
    });

    await alert.present();
  }



  // Language Methods
  async selectLanguage() {
    const languageOptions = this.availableLanguages.map(lang => ({
      name: 'language',
      type: 'radio' as const,
      label: `${lang.nativeName} (${lang.name})`,
      value: lang.code,
      checked: this.currentLanguage === lang.code
    }));

    const alert = await this.alertController.create({
      header: 'Select Language / மொழியைத் தேர்ந்தெடுக்கவும்',
      message: 'Choose your preferred language for the application',
      inputs: languageOptions,
      buttons: [
        {
          text: 'Cancel / ரத்து',
          role: 'cancel'
        },
        {
          text: 'Apply / பயன்படுத்து',
          handler: (selectedLanguage) => {
            if (selectedLanguage && selectedLanguage !== this.currentLanguage) {
              this.changeLanguage(selectedLanguage);
            }
          }
        }
      ]
    });

    await alert.present();
  }

  async changeLanguage(languageCode: string) {
    try {
      // Update the language in TranslationService
      this.translationService.setLanguage(languageCode);
      this.currentLanguage = languageCode;
      
      // Update metadata with the new language preference
      if (!this.metadata.userPreferences) {
        this.metadata.userPreferences = {};
      }
      this.metadata.userPreferences.language = languageCode;
      
             // Save to backend using existing updateMetadata method
       await this.updateMetadata();
      
      // Show success message in the new language
      const successMessage = this.translationService.instant('messages.languageChanged');
      this.toast.toastServices(
        successMessage || 'Language changed successfully / மொழி வெற்றிகரமாக மாற்றப்பட்டது', 
        'success', 
        'top'
      );
    } catch (error) {
      console.error('Error updating language preference:', error);
      this.toast.toastServices('Error saving language preference', 'danger', 'top');
    }
  }

  getCurrentLanguageName(): string {
    const language = this.availableLanguages.find(lang => lang.code === this.currentLanguage);
    return language ? language.nativeName : 'English';
  }

  // Navigation Methods
  navigateToRouteManagement() {
    this.router.navigate(['/route-management']);
  }
}
