/**
 * Purchase Rate Calculator Test Suite
 *
 * This file contains test cases to verify the purchase rate calculator
 * formula implementation and edge cases for the updated logic.
 */

interface CalculatorInputs {
  totalPurchaseAmount: number;
  orderQuantity: number;
  unitContains: number;
  crateContains: number;
  gstIncluded: boolean;
  taxPercent: number;
}

interface CalculatorResults {
  finalTotalAmount: number;
  taxAmount: number;
  prRate: number;
}

/**
 * Purchase Rate Calculator Logic
 * Implements the same calculation logic as the component
 */
class PurchaseRateCalculator {

  static calculate(inputs: CalculatorInputs): CalculatorResults {
    let finalTotalAmount: number;
    let taxAmount: number;

    // Step 1: Determine final total purchase amount
    if (inputs.gstIncluded) {
      // GST is already included in the total purchase amount
      finalTotalAmount = inputs.totalPurchaseAmount;
      taxAmount = finalTotalAmount * (inputs.taxPercent / (100 + inputs.taxPercent));
    } else {
      // GST needs to be added to the total purchase amount
      taxAmount = inputs.totalPurchaseAmount * (inputs.taxPercent / 100);
      finalTotalAmount = inputs.totalPurchaseAmount + taxAmount;
    }

    // Step 2: Calculate pr_rate using the required formula
    // pr_rate = total_purchase_amount / (order_quantity × unit_contains × crate_contains)
    const totalUnits = inputs.orderQuantity * inputs.unitContains * inputs.crateContains;
    const prRate = totalUnits > 0 ? finalTotalAmount / totalUnits : 0;

    return {
      finalTotalAmount: parseFloat(finalTotalAmount.toFixed(2)),
      taxAmount: parseFloat(taxAmount.toFixed(2)),
      prRate: parseFloat(prRate.toFixed(2))
    };
  }
}

/**
 * Test Cases for Purchase Rate Calculator
 */
export class PurchaseCalculatorTests {
  
  static runAllTests(): boolean {
    console.log('🧪 Running Purchase Rate Calculator Tests (Updated Logic)...\n');

    const tests = [
      this.testBasicCalculationNoTax,
      this.testWithTaxNotIncluded,
      this.testWithTaxIncluded,
      this.testComplexCalculation,
      this.testZeroValues,
      this.testEdgeCases
    ];

    let passedTests = 0;
    let totalTests = tests.length;

    for (const test of tests) {
      try {
        if (test()) {
          passedTests++;
        }
      } catch (error) {
        console.error(`❌ Test failed with error: ${error.message}`);
      }
    }

    console.log(`\n📊 Test Results: ${passedTests}/${totalTests} tests passed`);

    if (passedTests === totalTests) {
      console.log('✅ All tests passed! Calculator implementation is correct.');
      return true;
    } else {
      console.log('❌ Some tests failed. Please review the implementation.');
      return false;
    }
  }
  
  static testBasicCalculationNoTax(): boolean {
    console.log('🔍 Test 1: Basic Calculation (No tax)');

    const inputs: CalculatorInputs = {
      totalPurchaseAmount: 1000,
      orderQuantity: 2,
      unitContains: 5,
      crateContains: 1,
      gstIncluded: false,
      taxPercent: 0
    };

    const expected = {
      finalTotalAmount: 1000, // 1000 + 0 tax = 1000
      taxAmount: 0,           // 1000 × 0% = 0
      prRate: 100             // 1000 ÷ (2 × 5 × 1) = 100
    };

    const result = PurchaseRateCalculator.calculate(inputs);

    return this.assertResults(result, expected, 'Basic Calculation No Tax');
  }

  static testWithTaxNotIncluded(): boolean {
    console.log('🔍 Test 2: Calculation with Tax Not Included');

    const inputs: CalculatorInputs = {
      totalPurchaseAmount: 1000,
      orderQuantity: 2,
      unitContains: 5,
      crateContains: 1,
      gstIncluded: false,
      taxPercent: 18
    };

    const expected = {
      finalTotalAmount: 1180, // 1000 + (1000 × 18%) = 1180
      taxAmount: 180,         // 1000 × 18% = 180
      prRate: 118             // 1180 ÷ (2 × 5 × 1) = 118
    };

    const result = PurchaseRateCalculator.calculate(inputs);

    return this.assertResults(result, expected, 'With Tax Not Included');
  }

  static testWithTaxIncluded(): boolean {
    console.log('🔍 Test 3: Calculation with Tax Included');

    const inputs: CalculatorInputs = {
      totalPurchaseAmount: 1180,
      orderQuantity: 2,
      unitContains: 5,
      crateContains: 1,
      gstIncluded: true,
      taxPercent: 18
    };

    const expected = {
      finalTotalAmount: 1180,   // 1180 (GST already included)
      taxAmount: 180,           // 1180 × (18 ÷ (100 + 18)) = 180
      prRate: 118               // 1180 ÷ (2 × 5 × 1) = 118
    };

    const result = PurchaseRateCalculator.calculate(inputs);

    return this.assertResults(result, expected, 'With Tax Included');
  }
  
  static testComplexCalculation(): boolean {
    console.log('🔍 Test 4: Complex Calculation (Multiple units and crates)');

    const inputs: CalculatorInputs = {
      totalPurchaseAmount: 5000,
      orderQuantity: 10,
      unitContains: 12,
      crateContains: 2,
      gstIncluded: false,
      taxPercent: 12
    };

    const expected = {
      finalTotalAmount: 5600,  // 5000 + (5000 × 12%) = 5600
      taxAmount: 600,          // 5000 × 12% = 600
      prRate: 23.33            // 5600 ÷ (10 × 12 × 2) = 23.33
    };

    const result = PurchaseRateCalculator.calculate(inputs);

    return this.assertResults(result, expected, 'Complex Calculation');
  }

  static testZeroValues(): boolean {
    console.log('🔍 Test 5: Zero Values');

    const inputs: CalculatorInputs = {
      totalPurchaseAmount: 0,
      orderQuantity: 1,
      unitContains: 1,
      crateContains: 1,
      gstIncluded: false,
      taxPercent: 0
    };

    const expected = {
      finalTotalAmount: 0,
      taxAmount: 0,
      prRate: 0
    };

    const result = PurchaseRateCalculator.calculate(inputs);

    return this.assertResults(result, expected, 'Zero Values');
  }

  static testEdgeCases(): boolean {
    console.log('🔍 Test 6: Edge Cases (Decimal values)');

    const inputs: CalculatorInputs = {
      totalPurchaseAmount: 999.99,
      orderQuantity: 1.5,
      unitContains: 2.5,
      crateContains: 1.2,
      gstIncluded: true,
      taxPercent: 8.25
    };

    // Total units = 1.5 × 2.5 × 1.2 = 4.5
    // Tax amount = 999.99 × (8.25 ÷ (100 + 8.25)) = 76.15
    // PR Rate = 999.99 ÷ 4.5 = 222.22
    const expected = {
      finalTotalAmount: 999.99,
      taxAmount: 76.15,
      prRate: 222.22
    };

    const result = PurchaseRateCalculator.calculate(inputs);

    return this.assertResults(result, expected, 'Edge Cases');
  }
  
  private static assertResults(actual: CalculatorResults, expected: CalculatorResults, testName: string): boolean {
    const tolerance = 0.01; // Allow small floating point differences

    const finalTotalMatch = Math.abs(actual.finalTotalAmount - expected.finalTotalAmount) < tolerance;
    const taxMatch = Math.abs(actual.taxAmount - expected.taxAmount) < tolerance;
    const prRateMatch = Math.abs(actual.prRate - expected.prRate) < tolerance;

    if (finalTotalMatch && taxMatch && prRateMatch) {
      console.log(`✅ ${testName}: PASSED`);
      console.log(`   Final Total Amount: ${actual.finalTotalAmount} (expected: ${expected.finalTotalAmount})`);
      console.log(`   Tax Amount: ${actual.taxAmount} (expected: ${expected.taxAmount})`);
      console.log(`   PR Rate: ${actual.prRate} (expected: ${expected.prRate})\n`);
      return true;
    } else {
      console.log(`❌ ${testName}: FAILED`);
      console.log(`   Final Total Amount: ${actual.finalTotalAmount} (expected: ${expected.finalTotalAmount}) ${finalTotalMatch ? '✓' : '✗'}`);
      console.log(`   Tax Amount: ${actual.taxAmount} (expected: ${expected.taxAmount}) ${taxMatch ? '✓' : '✗'}`);
      console.log(`   PR Rate: ${actual.prRate} (expected: ${expected.prRate}) ${prRateMatch ? '✓' : '✗'}\n`);
      return false;
    }
  }
}

// Export for use in browser console or testing framework
if (typeof window !== 'undefined') {
  (window as any).PurchaseCalculatorTests = PurchaseCalculatorTests;
  console.log('💡 Purchase Calculator Tests loaded. Run PurchaseCalculatorTests.runAllTests() to test the calculator.');
}
