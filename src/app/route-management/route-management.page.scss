// Summary Section
.summary-section {
  padding: 16px;
  background: var(--ion-color-light);
}

.summary-card {
  margin: 0;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  &.active {
    background: linear-gradient(135deg, var(--ion-color-success) 0%, var(--ion-color-success-shade) 100%);
    color: white;

    .summary-icon, .summary-text h3, .summary-text p {
      color: white;
    }
  }

  &.inactive {
    background: linear-gradient(135deg, var(--ion-color-warning) 0%, var(--ion-color-warning-shade) 100%);
    color: white;

    .summary-icon, .summary-text h3, .summary-text p {
      color: white;
    }
  }
}

.summary-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.summary-icon {
  font-size: 24px;
  color: var(--ion-color-primary);
}

.summary-text {
  h3 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--ion-color-dark);
  }

  p {
    margin: 0;
    font-size: 12px;
    color: var(--ion-color-medium);
  }
}

// Routes Section
.routes-section {
  padding: 16px;
}

.route-card {
  margin-bottom: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.route-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.route-info {
  flex: 1;

  ion-card-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--ion-color-dark);
    margin-bottom: 8px;
  }

  ion-card-subtitle {
    margin: 0;
  }
}

.route-actions {
  display: flex;
  gap: 4px;

  ion-button {
    --padding-start: 8px;
    --padding-end: 8px;
    height: 32px;
    width: 32px;
  }
}

// Weekdays Grid
.weekdays-grid {
  display: flex;
  gap: 8px;
  margin-top: 12px;
  flex-wrap: wrap;
}

.weekday-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: var(--ion-color-light);
  border: 2px solid var(--ion-color-light-shade);

  &.active {
    background: var(--ion-color-primary);
    border-color: var(--ion-color-primary);
    color: white;
  }

  .weekday-short {
    font-size: 12px;
    font-weight: 600;
  }
}

// Empty State
.empty-state {
  text-align: center;
  padding: 40px 20px;

  .empty-icon {
    font-size: 64px;
    color: var(--ion-color-medium);
    margin-bottom: 16px;
  }

  h3 {
    color: var(--ion-color-dark);
    margin-bottom: 8px;
  }

  p {
    color: var(--ion-color-medium);
    margin-bottom: 24px;
  }
}

// Modal Styles
.modal-content {
  padding: 16px;
}

.section {
  margin-bottom: 24px;

  h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--ion-color-dark);
  }
}

.weekdays-selection {
  ion-item {
    --padding-start: 0;
    --inner-padding-end: 0;
    margin-bottom: 8px;

    ion-label {
      margin-left: 12px;
    }
  }
}