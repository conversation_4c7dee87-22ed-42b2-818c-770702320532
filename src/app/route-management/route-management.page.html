<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button defaultHref="/tabs/settings"></ion-back-button>
    </ion-buttons>
    <ion-title>Route Management</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="refreshData()">
        <ion-icon name="refresh-outline"></ion-icon>
      </ion-button>
      <ion-button (click)="openCreateModal()">
        <ion-icon name="add-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <!-- Summary Cards -->
  <div class="summary-section">
    <ion-grid>
      <ion-row>
        <ion-col size="6" size-md="3">
          <ion-card class="summary-card">
            <ion-card-content>
              <div class="summary-content">
                <ion-icon name="map-outline" class="summary-icon"></ion-icon>
                <div class="summary-text">
                  <h3>{{summary.total_routes}}</h3>
                  <p>Total Routes</p>
                </div>
              </div>
            </ion-card-content>
          </ion-card>
        </ion-col>

        <ion-col size="6" size-md="3">
          <ion-card class="summary-card active">
            <ion-card-content>
              <div class="summary-content">
                <ion-icon name="checkmark-circle-outline" class="summary-icon"></ion-icon>
                <div class="summary-text">
                  <h3>{{summary.active_routes}}</h3>
                  <p>Active Routes</p>
                </div>
              </div>
            </ion-card-content>
          </ion-card>
        </ion-col>

        <ion-col size="6" size-md="3">
          <ion-card class="summary-card inactive">
            <ion-card-content>
              <div class="summary-content">
                <ion-icon name="close-circle-outline" class="summary-icon"></ion-icon>
                <div class="summary-text">
                  <h3>{{summary.inactive_routes}}</h3>
                  <p>Inactive Routes</p>
                </div>
              </div>
            </ion-card-content>
          </ion-card>
        </ion-col>

        <ion-col size="6" size-md="3">
          <ion-card class="summary-card">
            <ion-card-content>
              <div class="summary-content">
                <ion-icon name="calendar-outline" class="summary-icon"></ion-icon>
                <div class="summary-text">
                  <h3>{{getWeekdayCount()}}</h3>
                  <p>Weekdays</p>
                </div>
              </div>
            </ion-card-content>
          </ion-card>
        </ion-col>
      </ion-row>
    </ion-grid>
  </div>

  <!-- Routes List -->
  <div class="routes-section">
    <ion-card *ngFor="let route of routes" class="route-card">
      <ion-card-header>
        <div class="route-header">
          <div class="route-info">
            <ion-card-title>{{route.name}}</ion-card-title>
            <ion-card-subtitle>
              <ion-chip [color]="getRouteStatusColor(route)" size="small">
                <ion-icon name="calendar-outline"></ion-icon>
                <ion-label>{{getActiveDaysText(route)}}</ion-label>
              </ion-chip>
            </ion-card-subtitle>
          </div>
          <div class="route-actions">
            <ion-button fill="clear" size="small" (click)="openScheduleModal(route)">
              <ion-icon name="calendar-outline"></ion-icon>
            </ion-button>
            <ion-button fill="clear" size="small" (click)="openEditModal(route)">
              <ion-icon name="create-outline"></ion-icon>
            </ion-button>
            <ion-button fill="clear" size="small" color="danger" (click)="deleteRoute(route)">
              <ion-icon name="trash-outline"></ion-icon>
            </ion-button>
          </div>
        </div>
      </ion-card-header>

      <ion-card-content *ngIf="route.active_days.length > 0">
        <div class="weekdays-grid">
          <div
            *ngFor="let option of weekdayOptions"
            class="weekday-item"
            [class.active]="route.active_days.includes(option.value)">
            <span class="weekday-short">{{option.label.substring(0, 3)}}</span>
          </div>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Empty State -->
    <div class="empty-state" *ngIf="routes.length === 0">
      <ion-icon name="map-outline" class="empty-icon"></ion-icon>
      <h3>No Routes Found</h3>
      <p>Create your first route to get started</p>
      <ion-button (click)="openCreateModal()">
        <ion-icon name="add-outline" slot="start"></ion-icon>
        Create Route
      </ion-button>
    </div>
  </div>

  <!-- Create Route Modal -->
  <ion-modal [isOpen]="isCreateModalOpen" (didDismiss)="closeCreateModal()">
    <ng-template>
      <ion-header>
        <ion-toolbar>
          <ion-title>Create New Route</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="closeCreateModal()">
              <ion-icon name="close"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>

      <ion-content>
        <div class="modal-content">
          <ion-item>
            <ion-label position="stacked">Route Name</ion-label>
            <ion-input
              [(ngModel)]="newRouteName"
              placeholder="Enter route name"
              type="text">
            </ion-input>
          </ion-item>
        </div>
      </ion-content>

      <ion-footer>
        <ion-toolbar>
          <ion-buttons slot="end">
            <ion-button fill="clear" (click)="closeCreateModal()">Cancel</ion-button>
            <ion-button fill="solid" (click)="createRoute()" [disabled]="!newRouteName.trim()">
              Create Route
            </ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-footer>
    </ng-template>
  </ion-modal>

  <!-- Edit Route Modal -->
  <ion-modal [isOpen]="isEditModalOpen" (didDismiss)="closeEditModal()">
    <ng-template>
      <ion-header>
        <ion-toolbar>
          <ion-title>Edit Route</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="closeEditModal()">
              <ion-icon name="close"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>

      <ion-content>
        <div class="modal-content" *ngIf="editRoute">
          <ion-item>
            <ion-label position="stacked">Route Name</ion-label>
            <ion-input
              [(ngModel)]="editRoute.name"
              placeholder="Enter route name"
              type="text">
            </ion-input>
          </ion-item>
        </div>
      </ion-content>

      <ion-footer>
        <ion-toolbar>
          <ion-buttons slot="end">
            <ion-button fill="clear" (click)="closeEditModal()">Cancel</ion-button>
            <ion-button fill="solid" (click)="updateRoute()" [disabled]="!editRoute?.name?.trim()">
              Update Route
            </ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-footer>
    </ng-template>
  </ion-modal>

  <!-- Schedule Modal -->
  <ion-modal [isOpen]="isScheduleModalOpen" (didDismiss)="closeScheduleModal()">
    <ng-template>
      <ion-header>
        <ion-toolbar>
          <ion-title>Manage Schedule - {{selectedRouteForSchedule?.name}}</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="closeScheduleModal()">
              <ion-icon name="close"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>

      <ion-content>
        <div class="modal-content">
          <!-- Weekdays Selection -->
          <div class="section">
            <h3>Select Active Days</h3>
            <div class="weekdays-selection">
              <ion-item *ngFor="let option of weekdayOptions">
                <ion-checkbox
                  slot="start"
                  [checked]="option.checked"
                  (ionChange)="onWeekdayChange($event, option.value)">
                </ion-checkbox>
                <ion-label>{{option.label}}</ion-label>
              </ion-item>
            </div>
          </div>

          <!-- Expected Billing Time -->
          <div class="section">
            <ion-item>
              <ion-label position="stacked">Expected Billing Time (Optional)</ion-label>
              <ion-input
                [(ngModel)]="expectedBillingTime"
                placeholder="e.g., 10:00 AM"
                type="text">
              </ion-input>
            </ion-item>
          </div>

          <!-- Notes -->
          <div class="section">
            <ion-item>
              <ion-label position="stacked">Notes (Optional)</ion-label>
              <ion-textarea
                [(ngModel)]="scheduleNotes"
                placeholder="Add any notes about this route schedule"
                rows="3">
              </ion-textarea>
            </ion-item>
          </div>
        </div>
      </ion-content>

      <ion-footer>
        <ion-toolbar>
          <ion-buttons slot="end">
            <ion-button fill="clear" (click)="closeScheduleModal()">Cancel</ion-button>
            <ion-button fill="solid" (click)="saveSchedule()" [disabled]="selectedWeekdays.length === 0">
              Save Schedule
            </ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-footer>
    </ng-template>
  </ion-modal>
</ion-content>
