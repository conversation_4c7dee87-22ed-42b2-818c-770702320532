import { Component, OnInit } from '@angular/core';
import { ModalController, AlertController } from '@ionic/angular';
import { RouteManagementService, RouteWithSchedules } from '../shared/services/route-management.service';
import { ToastService } from '../shared/services/toast.service';
import { IonLoaderService } from '../shared/services/ion-loader.service';

@Component({
  selector: 'app-route-management',
  templateUrl: './route-management.page.html',
  styleUrls: ['./route-management.page.scss'],
})
export class RouteManagementPage implements OnInit {
  routes: RouteWithSchedules[] = [];

  // Modal states
  isCreateModalOpen = false;
  isEditModalOpen = false;
  isScheduleModalOpen = false;

  // Form data
  newRouteName = '';
  editRoute: any = null;
  selectedRouteForSchedule: any = null;

  // Schedule form data
  selectedWeekdays: string[] = [];
  expectedBillingTime = '';
  scheduleNotes = '';

  // Weekday options
  weekdayOptions = [
    { value: 'monday', label: 'Monday', checked: false },
    { value: 'tuesday', label: 'Tuesday', checked: false },
    { value: 'wednesday', label: 'Wednesday', checked: false },
    { value: 'thursday', label: 'Thursday', checked: false },
    { value: 'friday', label: 'Friday', checked: false },
    { value: 'saturday', label: 'Saturday', checked: false },
    { value: 'sunday', label: 'Sunday', checked: false }
  ];

  // Summary data
  summary = {
    total_routes: 0,
    active_routes: 0,
    inactive_routes: 0,
    weekday_counts: {}
  };

  constructor(
    private routeManagementService: RouteManagementService,
    private toastService: ToastService,
    private ionLoaderService: IonLoaderService,
    private modalController: ModalController,
    private alertController: AlertController
  ) { }

  ngOnInit() {
    this.loadData();
  }

  async loadData() {
    await this.ionLoaderService.startLoader();
    try {
      await Promise.all([
        this.loadRoutes(),
        this.loadSummary()
      ]);
    } catch (error) {
      console.error('Error loading data:', error);
      this.toastService.toastServices('Error loading data', 'danger', 'top');
    } finally {
      this.ionLoaderService.dismissLoader();
    }
  }

  async loadRoutes() {
    try {
      this.routes = await this.routeManagementService.getRoutesWithSchedules();
    } catch (error) {
      console.error('Error loading routes:', error);
      this.toastService.toastServices('Error loading routes', 'danger', 'top');
    }
  }

  async loadSummary() {
    try {
      this.summary = await this.routeManagementService.getRouteSummary();
    } catch (error) {
      console.error('Error loading summary:', error);
    }
  }

  // Create Route Methods
  openCreateModal() {
    this.newRouteName = '';
    this.isCreateModalOpen = true;
  }

  closeCreateModal() {
    this.isCreateModalOpen = false;
  }

  async createRoute() {
    if (!this.newRouteName.trim()) {
      this.toastService.toastServices('Please enter a route name', 'warning', 'top');
      return;
    }

    await this.ionLoaderService.startLoader();
    try {
      const response = await this.routeManagementService.createRoute(this.newRouteName.trim());
      if (response.success) {
        this.toastService.toastServices('Route created successfully', 'success', 'top');
        this.closeCreateModal();
        await this.loadData();
      } else {
        this.toastService.toastServices(response.message, 'danger', 'top');
      }
    } catch (error) {
      console.error('Error creating route:', error);
      this.toastService.toastServices('Error creating route', 'danger', 'top');
    } finally {
      this.ionLoaderService.dismissLoader();
    }
  }

  // Edit Route Methods
  openEditModal(route: RouteWithSchedules) {
    this.editRoute = { ...route };
    this.isEditModalOpen = true;
  }

  closeEditModal() {
    this.isEditModalOpen = false;
    this.editRoute = null;
  }

  async updateRoute() {
    if (!this.editRoute.name.trim()) {
      this.toastService.toastServices('Please enter a route name', 'warning', 'top');
      return;
    }

    await this.ionLoaderService.startLoader();
    try {
      const response = await this.routeManagementService.updateRoute(this.editRoute.id, this.editRoute.name.trim());
      if (response.success) {
        this.toastService.toastServices('Route updated successfully', 'success', 'top');
        this.closeEditModal();
        await this.loadData();
      } else {
        this.toastService.toastServices(response.message, 'danger', 'top');
      }
    } catch (error) {
      console.error('Error updating route:', error);
      this.toastService.toastServices('Error updating route', 'danger', 'top');
    } finally {
      this.ionLoaderService.dismissLoader();
    }
  }

  // Delete Route Methods
  async deleteRoute(route: RouteWithSchedules) {
    const alert = await this.alertController.create({
      header: 'Confirm Delete',
      message: `Are you sure you want to delete route "${route.name}"? This action cannot be undone.`,
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Delete',
          role: 'destructive',
          handler: async () => {
            await this.performDeleteRoute(route.id);
          }
        }
      ]
    });

    await alert.present();
  }

  async performDeleteRoute(routeId: number) {
    await this.ionLoaderService.startLoader();
    try {
      const response = await this.routeManagementService.deleteRoute(routeId);
      if (response.success) {
        this.toastService.toastServices('Route deleted successfully', 'success', 'top');
        await this.loadData();
      } else {
        this.toastService.toastServices(response.message, 'danger', 'top');
      }
    } catch (error) {
      console.error('Error deleting route:', error);
      this.toastService.toastServices('Error deleting route', 'danger', 'top');
    } finally {
      this.ionLoaderService.dismissLoader();
    }
  }

  // Schedule Management Methods
  openScheduleModal(route: RouteWithSchedules) {
    this.selectedRouteForSchedule = route;
    this.selectedWeekdays = [...route.active_days];
    this.expectedBillingTime = '';
    this.scheduleNotes = '';

    // Reset weekday options
    this.weekdayOptions.forEach(option => {
      option.checked = this.selectedWeekdays.includes(option.value);
    });

    this.isScheduleModalOpen = true;
  }

  closeScheduleModal() {
    this.isScheduleModalOpen = false;
    this.selectedRouteForSchedule = null;
    this.selectedWeekdays = [];
    this.expectedBillingTime = '';
    this.scheduleNotes = '';
  }

  onWeekdayChange(event: any, weekday: string) {
    if (event.detail.checked) {
      if (!this.selectedWeekdays.includes(weekday)) {
        this.selectedWeekdays.push(weekday);
      }
    } else {
      this.selectedWeekdays = this.selectedWeekdays.filter(day => day !== weekday);
    }
  }

  async saveSchedule() {
    if (this.selectedWeekdays.length === 0) {
      this.toastService.toastServices('Please select at least one weekday', 'warning', 'top');
      return;
    }

    await this.ionLoaderService.startLoader();
    try {
      const response = await this.routeManagementService.saveRouteSchedules(
        this.selectedRouteForSchedule.id,
        this.selectedWeekdays,
        this.expectedBillingTime,
        this.scheduleNotes
      );

      if (response.success) {
        this.toastService.toastServices('Schedule saved successfully', 'success', 'top');
        this.closeScheduleModal();
        await this.loadData();
      } else {
        this.toastService.toastServices(response.message, 'danger', 'top');
      }
    } catch (error) {
      console.error('Error saving schedule:', error);
      this.toastService.toastServices('Error saving schedule', 'danger', 'top');
    } finally {
      this.ionLoaderService.dismissLoader();
    }
  }

  // Utility Methods
  getWeekdayLabel(weekday: string): string {
    const option = this.weekdayOptions.find(opt => opt.value === weekday);
    return option ? option.label : weekday;
  }

  getActiveDaysText(route: RouteWithSchedules): string {
    if (route.active_days.length === 0) {
      return 'No active days';
    }

    if (route.active_days.length === 7) {
      return 'All days';
    }

    return route.active_days.map(day => this.getWeekdayLabel(day)).join(', ');
  }

  getRouteStatusColor(route: RouteWithSchedules): string {
    if (route.active_days.length === 0) {
      return 'danger';
    } else if (route.active_days.length >= 5) {
      return 'success';
    } else {
      return 'warning';
    }
  }

  async refreshData() {
    await this.loadData();
  }

  // Helper method for template
  getWeekdayCount(): number {
    return this.summary.weekday_counts ? Object.keys(this.summary.weekday_counts).length : 0;
  }

}