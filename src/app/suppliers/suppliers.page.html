<app-header [title]="'Suppliers'" [returnUrl]="'tabs/home'"></app-header>

<ion-content class="suppliers-content">
  <!-- Collapsible Summary Section - Moved to Top -->
  <div class="summary-section" *ngIf="displayData && displayData.length > 0">
    <div class="summary-header" (click)="toggleSummary()">
      <h3 class="section-title">
        <ion-icon name="analytics-outline" class="section-icon"></ion-icon>
        Suppliers Summary
      </h3>
      <ion-button fill="clear" class="toggle-button">
        <ion-icon [name]="showSummary ? 'chevron-up' : 'chevron-down'" class="toggle-icon"></ion-icon>
      </ion-button>
    </div>
    <div class="summary-content" [class.expanded]="showSummary">

    <ion-row>
      <ion-col size="6">
        <ion-card class="summary-card total-card">
          <ion-card-content>
            <div class="summary-content">
              <div class="summary-info">
                <h3>Total Suppliers</h3>
                <h1>{{getTotalSuppliers()}}</h1>
                <p>Registered suppliers</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="business" class="summary-icon total"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6">
        <ion-card class="summary-card active-card">
          <ion-card-content>
            <div class="summary-content">
              <div class="summary-info">
                <h3>Active Suppliers</h3>
                <h1>{{getActiveSuppliers()}}</h1>
                <p>Currently active</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="checkmark-circle" class="summary-icon active"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col size="6">
        <ion-card class="summary-card outstanding-card">
          <ion-card-content>
            <div class="summary-content">
              <div class="summary-info">
                <h3>Total Outstanding</h3>
                <h1>{{getTotalOutstanding() | currency: 'INR':'symbol':'1.0-0'}}</h1>
                <p>Pending payments</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="card" class="summary-icon outstanding"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6">
        <ion-card class="summary-card average-card">
          <ion-card-content>
            <div class="summary-content">
              <div class="summary-info">
                <h3>Avg Balance</h3>
                <h1>{{getAverageBalance() | currency: 'INR':'symbol':'1.0-0'}}</h1>
                <p>Average per supplier</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="trending-up" class="summary-icon average"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
    </div>
  </div>

  <!-- Collapsible Search Section -->
  <div class="search-section">
    <div class="search-header" (click)="toggleSearch()">
      <h3 class="section-title">
        <ion-icon name="search-outline" class="section-icon"></ion-icon>
        Search & Options
      </h3>
      <ion-button fill="clear" class="toggle-button">
        <ion-icon [name]="showSearch ? 'chevron-up' : 'chevron-down'" class="toggle-icon"></ion-icon>
      </ion-button>
    </div>

    <div class="search-content" [class.expanded]="showSearch">
      <ion-grid>
        <ion-row>
          <ion-col size="12">
            <ion-searchbar
              (ionInput)="filterItems($event)"
              showCancelButton="focus"
              placeholder="Search suppliers..."
              class="custom-searchbar">
            </ion-searchbar>
          </ion-col>
          <!-- Import/Export Button -->
          <ion-col size="6">
          </ion-col>
          <ion-col size="6">
            <ion-button fill="outline" expand="block" class="filter-button" (click)="openImportExportModal()">
              <ion-label>Import/Export</ion-label>
              <ion-icon name="cloud-upload-outline" slot="start"></ion-icon>
            </ion-button>
          </ion-col>
        </ion-row>
      </ion-grid>
    </div>
  </div>

  <!-- No Data Message -->
  <ion-item lines="none" *ngIf="filterEmpty" class="no-data-item">
    <ion-icon name="search-outline" slot="start" color="medium"></ion-icon>
    <ion-text color="medium">
      <h6>No suppliers found for your search.</h6>
    </ion-text>
  </ion-item>

  <!-- Suppliers List -->
  <div class="suppliers-list">
    <ion-item
      *ngFor="let supplier of displayData"
      class="supplier-item"
      lines="none">

      <!-- Supplier Icon -->
      <div class="supplier-icon" slot="start">
        <ion-icon name="business-outline" class="business-icon"></ion-icon>
      </div>

      <!-- Supplier Details -->
      <ion-label class="supplier-details">
        <h2 class="supplier-name">{{supplier.name}}</h2>

        <!-- Location and Contact -->
        <div class="supplier-meta">
          <div class="location-info">
            <ion-icon name="location-outline" class="meta-icon"></ion-icon>
            <span class="location-text">{{supplier.place}}</span>
          </div>
          <div class="contact-info">
            <ion-icon name="call-outline" class="meta-icon"></ion-icon>
            <span class="contact-text">{{supplier.phone_no}}</span>
          </div>
        </div>

        <!-- Contact Person and Sort Order -->
        <div class="supplier-details-row">
          <span class="supplier-detail" *ngIf="supplier.contact_person">Contact: {{supplier.contact_person}}</span>
          <span class="supplier-detail">Order: {{supplier.sort_order}}</span>
        </div>

        <!-- Balance Information -->
        <div class="balance-details">
          <div class="balance-row">
            <span class="balance-label">Current Balance:</span>
            <span class="balance-value" [ngClass]="{
              'balance-positive': supplier.current_balance > 0,
              'balance-zero': supplier.current_balance === 0,
              'balance-negative': supplier.current_balance < 0
            }">
              {{supplier.current_balance | currency: 'INR':'symbol':'1.0-0'}}
            </span>
          </div>
        </div>

        <!-- Status -->
        <div class="status-row">
          <ion-badge [color]="supplier.active ? 'success' : 'medium'" class="status-badge">
            {{supplier.active ? 'Active' : 'Inactive'}}
          </ion-badge>
        </div>
      </ion-label>

      <!-- Actions -->
      <div class="supplier-actions" slot="end">
        <ion-button
          fill="clear"
          size="small"
          color="secondary"
          class="action-button brands-button"
          (click)="manageBrands(supplier)"
          title="Manage Brands">
          <ion-icon name="pricetag-outline"></ion-icon>
        </ion-button>
        <ion-button
          fill="clear"
          size="small"
          color="warning"
          class="action-button edit-button"
          (click)="edit(supplier)">
          <ion-icon name="create-outline"></ion-icon>
        </ion-button>
        <ion-button
          fill="clear"
          size="small"
          color="danger"
          class="action-button delete-button"
          (click)="delete(supplier.id)">
          <ion-icon name="trash-outline"></ion-icon>
        </ion-button>
        <ion-toggle
          [(ngModel)]="supplier.active"
          (ngModelChange)="editSupplierStatus($event, supplier.id)"
          class="status-toggle">
        </ion-toggle>
      </div>
    </ion-item>
  </div>

  <!-- Add Supplier Modal -->
  <ion-modal [isOpen]="isModalOpen" class="supplier-modal">
    <ng-template>
      <ion-header translucent>
        <ion-toolbar color="primary">
          <ion-title>Add New Supplier</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="setOpen(false)" fill="clear" color="light">
              <ion-icon name="close-outline"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <ion-content class="ion-padding modal-content">
        <form #form="ngForm" (ngSubmit)="addSupplier(form.value)">
          <div class="form-section">
            <h3 class="section-title">Basic Information</h3>

            <ion-item class="form-item">
              <ion-label position="stacked">Name
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input ngModel type="text" name="name" required="true" placeholder="Enter supplier name"></ion-input>
            </ion-item>

            <ion-item class="form-item">
              <ion-label position="stacked">Place
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input ngModel type="text" name="place" required="true" placeholder="Enter location"></ion-input>
            </ion-item>
          </div>

          <div class="form-section">
            <h3 class="section-title">Contact Information</h3>

            <ion-item class="form-item">
              <ion-label position="stacked">Contact Person
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input ngModel type="text" name="contact_person" required="true" placeholder="Enter contact person name"></ion-input>
            </ion-item>

            <ion-item class="form-item">
              <ion-label position="stacked">Phone Number
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input ngModel type="tel" name="phone_no" required="true" placeholder="Enter phone number"></ion-input>
            </ion-item>
          </div>

          <div class="form-section">
            <h3 class="section-title">Additional Details</h3>

            <ion-item class="form-item">
              <ion-label position="stacked">Sort Order
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input ngModel type="number" name="sort_order" required="true" placeholder="Enter sort order"></ion-input>
            </ion-item>
          </div>

          <div class="form-actions">
            <ion-button
              type="submit"
              [disabled]="form.invalid"
              expand="block"
              fill="solid"
              shape="round"
              color="primary"
              class="submit-button">
              <ion-icon name="add-outline" slot="start"></ion-icon>
              Add Supplier
            </ion-button>
          </div>
        </form>
      </ion-content>
    </ng-template>
  </ion-modal>
  <!-- Edit Supplier Modal -->
  <ion-modal [isOpen]="isEditModalOpen" class="supplier-modal">
    <ng-template>
      <ion-header translucent>
        <ion-toolbar color="warning">
          <ion-title>Edit Supplier</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="setEditOpen(false)" fill="clear" color="light">
              <ion-icon name="close-outline"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <ion-content class="ion-padding modal-content">
        <form #form="ngForm" (ngSubmit)="editSupplier(form.value)">
          <input type="hidden" name="id" [(ngModel)]="editData.id" />

          <div class="form-section">
            <h3 class="section-title">Basic Information</h3>

            <ion-item class="form-item">
              <ion-label position="stacked">Name
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input [(ngModel)]="editData.name" type="text" name="name" required="true" placeholder="Enter supplier name"></ion-input>
            </ion-item>

            <ion-item class="form-item">
              <ion-label position="stacked">Place
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input [(ngModel)]="editData.place" type="text" name="place" required="true" placeholder="Enter location"></ion-input>
            </ion-item>
          </div>

          <div class="form-section">
            <h3 class="section-title">Contact Information</h3>

            <ion-item class="form-item">
              <ion-label position="stacked">Contact Person
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input [(ngModel)]="editData.contact_person" type="text" name="contact_person" required="true" placeholder="Enter contact person name"></ion-input>
            </ion-item>

            <ion-item class="form-item">
              <ion-label position="stacked">Phone Number
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input [(ngModel)]="editData.phone_no" type="tel" name="phone_no" required="true" placeholder="Enter phone number"></ion-input>
            </ion-item>
          </div>

          <div class="form-section">
            <h3 class="section-title">Additional Details</h3>

            <ion-item class="form-item">
              <ion-label position="stacked">Sort Order
                <ion-text color="danger">*</ion-text>
              </ion-label>
              <ion-input [(ngModel)]="editData.sort_order" type="number" name="sort_order" required="true" placeholder="Enter sort order"></ion-input>
            </ion-item>
          </div>

          <div class="form-actions">
            <ion-button
              type="submit"
              [disabled]="form.invalid"
              expand="block"
              fill="solid"
              shape="round"
              color="warning"
              class="submit-button">
              <ion-icon name="save-outline" slot="start"></ion-icon>
              Update Supplier
            </ion-button>
          </div>
        </form>
      </ion-content>
    </ng-template>
  </ion-modal>

  <!-- Brand Management Modal -->
  <ion-modal [isOpen]="isBrandModalOpen" class="brand-modal">
    <ng-template>
      <ion-header translucent>
        <ion-toolbar color="secondary">
          <ion-title>Manage Brands - {{selectedSupplierForBrands?.name}}</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="setBrandModalOpen(false)" fill="clear" color="light">
              <ion-icon name="close-outline"></ion-icon>
            </ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <ion-content class="ion-padding brand-modal-content">
        <div class="brand-section">
          <div class="section-header">
            <h3>Select Brands for this Supplier</h3>
            <p>Choose which brands this supplier can provide products for.</p>
          </div>

          <div class="brands-list" *ngIf="brands.length > 0">
            <ion-item
              *ngFor="let brand of brands"
              class="brand-item"
              [class.selected]="isBrandSelected(brand.id)">
              <ion-checkbox
                slot="start"
                [checked]="isBrandSelected(brand.id)"
                (ionChange)="toggleBrand(brand.id)">
              </ion-checkbox>
              <ion-label>
                <h3>{{brand.name}}</h3>
              </ion-label>
            </ion-item>
          </div>

          <div class="no-brands" *ngIf="brands.length === 0">
            <ion-icon name="pricetag-outline" class="no-brands-icon"></ion-icon>
            <h3>No Brands Available</h3>
            <p>Please create some brands first before assigning them to suppliers.</p>
          </div>

          <div class="selected-summary" *ngIf="selectedBrandIds.length > 0">
            <h4>Selected Brands ({{selectedBrandIds.length}}):</h4>
            <div class="selected-brands">
              <ion-chip
                *ngFor="let brandId of selectedBrandIds"
                color="secondary">
                <ion-label>{{getBrandName(brandId)}}</ion-label>
              </ion-chip>
            </div>
          </div>
        </div>

        <div class="brand-actions">
          <ion-button
            expand="block"
            fill="outline"
            (click)="setBrandModalOpen(false)"
            class="cancel-button">
            Cancel
          </ion-button>
          <ion-button
            expand="block"
            (click)="saveBrandChanges()"
            color="secondary"
            class="save-button">
            <ion-icon name="save-outline" slot="start"></ion-icon>
            Save Changes
          </ion-button>
        </div>
      </ion-content>
    </ng-template>
  </ion-modal>

</ion-content>

<ion-footer>
  <ion-fab vertical="bottom" horizontal="start">
    <ion-fab-button (click)="setOpen(true)" color="primary">
      <ion-icon name="add"></ion-icon>
    </ion-fab-button>
  </ion-fab>
  <app-floating-menu></app-floating-menu>
</ion-footer>