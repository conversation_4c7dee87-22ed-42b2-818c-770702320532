import { Component, OnInit } from '@angular/core';
import { Platform, ModalController } from '@ionic/angular';
import { AlertService } from '../shared/services/alert.service';
import { BuyerService } from '../shared/services/buyer.service';
import { IonLoaderService } from '../shared/services/ion-loader.service';
import { SuppliersService } from '../shared/services/supplier.service';
import { ToastService } from '../shared/services/toast.service';
import { ImportExportModalComponent } from '../shared/components/import-export-modal/import-export-modal.component';
import { SupplierBrandService, SupplierBrand } from '../shared/services/supplier-brand.service';
import { ProductService } from '../shared/services/product.service';

@Component({
  selector: 'app-suppliers',
  templateUrl: './suppliers.page.html',
  styleUrls: ['./suppliers.page.scss'],
})
export class SuppliersPage implements OnInit {
  isModalOpen = false;
  isEditModalOpen = false;
  isBrandModalOpen = false;
  data: any;
  editData: any;
  displayData: any;
  filterEmpty: boolean;
  showSearch: boolean = false;
  showSummary: boolean = false;

  // Brand management properties
  selectedSupplierForBrands: any = null;
  brands: any[] = [];
  supplierBrands: SupplierBrand[] = [];
  selectedBrandIds: number[] = [];
  constructor(
    private api: SuppliersService,
    private toast: ToastService,
    public ionLoaderService: IonLoaderService,
    public platform: Platform,
    public alertService: AlertService,
    private modalController: ModalController,
    private supplierBrandService: SupplierBrandService,
    private productService: ProductService
  ) { }


  ngOnInit() {
    this.getData()
  }
  setOpen(isOpen: boolean) {
    this.isModalOpen = isOpen;
  }
  setEditOpen(isOpen: boolean) {
    this.isEditModalOpen = isOpen;
  }
  async getData() {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .getSupplier()
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, 'success', 'top');
            this.data = res.data;
            this.displayData = this.data;
          }
          else {
            this.toast.toastServices(res.message, 'danger', 'top')
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err, 'danger', 'top')
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }
  edit(data) {
    this.editData = data
    this.setEditOpen(true)
  }
  delete(data) {
    this.alertService.alertConfirm('Alert', 'Are you sure you want to delete the supplier !!!', 'yes', 'no').then((res) => {
      if (res) {
        this.deleteSupplier(data)
      }
    })
  }
  async addSupplier(data) {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .saveSupplier(data)
        .then(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.toast.toastServices(res.message, 'success', 'top');
            this.isModalOpen = false;
            this.getData()
          }
          else {
            this.toast.toastServices(res.message, 'danger', 'top')
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err, 'danger', 'top')
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });

  }
  async editSupplier(data) {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api.editSupplier(data).then(async (res: any) => {
        if (res.success) {
          console.log(res);
          this.toast.toastServices(res.message, 'success', 'top');
          this.isEditModalOpen = false;
          this.getData()
        }
        else {
          this.toast.toastServices(res.message, 'danger', 'top')
        }
        this.ionLoaderService.dismissLoader();
      }).catch(async (err) => {
        this.toast.toastServices(err, 'danger', 'top');
        this.ionLoaderService.dismissLoader()
      });
    })
  }
  async deleteSupplier(id) {
    let data = {
      id: id
    }
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api.deleteSupplier(data).then(async (res: any) => {
        if (res.success) {
          console.log(res);
          this.toast.toastServices(res.message, 'success', 'top');
          this.getData()
        }
        else {
          this.toast.toastServices(res.message, 'danger', 'top')
        }
        this.ionLoaderService.dismissLoader();
      }).catch(async (err) => {
        this.toast.toastServices(err, 'danger', 'top');
        this.ionLoaderService.dismissLoader()
      });
    });
  }
  async editSupplierStatus(status, id) {
    let data = {
      id: id,
      active: status
    }
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api.editSupplierStatus(data).then(async (res: any) => {
        if (res.success) {
          console.log(res);
          this.toast.toastServices(res.message, 'success', 'top');
          this.getData()
        }
        else {
          this.toast.toastServices(res.message, 'danger', 'top')
        }
        this.ionLoaderService.dismissLoader();
      }).catch(async (err) => {
        this.toast.toastServices(err, 'danger', 'top');
        this.ionLoaderService.dismissLoader()
      })
    })
  }

  // Search functionality
  filterItems(event) {
    const val = event.target.value;
    if (val.length >= 1) {
      this.displayData = this.data.filter((item: any) => {
        return item.name.toLowerCase().indexOf(val.toLowerCase()) > -1 ||
               item.place.toLowerCase().indexOf(val.toLowerCase()) > -1 ||
               item.contact_person.toLowerCase().indexOf(val.toLowerCase()) > -1 ||
               item.phone_no.toString().indexOf(val) > -1;
      });
      this.displayData.length <= 0 ? (this.filterEmpty = true) : (this.filterEmpty = false);
    } else {
      this.displayData = this.data;
      this.filterEmpty = false;
    }
  }

  toggleSearch() {
    this.showSearch = !this.showSearch;
  }

  toggleSummary() {
    this.showSummary = !this.showSummary;
  }

  // Summary calculation methods
  getTotalSuppliers(): number {
    return this.displayData ? this.displayData.length : 0;
  }

  getActiveSuppliers(): number {
    return this.displayData ? this.displayData.filter(supplier => supplier.active).length : 0;
  }

  getTotalOutstanding(): number {
    if (!this.displayData) return 0;
    return this.displayData.reduce((total, supplier) => {
      return total + (supplier.current_balance > 0 ? supplier.current_balance : 0);
    }, 0);
  }

  getAverageBalance(): number {
    if (!this.displayData || this.displayData.length === 0) return 0;
    const total = this.displayData.reduce((sum, supplier) => sum + supplier.current_balance, 0);
    return total / this.displayData.length;
  }

  // Import/Export Modal Methods
  async openImportExportModal() {
    const modal = await this.modalController.create({
      component: ImportExportModalComponent,
      componentProps: {
        appLabel: 'master',
        modelName: 'supplier'
      },
      cssClass: 'import-export-modal'
    });

    modal.onDidDismiss().then((result) => {
      if (result.data && result.data.imported) {
        // Refresh data after successful import
        this.getData();
      }
    });

    return await modal.present();
  }

  // Brand Management Methods
  setBrandModalOpen(isOpen: boolean) {
    this.isBrandModalOpen = isOpen;
  }

  async manageBrands(supplier: any) {
    this.selectedSupplierForBrands = supplier;
    await this.loadBrandsData();
    this.setBrandModalOpen(true);
  }

  async loadBrandsData() {
    try {
      // Load all brands
      const brandsResponse: any = await this.productService.getBrand();
      if (brandsResponse.success) {
        this.brands = brandsResponse.data;
      }

      // Load supplier brands
      if (this.selectedSupplierForBrands) {
        const supplierBrandsResponse: any = await this.supplierBrandService.getSupplierBrands(this.selectedSupplierForBrands.id).toPromise();
        if (supplierBrandsResponse.success) {
          this.supplierBrands = supplierBrandsResponse.data;
          this.selectedBrandIds = this.supplierBrands.map(sb => sb.brand_id);
        }
      }
    } catch (error) {
      console.error('Error loading brands data:', error);
      this.toast.toastServices('Error loading brands data', 'danger', 'top');
    }
  }

  isBrandSelected(brandId: number): boolean {
    return this.selectedBrandIds.includes(brandId);
  }

  toggleBrand(brandId: number) {
    const index = this.selectedBrandIds.indexOf(brandId);
    if (index > -1) {
      this.selectedBrandIds.splice(index, 1);
    } else {
      this.selectedBrandIds.push(brandId);
    }
  }

  async saveBrandChanges() {
    await this.ionLoaderService.startLoader();

    try {
      const currentBrandIds = this.supplierBrands.map(sb => sb.brand_id);
      const toAdd = this.selectedBrandIds.filter(id => !currentBrandIds.includes(id));
      const toRemove = this.supplierBrands.filter(sb => !this.selectedBrandIds.includes(sb.brand_id));

      // Add new relationships
      if (toAdd.length > 0) {
        await this.supplierBrandService.createBulkSupplierBrands(this.selectedSupplierForBrands.id, toAdd).toPromise();
      }

      // Remove old relationships
      if (toRemove.length > 0) {
        const idsToRemove = toRemove.map(sb => sb.id!);
        await this.supplierBrandService.deleteBulkSupplierBrands(idsToRemove).toPromise();
      }

      this.toast.toastServices('Supplier brands updated successfully', 'success', 'top');
      this.setBrandModalOpen(false);

    } catch (error) {
      console.error('Error saving supplier brands:', error);
      this.toast.toastServices('Error updating supplier brands', 'danger', 'top');
    } finally {
      await this.ionLoaderService.dismissLoader();
    }
  }

  getBrandName(brandId: number): string {
    const brand = this.brands.find(b => b.id === brandId);
    return brand ? brand.name : '';
  }
}
