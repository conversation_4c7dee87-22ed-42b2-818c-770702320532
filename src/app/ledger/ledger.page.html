<app-header [title]="'Ledger'" [returnUrl]="'tabs/home'"></app-header>
<ion-content class="ledger-content">
  <!-- Summary Section -->
  <div class="summary-section">
    <h3 class="section-title">
      <ion-icon name="analytics-outline" class="section-icon"></ion-icon>
      Ledger Summary
    </h3>

    <ion-row>
      <ion-col size="6">
        <ion-card class="summary-card outstanding-card">
          <ion-card-content>
            <div class="summary-content">
              <div class="summary-info">
                <h3>Total Outstanding</h3>
                <h1>{{(displayActiveSegment == 'pay-in' ? balance_amounts : advance_payments) | currency: 'INR':'symbol':'1.0-0'}}</h1>
                <p>Pending collections</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="trending-down" class="summary-icon outstanding"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>

      <ion-col size="6">
        <ion-card class="summary-card advance-card">
          <ion-card-content>
            <div class="summary-content">
              <div class="summary-info">
                <h3>Advance Payment</h3>
                <h1>{{(displayActiveSegment == 'pay-in' ? advance_payments : balance_amounts) | currency: 'INR':'symbol':'1.0-0'}}</h1>
                <p>Advance received</p>
              </div>
              <div class="summary-illustration">
                <ion-icon name="trending-up" class="summary-icon advance"></ion-icon>
              </div>
            </div>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
  </div>
  <!-- Segment Selection -->
  <div class="segment-section">
    <h3 class="section-title">
      <ion-icon name="swap-horizontal-outline" class="section-icon"></ion-icon>
      Transaction Type
    </h3>

    <ion-segment value="pay-in" (ionChange)="viewSegmentChanged($event)" class="custom-segment">
      <ion-segment-button value="pay-in" class="segment-button">
        <ion-icon name="arrow-down-circle-outline"></ion-icon>
        <ion-label>Pay In</ion-label>
      </ion-segment-button>
      <ion-segment-button value="pay-out" class="segment-button">
        <ion-icon name="arrow-up-circle-outline"></ion-icon>
        <ion-label>Pay Out</ion-label>
      </ion-segment-button>
    </ion-segment>
  </div>

  <!-- Collapsible Search Section -->
  <div class="search-section" *ngIf="initialData && buyerView">
    <div class="search-header" (click)="toggleSearch()">
      <h3 class="section-title">
        <ion-icon name="search-outline" class="section-icon"></ion-icon>
        Search {{displayActiveSegment == 'pay-in' ? 'Buyers' : 'Suppliers'}}
      </h3>
      <ion-button fill="clear" class="toggle-button">
        <ion-icon [name]="showSearch ? 'chevron-up' : 'chevron-down'" class="toggle-icon"></ion-icon>
      </ion-button>
    </div>

    <div class="search-content" [class.expanded]="showSearch">
      <ion-searchbar
        placeholder="Filter {{displayActiveSegment == 'pay-in' ? 'Buyers' : 'Suppliers'}}"
        inputmode="text"
        type="text"
        (ionChange)="filterItems($event)"
        [debounce]="450"
        showCancelButton="focus"
        class="custom-searchbar">
      </ion-searchbar>
    </div>
  </div>

  <!-- Buyers/Suppliers List -->
  <div class="entities-section" *ngIf="initialData && buyerView">
    <h3 class="section-title">
      <ion-icon name="people-outline" class="section-icon"></ion-icon>
      {{displayActiveSegment == 'pay-in' ? 'Buyers' : 'Suppliers'}}
    </h3>

    <!-- No Data Message -->
    <ion-card class="no-data-card" *ngIf="filterEmpty">
      <ion-card-content>
        <div class="no-data-content">
          <ion-icon name="search-outline" class="no-data-icon"></ion-icon>
          <h3>No data available</h3>
          <p>No data found for the searched item.</p>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Entities List -->
    <ion-card class="entities-card" *ngIf="!filterEmpty">
      <ion-card-content>
        <div>
          <ion-item
            *ngFor="let buyer of initialData"
            (click)="openBuyerLedger(buyer)"
            lines="none"
            class="entity-item">
            <div class="entity-avatar" slot="start">
              <ion-badge class="avatar-badge" color="primary">{{getInitials(buyer.name)}}</ion-badge>
            </div>
            <ion-label>
              <h3 class="entity-name">{{buyer.name}}</h3>
              <p class="entity-type">{{displayActiveSegment == 'pay-in' ? 'Buyer' : 'Supplier'}}</p>
            </ion-label>
            <div class="entity-balance" slot="end">
              <span class="balance-amount">{{buyer.balance_amount | currency: 'INR':'symbol':'1.0-0'}}</span>
              <ion-icon name="chevron-forward-outline" class="chevron-icon"></ion-icon>
            </div>
          </ion-item>
        </div>

        <ion-infinite-scroll (ionInfinite)="loadLedgers($event)">
          <ion-infinite-scroll-content loadingSpinner="bubbles" loadingText="Loading more ledgers...">
          </ion-infinite-scroll-content>
        </ion-infinite-scroll>
      </ion-card-content>
    </ion-card>
  </div>
  <!-- Ledger Detail View -->
  <div class="ledger-detail-section" *ngIf="ledgerData && ledgerView">
    <h3 class="section-title">
      <ion-icon name="person-circle-outline" class="section-icon"></ion-icon>
      {{selectedBuyer?.name}} Ledger
    </h3>

    <!-- Selected Entity Card -->
    <ion-card class="selected-entity-card">
      <ion-card-content>
        <div class="entity-header">
          <div class="entity-info">
            <ion-badge class="entity-badge" color="primary">{{getInitials(selectedBuyer.name)}}</ion-badge>
            <div class="entity-details">
              <h3 class="entity-name">{{selectedBuyer.name}}</h3>
              <p class="entity-type">{{displayActiveSegment == 'pay-in' ? 'Buyer' : 'Supplier'}}</p>
            </div>
          </div>
          <div class="entity-balance">
            <span class="balance-label">Current Balance</span>
            <span class="balance-amount">{{selectedBuyer?.balance_amount | currency: 'INR':'symbol':'1.2-2'}}</span>
          </div>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Date Filter -->
    <ion-card class="filter-card">
      <ion-card-content>
        <ion-row>
          <ion-col size="5">
            <ion-item lines="none" class="date-item">
              <ion-label position="stacked">From Date</ion-label>
              <ion-input
                [(ngModel)]="from_date"
                [value]="from_date | date : 'YYYY-MM-dd'"
                placeholder="Select start date"
                type="date"
                class="date-input">
              </ion-input>
            </ion-item>
          </ion-col>
          <ion-col size="5">
            <ion-item lines="none" class="date-item">
              <ion-label position="stacked">To Date</ion-label>
              <ion-input
                [(ngModel)]="to_date"
                [value]="to_date | date : 'YYYY-MM-dd'"
                placeholder="Select end date"
                type="date"
                class="date-input">
              </ion-input>
            </ion-item>
          </ion-col>
          <ion-col size="2">
            <ion-button
              (click)="filterList()"
              fill="solid"
              expand="block"
              class="filter-button">
              <ion-icon name="search" slot="icon-only"></ion-icon>
            </ion-button>
          </ion-col>
        </ion-row>
      </ion-card-content>
    </ion-card>
    <!-- Ledger Transactions -->
    <ion-card class="transactions-card">
      <ion-card-header>
        <div class="transactions-header">
          <ion-card-title class="transactions-title">
            <ion-icon name="list-outline" class="transactions-icon"></ion-icon>
            Transaction History
          </ion-card-title>
          <ion-button
            (click)="printStatement()"
            fill="outline"
            size="small"
            class="print-button">
            <ion-icon name="print-outline" slot="start"></ion-icon>
            Print
          </ion-button>
        </div>
      </ion-card-header>

      <ion-card-content>
        <div class="transactions-container">
          <!-- Transaction Cards -->
          <div class="transaction-cards">
            <ion-card
              *ngFor="let ledger of ledgerData"
              class="transaction-card"
              [ngClass]="{'credit-transaction': ledger.entry_type === 'credit', 'debit-transaction': ledger.entry_type === 'debit'}"
              (click)="openLedgerView(ledger)">

              <ion-card-content>
                <div class="transaction-content">
                  <!-- Transaction Header -->
                  <div class="transaction-header">
                    <div class="transaction-type-badge"
                         [ngClass]="{'credit-badge': ledger.entry_type === 'credit', 'debit-badge': ledger.entry_type === 'debit'}">
                      <ion-icon
                        [name]="ledger.entry_type === 'credit' ? 'add-circle' : 'remove-circle'"
                        class="type-icon"></ion-icon>
                      <span class="type-label">{{ledger.entry_type === 'credit' ? 'Credit' : 'Debit'}}</span>
                    </div>

                    <div class="transaction-amount-display">
                      <span class="amount-sign"
                            [ngClass]="{'positive': ledger.entry_type === 'credit', 'negative': ledger.entry_type === 'debit'}">
                        {{ledger.entry_type === 'credit' ? '+' : '-'}}
                      </span>
                      <span class="amount-value"
                            [ngClass]="{'credit-amount': ledger.entry_type === 'credit', 'debit-amount': ledger.entry_type === 'debit'}">
                        {{(ledger.amount + ledger.discount_amount) | currency: 'INR':'symbol':'1.2-2'}}
                      </span>
                    </div>
                  </div>

                  <!-- Transaction Details -->
                  <div class="transaction-body">
                    <div class="date-section">
                      <div class="date-info">
                        <ion-icon name="calendar-outline" class="detail-icon"></ion-icon>
                        <div class="date-details">
                          <span class="date">{{ledger.created_at | date:'dd/MM/yyyy'}}</span>
                          <span class="time">{{ledger.created_at | date:'HH:mm'}}</span>
                        </div>
                      </div>
                    </div>

                    <div class="transaction-meta">
                      <div class="balance-info">
                        <ion-icon name="wallet-outline" class="detail-icon"></ion-icon>
                        <span class="balance-label">Balance:</span>
                        <span class="balance-amount">{{ledger.closing_amount | currency: 'INR':'symbol':'1.2-2'}}</span>
                      </div>

                      <div class="payment-mode-info" *ngIf="ledger.mode_of_payment">
                        <ion-icon name="card-outline" class="detail-icon"></ion-icon>
                        <span class="payment-mode">{{ledger.mode_of_payment}}</span>
                      </div>

                      <div class="discount-info" *ngIf="ledger.discount_amount && ledger.discount_amount > 0">
                        <ion-icon name="pricetag-outline" class="detail-icon"></ion-icon>
                        <span class="discount-label">Discount:</span>
                        <span class="discount-amount">{{ledger.discount_amount | currency: 'INR':'symbol':'1.2-2'}}</span>
                      </div>
                    </div>

                    <div class="remarks-section" *ngIf="ledger.remarks">
                      <ion-icon name="chatbubble-outline" class="detail-icon"></ion-icon>
                      <span class="remarks">{{ledger.remarks}}</span>
                    </div>
                  </div>
                </div>
              </ion-card-content>
            </ion-card>
          </div>

          <!-- No Transactions Message -->
          <div class="no-transactions" *ngIf="!ledgerData || ledgerData.length === 0">
            <ion-icon name="receipt-outline" class="no-transactions-icon"></ion-icon>
            <h3>No Transactions Found</h3>
            <p>No transactions found for the selected date range.</p>
          </div>

          <!-- Infinite Scroll for Ledger Transactions -->
          <ion-infinite-scroll #infiniteScroll (ionInfinite)="loadMoreLedgerTransactions($event)" [disabled]="!ledger_has_more" *ngIf="ledgerData && ledgerData.length > 0">
            <ion-infinite-scroll-content loadingSpinner="bubbles" loadingText="Loading more transactions...">
            </ion-infinite-scroll-content>
          </ion-infinite-scroll>
        </div>
      </ion-card-content>
    </ion-card>
  </div>
  <ion-modal [isOpen]="isViewModalOpen">
    <ng-template>
      <ion-header translucent>
        <ion-toolbar>
          <ion-title>View Ledger</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="setViewOpen(false)">Close</ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <ion-content class="ion-padding modal-content">
        <!-- Transaction Type Header -->
        <div class="modal-transaction-header">
          <div class="modal-type-badge"
               [ngClass]="{'credit-badge': ledgerViewData.entry_type === 'credit', 'debit-badge': ledgerViewData.entry_type === 'debit'}">
            <ion-icon
              [name]="ledgerViewData.entry_type === 'credit' ? 'add-circle' : 'remove-circle'"
              class="modal-type-icon"></ion-icon>
            <span class="modal-type-label">{{ledgerViewData.entry_type === 'credit' ? 'Credit Transaction' : 'Debit Transaction'}}</span>
          </div>

          <div class="modal-amount-display">
            <span class="modal-amount-sign"
                  [ngClass]="{'positive': ledgerViewData.entry_type === 'credit', 'negative': ledgerViewData.entry_type === 'debit'}">
              {{ledgerViewData.entry_type === 'credit' ? '+' : '-'}}
            </span>
            <span class="modal-amount-value"
                  [ngClass]="{'credit-amount': ledgerViewData.entry_type === 'credit', 'debit-amount': ledgerViewData.entry_type === 'debit'}">
              {{(ledgerViewData.amount + ledgerViewData.discount_amount) | currency: 'INR':'symbol':'1.2-2'}}
            </span>
          </div>
        </div>

        <!-- Transaction Details -->
        <div class="modal-details-section">
          <ion-item class="modal-detail-item">
            <ion-icon name="cash-outline" slot="start" class="modal-detail-icon"></ion-icon>
            <ion-label>
              <h3>Base Amount</h3>
              <p>{{ledgerViewData.amount | currency: 'INR':'symbol':'1.2-2'}}</p>
            </ion-label>
          </ion-item>

          <ion-item class="modal-detail-item" *ngIf="ledgerViewData.discount_amount && ledgerViewData.discount_amount > 0">
            <ion-icon name="pricetag-outline" slot="start" class="modal-detail-icon"></ion-icon>
            <ion-label>
              <h3>Discount Amount</h3>
              <p class="discount-text">{{ledgerViewData.discount_amount | currency: 'INR':'symbol':'1.2-2'}}</p>
            </ion-label>
          </ion-item>

          <ion-item class="modal-detail-item">
            <ion-icon name="wallet-outline" slot="start" class="modal-detail-icon"></ion-icon>
            <ion-label>
              <h3>Closing Balance</h3>
              <p class="balance-text">{{ledgerViewData.closing_amount | currency: 'INR':'symbol':'1.2-2'}}</p>
            </ion-label>
          </ion-item>

          <ion-item class="modal-detail-item">
            <ion-icon name="calendar-outline" slot="start" class="modal-detail-icon"></ion-icon>
            <ion-label>
              <h3>Transaction Date</h3>
              <p>{{ledgerViewData.created_at | date:'dd/MM/yyyy HH:mm'}}</p>
            </ion-label>
          </ion-item>

          <ion-item class="modal-detail-item" *ngIf="ledgerViewData.mode_of_payment">
            <ion-icon name="card-outline" slot="start" class="modal-detail-icon"></ion-icon>
            <ion-label>
              <h3>Payment Mode</h3>
              <p>{{ledgerViewData.mode_of_payment}}</p>
            </ion-label>
          </ion-item>
        </div>

        <!-- Remarks Section -->
        <div class="modal-remarks-section" *ngIf="ledgerViewData.remarks">
          <h3 class="remarks-title">
            <ion-icon name="chatbubble-outline" class="remarks-icon"></ion-icon>
            Remarks
          </h3>
          <div class="remarks-content">
            <p>{{ledgerViewData.remarks}}</p>
          </div>
        </div>

        <!-- Attachment Section -->
        <div class="modal-attachment-section" *ngIf="ledgerViewData.ledger_file">
          <h3 class="attachment-title">
            <ion-icon name="attach-outline" class="attachment-icon"></ion-icon>
            Attachment
          </h3>
          <div class="attachment-preview" (click)="openImg()">
            <img src="{{apiUrlClean}}{{ledgerViewData.ledger_file}}" alt="Transaction attachment" class="attachment-image">
            <div class="attachment-overlay">
              <ion-icon name="eye-outline" class="view-icon"></ion-icon>
              <span>Tap to view</span>
            </div>
          </div>
        </div>

        <ion-fab vertical="bottom" horizontal="start" slot="fixed">
          <ion-fab-button  (click)="setEditOpen(true)" >
            <ion-icon name="pencil"></ion-icon>
          </ion-fab-button>
        </ion-fab>
       <!--  <ion-fab vertical="bottom" horizontal="end" slot="fixed">
          <ion-fab-button color="danger" (click)="deleteLedger(ledgerViewData.id)">
            <ion-icon name="trash"></ion-icon>
          </ion-fab-button>
        </ion-fab>-->
      </ion-content>
    </ng-template>
  </ion-modal>
  <ion-modal [isOpen]="isModalOpen">
    <ng-template>
      <ion-header translucent>
        <ion-toolbar>
          <ion-title>Add Ledger</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="setOpen(false)">Close</ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <ion-content class="ion-padding">
        <ion-segment value="pay-in" (ionChange)="segmentChanged($event)">
          <ion-segment-button value="pay-in">
            <ion-label>Pay-In</ion-label>
          </ion-segment-button>
          <ion-segment-button value="pay-out">
            <ion-label>Pay-Out</ion-label>
          </ion-segment-button>
          <ion-segment-button value="party-transfer">
            <ion-label>Party Transfer</ion-label>
          </ion-segment-button>
        </ion-segment>
        <ng-container *ngIf="activeSegment == 'pay-in'">
          <div style="margin: 12px;">
            <ngx-select-dropdown [config]="buyerConfig" [options]="buyerOptions" [(ngModel)]="selectedBuyerOption"
              (change)="onBuyerChange($event)">
            </ngx-select-dropdown>
          </div>
        </ng-container>
        <ng-container *ngIf="activeSegment == 'pay-out'">
          <div style="margin: 12px;">
            <ngx-select-dropdown [config]="supplierConfig" [options]="supplierOptions"
              [(ngModel)]="selectedSupplierOption" (change)="onSupplierChange($event)">
            </ngx-select-dropdown>
          </div>
        </ng-container>
        <ng-container *ngIf="activeSegment == 'party-transfer'">
          <div style="margin: 12px;">
            <ngx-select-dropdown [config]="fromPartyConfig" [options]="partyOptions" [(ngModel)]="selectedFromOption"
              (change)="onFromChange($event)">
            </ngx-select-dropdown>
          </div>
          <div style="margin: 12px;">
            <ngx-select-dropdown [config]="toPartyConfig" [options]="partyOptions" [(ngModel)]="selectedToOption"
              (change)="onToChange($event)">
            </ngx-select-dropdown>
          </div>
        </ng-container>

        <div style="margin: 12px;" *ngIf="activeSegment != 'party-transfer'">
          <ngx-select-dropdown [config]="modeOfPaymentConfig" [options]="modeOfPaymentOptions"
            [(ngModel)]="modeOfPaymentSelectedOption" (change)="onModeOfPaymentOptionChange($event)">
          </ngx-select-dropdown>
        </div>

        <form #form="ngForm" (ngSubmit)="addLedger(form.value)">
          <ion-item>
            <ion-label position="stacked">Amount
              <ion-text color="danger">*</ion-text>
            </ion-label>
            <ion-input ngModel type="number" name="amount" required="true"></ion-input>
          </ion-item>
          <ion-item>
            <ion-label position="stacked">Discount
              <ion-text color="danger">*</ion-text>
            </ion-label>
            <ion-input ngModel type="number" name="discount_amount" required="true"></ion-input>
          </ion-item>
          <ion-item>
            <ion-label position="stacked">Remarks
              <ion-text color="danger">*</ion-text>
            </ion-label>
            <ion-input ngModel type="text" name="remarks" required="true"></ion-input>
          </ion-item>
          <ion-item *ngIf="activeSegment != 'party-transfer'">
            <ion-label position="stacked">Type
              <ion-text color="danger">*</ion-text>
            </ion-label>
            <ion-select ngModel name="entry_type" required="true">
              <ion-select-option value="debit">{{getLedgerName('debit')}}</ion-select-option>
              <ion-select-option value="credit">{{getLedgerName('credit')}}</ion-select-option>
              <!-- Add more options as needed -->
            </ion-select>
          </ion-item>
          <ion-item>
            <ion-label position="stacked">Transaction Image
              <ion-text color="danger">*</ion-text>
            </ion-label>
            
            <!-- Image Preview -->
            <div class="image-preview" *ngIf="previewUrl">
              <img [src]="previewUrl" alt="Transaction preview" class="preview-image">
              <ion-button 
                fill="clear" 
                size="small" 
                (click)="clearImageSelection()"
                class="remove-preview-btn">
                <ion-icon name="close-circle" slot="icon-only"></ion-icon>
              </ion-button>
            </div>

            <!-- Upload Controls -->
            <div class="upload-controls" *ngIf="!previewUrl">
              <ion-button 
                expand="block" 
                fill="outline" 
                class="select-image-btn"
                (click)="presentImageOptions()">
                <ion-icon name="camera-outline" slot="start"></ion-icon>
                Add Transaction Image
              </ion-button>
            </div>
          </ion-item>

          <!-- Hidden File Inputs -->
          <input 
            #fileInput 
            type="file" 
            accept="image/*" 
            (change)="onFileSelected($event)" 
            style="display: none;">
          
          <input 
            #cameraInput 
            type="file" 
            accept="image/*" 
            capture="environment" 
            (change)="onFileSelected($event)" 
            style="display: none;">
          <ion-row>
            <ion-col size="12" class="ion-margin-vertical">
              <ion-button type="submit" [disabled]="form.invalid" expand="block" fill="solid" shape="round">
                Add
              </ion-button>
            </ion-col>
          </ion-row>
        </form>

      </ion-content>
    </ng-template>
  </ion-modal>
  <ion-modal [isOpen]="isEditModalOpen">
    <ng-template>
      <ion-header translucent>
        <ion-toolbar>
          <ion-title>Edit Ledger</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="setEditOpen(false)">Close</ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <ion-content class="ion-padding">
        <form #editForm="ngForm" (ngSubmit)="editLedger(editForm.value)">
          <input  [(ngModel)]="ledgerViewData.id" type="number" name="id" hidden="true">
          <ion-item>
            <ion-label position="stacked">Amount
              <ion-text color="danger">*</ion-text>
            </ion-label>
            <ion-input [(ngModel)]="ledgerViewData.amount" type="number" name="amount" required="true"></ion-input>
          </ion-item>
          <ion-item>
            <ion-label position="stacked">Discount
              <ion-text color="danger">*</ion-text>
            </ion-label>
            <ion-input [(ngModel)]="ledgerViewData.discount_amount" type="number" name="discount_amount"
              required="true"></ion-input>
          </ion-item>
          <ion-item>
            <ion-label position="stacked">Remarks
              <ion-text color="danger">*</ion-text>
            </ion-label>
            <ion-input [(ngModel)]="ledgerViewData.remarks" type="text" name="remarks" required="true"></ion-input>
          </ion-item>
          <ion-item *ngIf="activeSegment != 'party-transfer'">
            <ion-label position="stacked">Type
              <ion-text color="danger">*</ion-text>
            </ion-label>
            <ion-select [(ngModel)]="ledgerViewData.entry_type" name="entry_type" required="true">
              <ion-select-option value="debit">{{getLedgerName('debit')}}</ion-select-option>
              <ion-select-option value="credit">{{getLedgerName('credit')}}</ion-select-option>
              <!-- Add more options as needed -->
            </ion-select>
          </ion-item>
          <ion-row>
            <ion-col size="12" class="ion-margin-vertical">
              <ion-button type="submit" [disabled]="editForm.invalid" expand="block" fill="solid" shape="round">
                Edit
              </ion-button>
            </ion-col>
          </ion-row>
        </form>

      </ion-content>
    </ng-template>
  </ion-modal>
</ion-content>
<ion-footer>
  <ion-fab vertical="bottom" horizontal="start">
    <ion-fab-button (click)="setOpen(true)">
      <ion-icon name="add"></ion-icon>
    </ion-fab-button>
  </ion-fab>
  <app-floating-menu *ngIf="buyerView"></app-floating-menu>
  <div *ngIf="ledgerData && ledgerView">
    <ion-fab vertical="bottom" horizontal="end">
      <ion-button (click)="backToBuyerView()" fill="solid" shape="round">
        Back
      </ion-button>
    </ion-fab>
  </div>
</ion-footer>