import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { IonInfiniteScroll } from '@ionic/angular';
import { ActivatedRoute } from '@angular/router';
import { Platform, ActionSheetController } from '@ionic/angular';
import { AlertService } from '../shared/services/alert.service';
import { IonLoaderService } from '../shared/services/ion-loader.service';
import { RouteService } from '../shared/services/route.service';
import { ToastService } from '../shared/services/toast.service';
import { LedgerService } from '../shared/services/ledger.service';
import { BuyerService } from '../shared/services/buyer.service';
import { environment } from 'src/environments/environment';
import * as moment from 'moment';
import { PrintServiceService } from '../shared/services/print-service.service';
import { UtilService } from '../shared/services/util.service';
import { SuppliersService } from '../shared/services/supplier.service';

@Component({
  selector: 'app-ledger',
  templateUrl: './ledger.page.html',
  styleUrls: ['./ledger.page.scss'],
})
export class LedgerPage implements OnInit {
  @ViewChild('fileInput', { static: false }) fileInput!: ElementRef<HTMLInputElement>;
  @ViewChild('cameraInput', { static: false }) cameraInput!: ElementRef<HTMLInputElement>;

  isModalOpen = false;
  isEditModalOpen = false;
  data: any;
  editData: any;
  initialData: any;
  from_date: any = moment().subtract(30, 'days').format("YYYY-MM-DD");
  to_date: any = moment().format("YYYY-MM-DD");
  ledgerData: any;
  buyerView: boolean = false;
  ledgerView: boolean = false;
  selectedBuyer: any;
  file: any;
  isViewModalOpen = false;
  ledgerViewData: any;
  apiUrlClean: any = environment.apiUrlClean;
  filterEmpty: boolean;
  displayData: any;
  balance_amounts: any;
  advance_payments: any;

  // Camera/File selection properties
  selectedFile: File | null = null;
  previewUrl: string | null = null;
  captureMode: 'camera' | 'gallery' = 'camera';

  buyerConfig = {
    displayKey: 'name', // if objects are passed
    search: true,
    height: '300px',
    placeholder: 'Select a buyer',
    customComparator: () => 0, // custom function to sort options
    limitTo: 5, // options to be displayed in the dropdown
    moreText: 'more', // text to be displayed when more than one option is selected
    noResultsFound: 'No results found!',
    searchPlaceholder: 'Search...',
  };
  selectedBuyerOption: any;
  buyerOptions = [];
  supplierConfig = {
    displayKey: 'name', // if objects are passed
    search: true,
    height: '300px',
    placeholder: 'Select a supplier',
    customComparator: () => 0, // custom function to sort options
    limitTo: 5, // options to be displayed in the dropdown
    moreText: 'more', // text to be displayed when more than one option is selected
    noResultsFound: 'No results found!',
    searchPlaceholder: 'Search...',
  };
  selectedSupplierOption = '';
  supplierOptions = [];
  fromPartyConfig = {
    displayKey: 'name', // if objects are passed
    search: true,
    height: '300px',
    placeholder: 'Select a from party',
    customComparator: () => 0, // custom function to sort options
    limitTo: 5, // options to be displayed in the dropdown
    moreText: 'more', // text to be displayed when more than one option is selected
    noResultsFound: 'No results found!',
    searchPlaceholder: 'Search...',
  };
  selectedFromOption: any;
  partyOptions = [];
  toPartyConfig = {
    displayKey: 'name', // if objects are passed
    search: true,
    height: '300px',
    placeholder: 'Select a to party',
    customComparator: () => 0, // custom function to sort options
    limitTo: 5, // options to be displayed in the dropdown
    moreText: 'more', // text to be displayed when more than one option is selected
    noResultsFound: 'No results found!',
    searchPlaceholder: 'Search...',
  };
  selectedToOption: any;
  modeOfPaymentConfig = {
    displayKey: 'name', // if objects are passed
    search: true,
    height: '300px',
    placeholder: 'Select a Mode of payment',
    customComparator: () => 0, // custom function to sort options
    limitTo: 5, // options to be displayed in the dropdown
    moreText: 'more', // text to be displayed when more than one option is selected
    noResultsFound: 'No results found!',
    searchPlaceholder: 'Search...',
  };
  modeOfPaymentSelectedOption: any;
  modeOfPaymentOptions: any = JSON.parse(localStorage.getItem('metadata')).modeOfPayment;
  ledgerFieldName: any = JSON.parse(localStorage.getItem('metadata')).ledgerFieldName;
  activeSegment: any;
  selectedSupplier: any;
  displayActiveSegment: any;
  page_number: any = 1;
  page_size: any = 30;
  count: any;
  searchText: any;
  showSearch: boolean = false;
  
  // Pagination for ledger transactions
  ledger_page_number: any = 1;
  ledger_page_size: any = 20;
  ledger_count: any;
  ledger_has_more: boolean = true;

  // Reference to infinite scroll component
  @ViewChild('infiniteScroll') infiniteScroll: IonInfiniteScroll;



  constructor(
    private toast: ToastService,
    public ionLoaderService: IonLoaderService,
    public platform: Platform,
    public alertService: AlertService,
    public routerService: RouteService,
    private route: ActivatedRoute,
    private api: LedgerService,
    private buyer_api: BuyerService,
    private supplier_api: SuppliersService,
    private printService: PrintServiceService,
    private util: UtilService,
    private actionSheetController: ActionSheetController,
  ) { }

  ngOnInit() {
  }
  ionViewWillEnter() {
    this.getData()
    this.displayActiveSegment = "pay-in";
  }
  setOpen(isOpen: boolean) {
    this.isModalOpen = isOpen;
    this.activeSegment = "pay-in"
    if (isOpen) {
      this.getBuyerData();
      this.getSupplierData();
    }
  }
  setViewOpen(isOpen: boolean) {
    this.isViewModalOpen = isOpen;
  }
  setEditOpen(isOpen: boolean) {
    this.isViewModalOpen = false;
    this.isEditModalOpen = isOpen;

  }
  openLedgerView(data) {
    this.ledgerViewData = data
    this.setViewOpen(true)
  }
  segmentChanged(ev: any) {
    console.log('Segment changed', ev);
    this.activeSegment = ev.detail.value;
    switch (this.activeSegment) {
      case 'pay-in':
        break;
      case 'pay-out':
        break;
      case 'party-transfer':
        this.partyOptions = [...this.buyerOptions, ...this.supplierOptions]
        console.log(this.partyOptions);

      default:
        break;
    }
  }
  getLedgerName(type) {
    let name = this.ledgerFieldName?.find(item => item.slug == type).displayName
    return name ? name : type;
  }
  async getData() {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api.getInitialLedger(this.displayActiveSegment == 'pay-in' ? 'buyer=true' : '', this.page_number, this.page_size, this.searchText).subscribe(async (res: any) => {
        if (res.success) {
          this.toast.toastServices(res.message, 'success', 'top');
          this.initialData = res.data.data.data;
          this.count = res.data.data.count / this.page_size;
          this.buyerView = true;
          this.advance_payments = res.data.advance_amount
          this.balance_amounts = res.data.pending_amount
        }
        else {
          this.toast.toastServices(res.message, 'danger', 'top')
        }
        this.ionLoaderService.dismissLoader();
      }, err => {
        this.ionLoaderService.dismissLoader();
      });
    });
  }
  async loadLedgers(ev) {
    console.log(ev);
    if (this.page_number < this.count) {
      this.page_number++;
      await this.api
        .getInitialLedger(this.displayActiveSegment == 'pay-in' ? 'buyer=true' : '', this.page_number, this.page_size, this.searchText)
        .subscribe(async (res: any) => {
          if (res.success) {
            console.log(res);
            this.initialData = [...this.initialData, ...res.data.data.data]
            ev.target.complete();
            this.advance_payments = res.data.advance_amount
            this.balance_amounts = res.data.pending_amount
          }
          else {
            ev.target.disabled = true;
          }
        }, async (err) => {
          ev.target.disabled = true;
        });
    } else {
      ev.target.disabled = true;
    }

  }

  async loadMoreLedgerTransactions(ev) {
    console.log('Loading more ledger transactions...');
    if (this.ledger_has_more && this.ledger_page_number <= this.ledger_count) {
      this.ledger_page_number++;
      await this.api
        .getFilterLedger(this.from_date, this.to_date, this.selectedBuyer.id, this.displayActiveSegment, this.ledger_page_number, this.ledger_page_size)
        .subscribe(async (res: any) => {
          if (res.success && res.data && res.data.data) {
            console.log('Loaded more transactions:', res.data.data);
            this.ledgerData = [...this.ledgerData, ...res.data.data];
            ev.target.complete();
            
            // Check if we've loaded all data
            if (res.data.data.length < this.ledger_page_size) {
              this.ledger_has_more = false;
            }
          } else if (res.success && res.data && Array.isArray(res.data)) {
            // Handle legacy response format (no pagination)
            this.ledgerData = [...this.ledgerData, ...res.data];
            ev.target.complete();
            this.ledger_has_more = false;
          } else {
            this.ledger_has_more = false;
            ev.target.complete();
          }
        }, async (err) => {
          console.error('Error loading more transactions:', err);
          this.ledger_has_more = false;
          ev.target.complete();
        });
    } else {
      this.ledger_has_more = false;
      ev.target.complete();
    }
  }
  async getSupplierData() {
    // await this.ionLoaderService.startLoader().then(async () => {
    await this.supplier_api.getSupplier().then(async (res: any) => {
      if (res.success) {
        this.toast.toastServices(res.message, 'success', 'top');
        this.supplierOptions = res.data;
      }
      else {
        this.toast.toastServices(res.message, 'danger', 'top')
      }
      this.ionLoaderService.dismissLoader();
    }, err => {
      this.ionLoaderService.dismissLoader();
    });
    // });
  }
  async getBuyerData() {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.buyer_api.getBuyer().then(async (res: any) => {
        if (res.success) {
          this.toast.toastServices(res.message, 'success', 'top');
          this.buyerOptions = res.data;
        }
        else {
          this.toast.toastServices(res.message, 'danger', 'top')
        }
        this.ionLoaderService.dismissLoader();
      }, err => {
        this.ionLoaderService.dismissLoader();
      });
    });
  }
  onBuyerChange(selected: any) {
    console.log('Selected Option:', selected);
    this.selectedBuyer = selected.value;
  }
  onSupplierChange(selected: any) {
    console.log('Selected Option:', selected);
    this.selectedSupplier = selected.value;
  }
  onFromChange(selected: any) {
    console.log('Selected Option:', selected);
    this.selectedFromOption = selected.value;
  }
  onToChange(selected: any) {
    console.log('Selected Option:', selected);
    this.selectedToOption = selected.value;
  }
  onModeOfPaymentOptionChange(selected: any) {
    console.log('Selected Option:', selected);
    this.modeOfPaymentSelectedOption = selected.value;
  }
  
  async filterList() {
    // Reset pagination for new filter
    this.ledger_page_number = 1;
    this.ledger_has_more = true;
    
    await this.ionLoaderService.startLoader().then(async () => {
      this.api.getFilterLedger(this.from_date, this.to_date, this.selectedBuyer.id, this.displayActiveSegment, this.ledger_page_number, this.ledger_page_size).subscribe((res: any) => {
        console.log(res);
        if (res.success && res.data && res.data.data) {
          this.ledgerData = res.data.data;
          this.ledger_count = Math.ceil(res.data.count / this.ledger_page_size);
          
          // Check if we have more data to load
          if (res.data.data.length < this.ledger_page_size) {
            this.ledger_has_more = false;
          }
        } else if (res.success && res.data && Array.isArray(res.data)) {
          // Handle legacy response format (no pagination)
          this.ledgerData = res.data;
          this.ledger_has_more = false;
        } else {
          this.ledgerData = [];
          this.ledger_has_more = false;
        }

        this.ionLoaderService.dismissLoader();
      }, err => {
        this.ionLoaderService.dismissLoader();
      });
    });
  }
  async openBuyerLedger(buyer) {
    this.selectedBuyer = buyer;
    // Reset pagination for new buyer
    this.ledger_page_number = 1;
    this.ledger_has_more = true;
    
    await this.ionLoaderService.startLoader().then(async () => {
      this.api.getFilterLedger(null, null, buyer.id, this.displayActiveSegment, this.ledger_page_number, this.ledger_page_size).subscribe((res: any) => {
        console.log(res);
        if (res.success && res.data && res.data.data) {
          this.ledgerData = res.data.data;
          this.ledger_count = Math.ceil(res.data.count / this.ledger_page_size);
          
          // Check if we have more data to load
          if (res.data.data.length < this.ledger_page_size) {
            this.ledger_has_more = false;
          }
        } else if (res.success && res.data && Array.isArray(res.data)) {
          // Handle legacy response format (no pagination)
          this.ledgerData = res.data;
          this.ledger_has_more = false;
        } else {
          this.ledgerData = [];
          this.ledger_has_more = false;
        }
        
        this.ledgerView = true;
        this.buyerView = false;

        this.ionLoaderService.dismissLoader();
      }, err => {
        this.ionLoaderService.dismissLoader();
      });
    });
  }
  backToBuyerView() {
    this.ledgerView = false;
    this.selectedBuyer = null;
    this.buyerView = true;
    this.setOpen(false);
    this.setViewOpen(false);
    
    // Reset ledger pagination
    this.ledger_page_number = 1;
    this.ledger_has_more = true;
    this.ledgerData = null;
    
    this.ionLoaderService.dismissLoader();
  }
  getInitials(string) {
    var names = string.split(' '),
      initials = names[0].substring(0, 1).toUpperCase();

    if (names.length > 1) {
      initials += names[names.length - 1].substring(0, 1).toUpperCase();
    }
    return initials;
  }
  async presentImageOptions() {
    const actionSheet = await this.actionSheetController.create({
      header: 'Add Transaction Image',
      cssClass: 'image-options-action-sheet',
      buttons: [
        {
          text: 'Take Photo',
          icon: 'camera-outline',
          handler: () => {
            this.openCamera();
          }
        },
        {
          text: 'Choose from Gallery',
          icon: 'images-outline',
          handler: () => {
            this.openGallery();
          }
        },
        {
          text: 'Cancel',
          icon: 'close-outline',
          role: 'cancel'
        }
      ]
    });

    await actionSheet.present();
  }

  openCamera() {
    this.captureMode = 'camera';
    if (this.cameraInput) {
      this.cameraInput.nativeElement.click();
    }
  }

  openGallery() {
    this.captureMode = 'gallery';
    if (this.fileInput) {
      this.fileInput.nativeElement.click();
    }
  }

  onFileSelected(event: any) {
    const file: File = event.target.files[0];
    if (file) {
      this.handleFileSelection(file);
    }
  }

  handleFileSelection(file: File) {
    // Validate file type
    if (!file.type.startsWith('image/')) {
      this.toast.toastServices('Please select a valid image file', 'danger', 'top');
      return;
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      this.toast.toastServices('Image size should not exceed 10MB', 'danger', 'top');
      return;
    }

    this.selectedFile = file;
    this.file = file;
    
    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      this.previewUrl = e.target?.result as string;
    };
    reader.readAsDataURL(file);

    this.toast.toastServices('Image selected successfully', 'success', 'top');
  }

  clearImageSelection() {
    this.selectedFile = null;
    this.previewUrl = null;
    this.file = null;
    
    // Reset file inputs
    if (this.fileInput) {
      this.fileInput.nativeElement.value = '';
    }
    if (this.cameraInput) {
      this.cameraInput.nativeElement.value = '';
    }
  }

  onFileChange(event: any) {
    // Implement the logic to handle file change event
    const fileList: FileList = event.target.files;
    if (fileList.length > 0) {
      this.file = fileList[0];

      // Handle the selected file, e.g., upload to server or process locally
      console.log(this.file);
    }
  }
  async addLedger(data) {
    let entryMode;
    await this.ionLoaderService.startLoader().then(async () => {
      console.log(this.file);
      const file: File = this.file;
      const body = new FormData();
      body.append('amount', data.amount);
      body.append('discount_amount', data.discount_amount);
      body.append('remarks', data.remarks);
      body.append('entry_type', data.entry_type);
      switch (this.activeSegment) {
        case 'pay-in':
          body.append('party__id', this.selectedBuyer.id);
          entryMode = '?buyer=true'
          break;
        case 'pay-out':
          body.append('party__id', this.selectedSupplier.id);
          entryMode = '?supplier=true'
          break;
        case 'party-transfer':
          if (this.selectedFromOption.id == this.selectedToOption.id) {
            this.toast.toastServices("Both from and to party can\'t be same", "danger", "top")
            return
          }
          body.append('from_party__id', this.selectedFromOption.id);
          body.append('to_party__id', this.selectedToOption.id);
          entryMode = this.selectedFromOption.buyer ? '?party_transfer=true&from=buyer&to=supplier' : '?party_transfer=true&from=supplier&to=buyer'
          break;
        default:
          break;
      }
      body.append('mode_of_payment', this.modeOfPaymentSelectedOption.slug);
      body.append('file', file);
      await this.api
        .saveLedger(body, entryMode)
        .then(async (res: any) => {
          if (res.success) {
            this.toast.toastServices(res.message, 'success', 'top');
            this.isModalOpen = false;
            this.getData();
            this.backToBuyerView();
          }
          else {
            this.toast.toastServices(res.message, 'danger', 'top')
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err, 'danger', 'top')
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });

  }
  async editLedger(data) {
    await this.ionLoaderService.startLoader().then(async () => {
      await this.api
        .editLedger(data)
        .then(async (res: any) => {
          if (res.success) {
            this.toast.toastServices(res.message, 'success', 'top');
            this.isEditModalOpen = false;
            this.getData();
            this.backToBuyerView();
          }
          else {
            this.toast.toastServices(res.message, 'danger', 'top')
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err, 'danger', 'top')
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });
  }

  async deleteLedger(id) {
    await this.ionLoaderService.startLoader().then(async () => {

      await this.api
        .deleteLedger(id)
        .then(async (res: any) => {
          if (res.success) {
            this.toast.toastServices(res.message, 'success', 'top');
            this.getData();
            this.backToBuyerView();
          }
          else {
            this.toast.toastServices(res.message, 'danger', 'top')
          }
          this.ionLoaderService.dismissLoader();
        })
        .catch(async (err) => {
          this.toast.toastServices(err, 'danger', 'top')
          this.ionLoaderService.dismissLoader();
          // console.log(err);
        });
    });

  }
  openImg() {
    window.open(this.apiUrlClean + this.ledgerViewData.ledger_file)
  }
  viewSegmentChanged(ev) {
    this.displayActiveSegment = ev.detail.value;
    this.getData();
    
    // Reset ledger pagination when switching segments
    this.ledger_page_number = 1;
    this.ledger_has_more = true;
    this.ledgerData = null;
  }
  async filterItems(event) {
    this.searchText = event.target.value;
    await this.api
      .getInitialLedger(this.displayActiveSegment == 'pay-in' ? 'buyer=true' : '', this.page_number, this.page_size, this.searchText)
      .subscribe(async (res: any) => {
        if (res.success) {
          console.log(res);
          this.initialData = res.data.data.data
          this.advance_payments = res.data.advance_amount
          this.balance_amounts = res.data.pending_amount
          this.initialData.length <= 0 ? (this.filterEmpty = true) : (this.filterEmpty = false);
        }
      }, async (err) => {

      });
  }

  toggleSearch() {
    this.showSearch = !this.showSearch;
  }
  printStatement() {
    const items = this.ledgerData.map((d, index) => {
      return `
        <tr>
          <td class="name-cell">${d.created_at}</td>
          <td class="debit-amount">${d.entry_type == 'debit' ? '- ₹' + (d.amount + d.discount_amount).toFixed(2) : '-'}</td>
          <td class="credit-amount">${d.entry_type == 'credit' ? '+ ₹' + (d.amount + d.discount_amount).toFixed(2) : '-'}</td>
          <td class="remarks-col">${d.remarks || '-'}</td>
          <td class="amount-cell">₹${d.closing_amount.toFixed(2)}</td>
        </tr>
      `;
    }).join("");

    const totals = this.ledgerData.reduce((acc, transaction) => {
      acc.totalCredit += transaction.entry_type == 'credit' ? transaction.amount : 0;
      acc.totalDebit += transaction.entry_type == 'debit' ? transaction.amount : 0;
      return acc;
    }, { totalCredit: 0, totalDebit: 0 });

    const dateRange = `${this.from_date} to ${this.to_date}`;

    const tableContent = `
      <table>
        <thead>
          <tr>
            <th>Date</th>
            <th>Debit</th>
            <th>Credit</th>
            <th>Remarks</th>
            <th>Balance</th>
          </tr>
        </thead>
        <tbody>
          ${items}
          <tr class="total-row">
            <td><strong>Total</strong></td>
            <td><strong>- ₹${totals.totalDebit.toFixed(2)}</strong></td>
            <td><strong>+ ₹${totals.totalCredit.toFixed(2)}</strong></td>
            <td></td>
            <td><strong>₹${this.selectedBuyer.balance_amount.toFixed(2)}</strong></td>
          </tr>
        </tbody>
      </table>
    `;

    const htmlContent = this.printService.generateEnhancedReportHTML(
      `Ledger Statement - ${this.selectedBuyer.name}`,
      dateRange,
      tableContent,
      `Total Debit: ₹${totals.totalDebit.toFixed(2)} | Total Credit: ₹${totals.totalCredit.toFixed(2)} | Current Balance: ₹${this.selectedBuyer.balance_amount.toFixed(2)}`
    );

    this.printService.printEnhancedReport(htmlContent, `Ledger Statement - ${this.selectedBuyer.name} - ${dateRange}`);
  }
}
