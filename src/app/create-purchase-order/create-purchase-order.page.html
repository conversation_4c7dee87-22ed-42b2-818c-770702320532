<app-header
  [title]="isEditMode ? 'Edit Purchase Order' : 'Create Purchase Order'"
  [returnUrl]="'purchase-order'">
</app-header>

<ion-content class="mobile-po-content">
  <form [formGroup]="purchaseOrderForm" (ngSubmit)="savePurchaseOrder()">

    <!-- Mobile-First Header Section -->
    <div class="mobile-header-section">
      <!-- Supplier Selection -->
      <ion-item class="mobile-item">
        <ion-label position="stacked">Supplier * ({{suppliers.length}} loaded)</ion-label>
        <ion-select
          formControlName="supplier_id"
          placeholder="Select Supplier"
          (ionChange)="onSupplierChange()"
          interface="action-sheet"
          [disabled]="isEditMode">
          <ion-select-option *ngFor="let supplier of suppliers" [value]="supplier.id">
            {{supplier.name}}
          </ion-select-option>
        </ion-select>
      </ion-item>

      <!-- Debug Info -->
      <ion-item *ngIf="suppliers.length === 0" class="debug-item">
        <ion-label color="warning">
          <p>No suppliers loaded. Check console for errors.</p>
        </ion-label>
      </ion-item>

      <!-- Expected Delivery Date -->
      <ion-item class="mobile-item">
        <ion-label position="stacked">Expected Delivery Date</ion-label>
        <ion-input
          formControlName="expected_delivery_date"
          [value]="purchaseOrderForm.get('expected_delivery_date')?.value | date : 'YYYY-MM-dd'"
          placeholder="Select delivery date"
          type="date"
          [min]="getTodayDate()">
        </ion-input>
      </ion-item>

      <!-- Selected Supplier Info -->
      <div class="supplier-info" *ngIf="selectedSupplier">
        <ion-chip color="primary">
          <ion-icon name="business-outline"></ion-icon>
          <ion-label>{{selectedSupplier.name}}</ion-label>
        </ion-chip>
      </div>
    </div>

    <!-- No Supplier Selected Message -->
    <div class="no-supplier-message" *ngIf="!selectedSupplier">
      <ion-icon name="business-outline"></ion-icon>
      <h3>Select a Supplier</h3>
      <p>Please select a supplier above to view available products</p>
    </div>

    <!-- Product Search and Selection -->
    <div class="product-section" *ngIf="selectedSupplier">
      <div class="section-title">
        <ion-icon name="search-outline"></ion-icon>
        <span>Search & Add Products (Brand-filtered)</span>
      </div>

      <!-- Product List with Invoice-style Layout -->
      <div class="product-grid-section" *ngIf="filteredProducts.length > 0">
        <!-- Debug Sync Button (only in edit mode) -->
        <div class="sync-debug-section" *ngIf="isEditMode">
          <ion-button size="small" fill="outline" color="secondary" (click)="forceRefreshSync()">
            <ion-icon name="sync-outline" slot="start"></ion-icon>
            Refresh Sync
          </ion-button>
        </div>

        <ion-grid class="ion-text-center ion-no-padding">
          <ion-grid class="selection ion-no-padding">
            <ion-row class="ion-no-padding">
              <ion-col size="12" class="ion-no-padding">
                <div style="max-width: 100%; overflow-y: scroll">
                  <ion-grid class="ion-no-padding main-content">
                    <!-- Header Row -->
                    <ion-row class="header-row">
                      <ion-col size="8" class="header-col">
                        <ion-label class="header-label">Product</ion-label>
                      </ion-col>
                      <ion-col size="2" class="header-col" *ngIf="!isAllCrateBased()">
                        <ion-label class="header-label">Box</ion-label>
                      </ion-col>
                      <ion-col size="2" class="header-col" *ngIf="!isAllCrateBased()">
                        <ion-label class="header-label">Pieces</ion-label>
                      </ion-col>
                      <ion-col size="4" class="header-col" *ngIf="isAllCrateBased()">
                        <ion-label class="header-label">Crate</ion-label>
                      </ion-col>
                    </ion-row>

                    <!-- Product Rows -->
                    <ion-row *ngFor="let product of displayedProducts; let i = index"
                             class="product-row"
                             [class.has-quantity]="hasQuantity(product)"
                             [class.crate-based]="product.is_crate_based">
                      <!-- Product Info Column -->
                      <ion-col size="8" class="product-col" (click)="handleProductClick($event, product)">
                        <div class="product-info">
                          <div class="product-header">
                            <div class="product-name">{{product.short_code}}</div>
                            <div class="product-badges">
                              <ion-badge color="primary" *ngIf="product.is_crate_based">CRATE</ion-badge>
                              <ion-badge color="success" *ngIf="hasQuantity(product)">SELECTED</ion-badge>
                            </div>
                          </div>
                          <div class="product-details">
                            <span class="product-full-name">{{product.name}}</span>
                            <div class="product-meta">
                              <span class="product-stock" [class.low-stock]="(product.stock_quantity || 0) < 10">
                                <ion-icon name="cube-outline"></ion-icon>
                                Stock: {{product.stock_quantity || 0}}
                              </span>
                              <span class="product-rate">
                                <ion-icon name="pricetag-outline"></ion-icon>
                                ₹{{product.pr_rate || 0}}
                              </span>
                            </div>
                          </div>
                          <div class="product-crate-info" *ngIf="product.is_crate_based">
                            <ion-chip color="secondary" size="small">
                              <ion-icon name="archive-outline"></ion-icon>
                              <ion-label>Crate Size: {{product.crate_size || 0}}</ion-label>
                            </ion-chip>
                          </div>
                          <div class="product-total-qty" *ngIf="product.tempTotalQty > 0">
                            <ion-chip color="tertiary" size="small">
                              <ion-icon name="calculator-outline"></ion-icon>
                              <ion-label>Total: {{product.tempTotalQty}}</ion-label>
                            </ion-chip>
                          </div>
                        </div>
                      </ion-col>

                      <!-- Quantity Inputs for Crate-based Products -->
                      <ion-col size="4" *ngIf="product.is_crate_based" class="input-col">
                        <div class="input-wrapper">
                          <ion-input
                            type="number"
                            placeholder="0"
                            min="0"
                            [(ngModel)]="product.tempCrateQty"
                            (ionBlur)="calculateProductQuantity(product)"
                            (keyup.enter)="onEnterKeyUp($event, product)"
                            [ngModelOptions]="{standalone: true}"
                            [id]="'crate-input-' + product.id"
                            class="quantity-input"
                            [class.has-value]="product.tempCrateQty > 0">
                          </ion-input>
                          <div class="input-label">Crates</div>
                        </div>
                      </ion-col>

                      <!-- Quantity Inputs for Regular Products -->
                      <ion-col size="2" *ngIf="!product.is_crate_based" class="input-col">
                        <div class="input-wrapper">
                          <ion-input
                            type="number"
                            placeholder="0"
                            min="0"
                            [(ngModel)]="product.tempBoxQty"
                            (ionBlur)="calculateProductQuantity(product)"
                            (keyup.enter)="onEnterKeyUp($event, product)"
                            [ngModelOptions]="{standalone: true}"
                            [id]="'box-input-' + product.id"
                            class="quantity-input"
                            [class.has-value]="product.tempBoxQty > 0">
                          </ion-input>
                          <div class="input-label">Box</div>
                        </div>
                      </ion-col>

                      <ion-col size="2" *ngIf="!product.is_crate_based" class="input-col">
                        <div class="input-wrapper">
                          <ion-input
                            type="number"
                            placeholder="0"
                            min="0"
                            [(ngModel)]="product.tempPiecesQty"
                            (ionBlur)="calculateProductQuantity(product)"
                            (keyup.enter)="onEnterKeyUp($event, product)"
                            [ngModelOptions]="{standalone: true}"
                            [id]="'pieces-input-' + product.id"
                            class="quantity-input"
                            [class.has-value]="product.tempPiecesQty > 0">
                          </ion-input>
                          <div class="input-label">Pcs</div>
                        </div>
                      </ion-col>
                    </ion-row>
                  </ion-grid>
                </div>
              </ion-col>
            </ion-row>
          </ion-grid>
        </ion-grid>
      </div>

      <!-- No Products Message -->
      <div class="no-products-message" *ngIf="filteredProducts.length === 0 && products.length === 0">
        <ion-icon name="cube-outline"></ion-icon>
        <p>No products available for this supplier.</p>
        <p><small>Please assign brands to this supplier first.</small></p>
      </div>

      <div class="no-products-message" *ngIf="filteredProducts.length === 0 && products.length > 0 && searchTerm">
        <ion-icon name="search-outline"></ion-icon>
        <p>No products found matching "{{searchTerm}}"</p>
        <p><small>Try a different search term</small></p>
      </div>
    </div>

    <!-- Selected Items Preview Table -->
    <div class="selected-items-preview" *ngIf="itemsArray.length > 0">
      <div class="preview-header">
        <ion-icon name="list-outline"></ion-icon>
        <span>Selected Items ({{itemsArray.length}})</span>
        <ion-button fill="clear" size="small" (click)="clearAllItems()" color="danger">
          <ion-icon name="trash-outline" slot="icon-only"></ion-icon>
        </ion-button>
      </div>

      <div class="preview-table" formArrayName="items">
        <!-- Table Header -->
        <div class="table-header">
          <div class="col-product">Product</div>
          <div class="col-qty">Qty</div>
          <div class="col-rate">Rate</div>
          <div class="col-total">Total</div>
          <div class="col-action">Action</div>
        </div>

        <!-- Table Rows -->
        <div
          class="table-row"
          *ngFor="let item of itemsArray.controls; let i = index"
          [formGroupName]="i">

          <div class="col-product">
            <div class="product-info">
              <span class="product-name">{{item.get('product_name')?.value || 'Item ' + (i + 1)}}</span>
              <span class="product-code">{{getProductCode(item.get('product_id')?.value)}}</span>
            </div>
          </div>

          <div class="col-qty">
            <!-- Editable quantity fields for crate-based products -->
            <div *ngIf="isProductCrateBased(item.get('product_id')?.value)" class="crate-qty-input">
              <ion-input
                type="number"
                placeholder="0"
                min="0"
                [value]="item.get('box_quantity')?.value || 0"
                (ionBlur)="updateItemQuantityFromInput(i, 'crate', $event)"
                class="quantity-input crate-input">
              </ion-input>
              <div class="input-label">Crate</div>
            </div>

            <!-- Editable quantity fields for regular products -->
            <div *ngIf="!isProductCrateBased(item.get('product_id')?.value)" class="regular-qty-inputs">
              <div class="qty-input-group">
                <ion-input
                  type="number"
                  placeholder="0"
                  min="0"
                  [value]="item.get('box_quantity')?.value || 0"
                  (ionBlur)="updateItemQuantityFromInput(i, 'box', $event)"
                  class="quantity-input box-input">
                </ion-input>
                <div class="input-label">Box</div>
              </div>
              <div class="qty-input-group">
                <ion-input
                  type="number"
                  placeholder="0"
                  min="0"
                  [value]="item.get('pieces_quantity')?.value || 0"
                  (ionBlur)="updateItemQuantityFromInput(i, 'pieces', $event)"
                  class="quantity-input pieces-input">
                </ion-input>
                <div class="input-label">Pcs</div>
              </div>
            </div>

            <!-- Total quantity display -->
            <div class="total-qty-display">
              <span class="total-label">Total:</span>
              <span class="total-value">{{item.get('ordered_quantity')?.value || 0}}</span>
            </div>
          </div>

          <div class="col-rate">
            <ion-input
              type="number"
              formControlName="unit_rate"
              placeholder="0.00"
              (ionBlur)="calculateItemTotal(i)"
              class="rate-input">
            </ion-input>
          </div>

          <div class="col-total">
            <span class="total-display">₹{{((item.get('line_total')?.value || 0) + (item.get('tax_amount')?.value || 0)) | number:'1.2-2'}}</span>
          </div>

          <div class="col-action">
            <ion-button
              fill="clear"
              color="danger"
              size="small"
              (click)="removeItem(i)">
              <ion-icon name="close-outline" slot="icon-only"></ion-icon>
            </ion-button>
          </div>
        </div>
      </div>
    </div>





    <!-- Additional Details (Collapsible) -->
    <div class="additional-details" *ngIf="itemsArray.length > 0">
      <ion-item button (click)="showAdditionalDetails = !showAdditionalDetails">
        <ion-label>Additional Details</ion-label>
        <ion-icon [name]="showAdditionalDetails ? 'chevron-up' : 'chevron-down'" slot="end"></ion-icon>
      </ion-item>

      <div class="details-content" *ngIf="showAdditionalDetails">
        <ion-item>
          <ion-label position="stacked">Remarks</ion-label>
          <ion-textarea
            formControlName="remarks"
            placeholder="Enter any remarks"
            rows="2">
          </ion-textarea>
        </ion-item>

        <ion-item>
          <ion-label position="stacked">Terms and Conditions</ion-label>
          <ion-textarea
            formControlName="terms_and_conditions"
            placeholder="Enter terms and conditions"
            rows="2">
          </ion-textarea>
        </ion-item>
      </div>
    </div>
  </form>
</ion-content>

<ion-footer>
  <!-- Search Section -->
  <div class="search-section">
    <ion-searchbar
      placeholder="Search products..."
      [(ngModel)]="searchTerm"
      (ionInput)="filterProducts()"
      [ngModelOptions]="{standalone: true}"
      [debounce]="250"
      showCancelButton="focus">
    </ion-searchbar>

    <!-- Empty State -->
    <div class="empty-state" *ngIf="filteredProducts.length === 0 && products.length > 0 && searchTerm">
      <p>Product not found</p>
      <ion-button size="small" fill="outline" color="primary" (click)="clearSearch()">
        <ion-icon name="refresh" slot="start"></ion-icon>
        Clear Search
      </ion-button>
    </div>
  </div>

  <!-- Sticky Purchase Order Summary Footer -->
  <div class="sticky-summary" *ngIf="itemsArray.length > 0">
    <!-- Order Statistics Row -->
    <ion-row class="summary-row stats-row">
      <ion-col size="4" class="summary-col">
        <div class="summary-item">
          <ion-icon name="list-outline" class="summary-icon"></ion-icon>
          <div class="summary-content">
            <span class="summary-label">Total Items</span>
            <span class="summary-value">{{itemsArray.length}}</span>
          </div>
        </div>
      </ion-col>
      <ion-col size="4" class="summary-col">
        <div class="summary-item">
          <ion-icon name="cube-outline" class="summary-icon"></ion-icon>
          <div class="summary-content">
            <span class="summary-label">Total Qty</span>
            <span class="summary-value">{{getTotalQuantity()}}</span>
          </div>
        </div>
      </ion-col>
      <ion-col size="4" class="summary-col">
        <div class="summary-item">
          <ion-icon name="calculator-outline" class="summary-icon"></ion-icon>
          <div class="summary-content">
            <span class="summary-label">Subtotal</span>
            <span class="summary-value">₹{{totals.totalAmount.toFixed(2)}}</span>
          </div>
        </div>
      </ion-col>
    </ion-row>

    <!-- Financial Summary Row -->
    <ion-row class="summary-row financial-row">
      <ion-col size="4" class="summary-col" *ngIf="totals.taxAmount > 0">
        <div class="summary-item">
          <ion-icon name="receipt-outline" class="summary-icon tax-icon"></ion-icon>
          <div class="summary-content">
            <span class="summary-label">Tax Total</span>
            <span class="summary-value">₹{{totals.taxAmount.toFixed(2)}}</span>
          </div>
        </div>
      </ion-col>
      <ion-col [size]="totals.taxAmount > 0 ? '8' : '12'" class="summary-col">
        <div class="summary-item net-amount">
          <ion-icon name="cash-outline" class="summary-icon net-icon"></ion-icon>
          <div class="summary-content">
            <span class="summary-label">Net Amount</span>
            <span class="summary-value highlight">₹{{totals.netAmount.toFixed(2)}}</span>
          </div>
        </div>
      </ion-col>
    </ion-row>
  </div>

  <!-- Action Buttons -->
  <ion-toolbar>
    <div class="mobile-footer-actions">
      <ion-button
        expand="block"
        fill="outline"
        size="small"
        (click)="saveDraft()"
        [disabled]="!purchaseOrderForm.get('supplier_id')?.value">
        <ion-icon name="save-outline" slot="start"></ion-icon>
        Draft
      </ion-button>

      <ion-button
        expand="block"
        color="primary"
        size="small"
        (click)="savePurchaseOrder()"
        [disabled]="purchaseOrderForm.invalid || itemsArray.length === 0">
        <ion-icon name="checkmark-circle-outline" slot="start"></ion-icon>
        {{isEditMode ? 'Update' : 'Create'}}
      </ion-button>
    </div>
  </ion-toolbar>
</ion-footer>
