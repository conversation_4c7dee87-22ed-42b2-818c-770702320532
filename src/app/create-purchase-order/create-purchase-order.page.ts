import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Nav<PERSON>ontroller, AlertController } from '@ionic/angular';
import { FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { PurchaseOrderService, PurchaseOrder, PurchaseOrderItem } from '../shared/services/purchase-order.service';
import { ProductService } from '../shared/services/product.service';
import { SuppliersService } from '../shared/services/supplier.service';
import { ToastService } from '../shared/services/toast.service';
import { IonLoaderService } from '../shared/services/ion-loader.service';

@Component({
  selector: 'app-create-purchase-order',
  templateUrl: './create-purchase-order.page.html',
  styleUrls: ['./create-purchase-order.page.scss'],
})
export class CreatePurchaseOrderPage implements OnInit {
  purchaseOrderForm: FormGroup;
  isEditMode = false;
  poId: number | null = null;

  // Data arrays
  suppliers: any[] = [];
  products: any[] = [];
  filteredProducts: any[] = [];
  displayedProducts: any[] = []; // For template display to avoid slice issues

  // Form state
  selectedSupplier: any = null;
  searchTerm = '';
  showProductSearch = false;
  showAdditionalDetails = false;

  // Date values
  todayDate: string;

  // Calculations
  totals = {
    totalAmount: 0,
    taxAmount: 0,
    netAmount: 0
  };

  // Loader state management
  private isLoaderActive = false;

  // Flag to prevent infinite loops during sync operations
  private isSyncing = false;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private nav: NavController,
    private alertController: AlertController,
    private purchaseOrderService: PurchaseOrderService,
    private productService: ProductService,
    private supplierService: SuppliersService,
    private toast: ToastService,
    private ionLoaderService: IonLoaderService
  ) {
    try {
      console.log('CreatePurchaseOrderPage constructor called');

      // Initialize date value once to prevent change detection errors
      this.todayDate = new Date().toISOString().split('T')[0]; // Format: YYYY-MM-DD

      this.initializeForm();

      // Initialize arrays to prevent template errors
      this.suppliers = [];
      this.products = [];
      this.filteredProducts = [];
      this.displayedProducts = [];
    } catch (error) {
      console.error('Error in constructor:', error);
    }
  }
  ngOnInit(): void {
    
  }
  ionViewWillEnter() {
    try {
      console.log('CreatePurchaseOrderPage ngOnInit called');
      this.loadInitialData();
      this.isEditMode = false;
      this.checkEditMode();
    } catch (error) {
      console.error('Error in ngOnInit:', error);
      this.toast.toastServices('Error initializing page', 'danger', 'top');
    }
  }
 

  initializeForm() {
    try {
      console.log('Initializing form...');
      this.purchaseOrderForm = this.fb.group({
        supplier_id: ['', Validators.required],
        expected_delivery_date: [this.todayDate], // Set current date as default
        remarks: [''],
        terms_and_conditions: [''],
        items: this.fb.array([])
      });
      console.log('Form initialized successfully with current date:', this.todayDate);
    } catch (error) {
      console.error('Error initializing form:', error);
      // Create a basic form as fallback
      this.purchaseOrderForm = this.fb.group({
        supplier_id: [''],
        expected_delivery_date: [this.todayDate],
        remarks: [''],
        terms_and_conditions: [''],
        items: this.fb.array([])
      });
    }
  }

  async loadInitialData() {
    await this.safeStartLoader();

    try {
      console.log('Loading initial data...');

      // Load suppliers
      console.log('Loading suppliers...');
      const supplierResponse: any = await this.supplierService.getSupplier();
      console.log('Supplier response:', supplierResponse);

      if (supplierResponse && supplierResponse.success) {
        this.suppliers = supplierResponse.data || [];
        console.log('Suppliers loaded:', this.suppliers.length);
        console.log('First supplier sample:', this.suppliers[0]);
        console.log('Suppliers array:', this.suppliers);
      } else {
        console.error('Failed to load suppliers:', supplierResponse);
        this.suppliers = [];
        this.toast.toastServices('Failed to load suppliers', 'warning', 'top');
      }

      // Don't load all products initially - they will be loaded when supplier is selected
      this.products = [];
      this.filteredProducts = [];
      this.displayedProducts = [];
      console.log('Products will be loaded when supplier is selected');

      // If in edit mode, load the purchase order after suppliers are loaded
      if (this.isEditMode && this.poId) {
        console.log('Loading purchase order in edit mode...');
        await this.loadPurchaseOrder(this.poId);
      }
    } catch (error: any) {
      console.error('Error in loadInitialData:', error);
      this.toast.toastServices('Error loading data: ' + (error?.message || 'Unknown error'), 'danger', 'top');
      // Initialize empty arrays to prevent template errors
      this.suppliers = [];
      this.products = [];
      this.filteredProducts = [];
      this.displayedProducts = [];
    } finally {
      await this.safeDismissLoader();
    }
  }

  checkEditMode() {
    const id = this.route.snapshot.queryParams['id'];
    if (id) {
      this.isEditMode = true;
      this.poId = parseInt(id);
      console.log('Edit mode detected, PO ID:', this.poId);
      // Don't load purchase order immediately - wait for suppliers to load first
    }
  }

  async loadPurchaseOrder(poId: number, showLoader: boolean = true) {
    console.log('Loading purchase order:', poId);

    if (showLoader) {
      await this.safeStartLoader();
    }

    try {
      const response: any = await this.purchaseOrderService.getPurchaseOrder(poId).toPromise();
      console.log('Purchase order response:', response);

      if (response.success) {
        const po = response.data;
        console.log('Purchase order data:', po);

        // Ensure suppliers are loaded before setting selected supplier
        if (this.suppliers.length === 0) {
          console.log('Suppliers not loaded yet, loading them first...');
          await this.loadSuppliersOnly();
        }

        // Populate form - use setValue for supplier_id to ensure proper validation
        this.purchaseOrderForm.get('supplier_id')?.setValue(po.supplier);
        this.purchaseOrderForm.patchValue({
          expected_delivery_date: po.expected_delivery_date,
          remarks: po.remarks,
          terms_and_conditions: po.terms_and_conditions
        });

        // Set selected supplier
        console.log('Looking for supplier with ID:', po.supplier);
        console.log('Available suppliers:', this.suppliers.map(s => ({ id: s.id, name: s.name })));
        this.selectedSupplier = this.suppliers.find(s => s.id === po.supplier);
        console.log('Selected supplier:', this.selectedSupplier);

        // If supplier not found, try to find by supplier field (alternative field name)
        if (!this.selectedSupplier && po.supplier) {
          this.selectedSupplier = this.suppliers.find(s => s.id === po.supplier);
          console.log('Selected supplier (alternative lookup):', this.selectedSupplier);
        }

        // Load products for the selected supplier (don't show loader since parent already has one)
        if (this.selectedSupplier) {
          await this.loadProductsBySupplier(this.selectedSupplier.id, false);
        }

        // Populate items after products are loaded
        const itemsArray = this.purchaseOrderForm.get('items') as FormArray;
        itemsArray.clear();

        if (po.items && po.items.length > 0) {
          console.log('Loading items:', po.items);
          po.items.forEach((item: any) => {
            const itemGroup = this.createItemFormGroup(item);
            itemsArray.push(itemGroup);
          });

          // Sync loaded items to product search for bidirectional sync
          this.syncLoadedItemsToProductSearch();
        }

        this.calculateTotals();
        console.log('Purchase order loaded successfully');
      } else {
        console.error('Failed to load purchase order:', response.message);
        this.toast.toastServices(response.message || 'Failed to load purchase order', 'danger', 'top');
        this.nav.back();
      }
    } catch (error: any) {
      console.error('Error loading purchase order:', error);
      this.toast.toastServices('Error loading purchase order: ' + (error?.message || 'Unknown error'), 'danger', 'top');
      this.nav.back();
    } finally {
      if (showLoader) {
        await this.safeDismissLoader();
      }
    }
  }

  get itemsArray(): FormArray {
    return this.purchaseOrderForm.get('items') as FormArray;
  }

  async loadSuppliersOnly() {
    try {
      console.log('Loading suppliers only...');
      const supplierResponse: any = await this.supplierService.getSupplier();

      if (supplierResponse && supplierResponse.success) {
        this.suppliers = supplierResponse.data || [];
        console.log('Suppliers loaded:', this.suppliers.length);
      } else {
        console.error('Failed to load suppliers:', supplierResponse);
        this.suppliers = [];
        throw new Error('Failed to load suppliers');
      }
    } catch (error) {
      console.error('Error loading suppliers:', error);
      throw error;
    }
  }

  createItemFormGroup(item?: PurchaseOrderItem): FormGroup {
    return this.fb.group({
      id: [item?.id || null],
      product_id: [item?.product_id || '', Validators.required],
      product_name: [item?.product_name || ''],
      ordered_quantity: [item?.ordered_quantity || 1, [Validators.required, Validators.min(0.01)]],
      box_quantity: [item?.box_quantity || 0, [Validators.min(0)]],
      pieces_quantity: [item?.pieces_quantity || 0, [Validators.min(0)]],
      unit_rate: [item?.unit_rate || 0, [Validators.required, Validators.min(0)]],
      tax_rate: [item?.tax_rate || 0, [Validators.min(0), Validators.max(100)]],
      line_total: [item?.line_total || 0],
      tax_amount: [item?.tax_amount || 0],
      forecast_quantity: [item?.forecast_quantity || 0],
      current_stock: [item?.current_stock || 0],
      min_stock_threshold: [item?.min_stock_threshold || 0],
      reorder_reason: [item?.reorder_reason || '']
    });
  }

  async onSupplierChange() {
    try {
      console.log('Supplier change triggered');
      const supplierId = this.purchaseOrderForm.get('supplier_id')?.value;
      console.log('Selected supplier ID:', supplierId);

      if (supplierId && this.suppliers && this.suppliers.length > 0) {
        this.selectedSupplier = this.suppliers.find(s => s.id === supplierId);
        console.log('Selected supplier:', this.selectedSupplier);

        // Load products filtered by supplier brands
        await this.loadProductsBySupplier(supplierId, true);
      } else {
        this.selectedSupplier = null;
        this.products = [];
        this.filteredProducts = [];
        this.displayedProducts = [];
        console.log('No supplier selected or suppliers not loaded');
      }
    } catch (error) {
      console.error('Error in onSupplierChange:', error);
      this.selectedSupplier = null;
      this.products = [];
      this.filteredProducts = [];
      this.displayedProducts = [];
    }
  }

  async loadProductsBySupplier(supplierId: number, showLoader: boolean = true) {
    if (showLoader) {
      await this.safeStartLoader();
    }

    try {
      console.log('Loading products for supplier:', supplierId);

      const response: any = await this.purchaseOrderService.getProductsBySupplier(supplierId).toPromise();
      console.log('Products response:', response);

      if (response && response.success) {
        this.products = response.data.products || [];
        this.filteredProducts = [...this.products];
        this.displayedProducts = this.filteredProducts.slice(0, 20);

        console.log(`Loaded ${this.products.length} products for supplier ${supplierId}`);
        console.log('Associated brands:', response.data.brands);

        if (this.products.length === 0) {
          const message = response.data.message || 'No products available for this supplier. Please assign brands to the supplier first.';
          this.toast.toastServices(message, 'warning', 'top');
        } else {
          this.toast.toastServices(`Loaded ${this.products.length} products from supplier's brands`, 'success', 'top');
        }
      } else {
        console.error('Failed to load products:', response);
        this.toast.toastServices(response?.message || 'Failed to load products', 'danger', 'top');
        this.products = [];
        this.filteredProducts = [];
        this.displayedProducts = [];
      }
    } catch (error) {
      console.error('Error loading products by supplier:', error);
      this.toast.toastServices('Error loading products for selected supplier', 'danger', 'top');
      this.products = [];
      this.filteredProducts = [];
      this.displayedProducts = [];
    } finally {
      if (showLoader) {
        await this.safeDismissLoader();
      }
    }
  }

  addItem() {
    this.itemsArray.push(this.createItemFormGroup());
  }

  removeItem(index: number) {
    try {
      // Get the product ID before removing the item for sync purposes
      const itemGroup = this.itemsArray.at(index) as FormGroup;
      const productId = itemGroup?.get('product_id')?.value;

      // Remove the item from the form array
      this.itemsArray.removeAt(index);
      this.calculateTotals();

      // Clear the product search values for bidirectional sync
      if (productId) {
        this.clearProductSearchValues(productId);
      }

      console.log('Item removed and product search values cleared');
    } catch (error) {
      console.error('Error removing item:', error);
      // Still try to remove the item even if sync fails
      this.itemsArray.removeAt(index);
      this.calculateTotals();
    }
  }

  clearAllItems() {
    try {
      // Clear all product search values
      this.itemsArray.controls.forEach((control) => {
        const itemGroup = control as FormGroup;
        const productId = itemGroup.get('product_id')?.value;
        if (productId) {
          this.clearProductSearchValues(productId);
        }
      });

      // Clear the form array
      this.itemsArray.clear();
      this.calculateTotals();
      console.log('All items cleared');
    } catch (error) {
      console.error('Error clearing all items:', error);
    }
  }

  onProductSelect(productId: number, itemIndex: number) {
    try {
      console.log('Product select triggered:', productId, 'for item:', itemIndex);

      if (!productId || !this.products || this.products.length === 0) {
        console.log('No product ID or products not loaded');
        return;
      }

      const product = this.products.find(p => p.id === productId);
      console.log('Found product:', product);

      if (product && itemIndex >= 0 && itemIndex < this.itemsArray.length) {
        const itemGroup = this.itemsArray.at(itemIndex) as FormGroup;

        if (itemGroup) {
          itemGroup.patchValue({
            product_id: product.id,
            product_name: product.name,
            unit_rate: product.pr_rate || 0,
            tax_rate: product.tax_rate || 0,
            current_stock: product.stock_quantity || 0,
            min_stock_threshold: product.min_stock_threshold || 0
          });

          this.calculateItemTotal(itemIndex);
          console.log('Product selected and item updated');
        } else {
          console.error('Item group not found at index:', itemIndex);
        }
      } else {
        console.error('Product not found or invalid item index:', productId, itemIndex);
      }
    } catch (error) {
      console.error('Error in onProductSelect:', error);
      this.toast.toastServices('Error selecting product', 'danger', 'top');
    }
  }

  calculateItemTotal(itemIndex: number, skipSync: boolean = false) {
    try {
      console.log('Calculating item total for index:', itemIndex, 'skipSync:', skipSync);

      if (itemIndex < 0 || itemIndex >= this.itemsArray.length) {
        console.error('Invalid item index:', itemIndex);
        return;
      }

      const itemGroup = this.itemsArray.at(itemIndex) as FormGroup;
      if (!itemGroup) {
        console.error('Item group not found at index:', itemIndex);
        return;
      }

      const productId = itemGroup.get('product_id')?.value;
      const product = this.products.find(p => p.id === productId);

      const boxQuantity = parseFloat(itemGroup.get('box_quantity')?.value) || 0;
      const piecesQuantity = parseFloat(itemGroup.get('pieces_quantity')?.value) || 0;
      const rate = parseFloat(itemGroup.get('unit_rate')?.value) || 0;
      const taxRate = parseFloat(itemGroup.get('tax_rate')?.value) || 0;

      console.log('Calculation values:', {
        boxQuantity,
        piecesQuantity,
        rate,
        taxRate,
        unitContains: product?.unit_contains,
        crateSize: product?.crate_size,
        isCrateBased: product?.is_crate_based,
        productId: productId,
        productName: product?.name
      });

      let lineTotal = 0;
      let totalQuantity = 0;
      let formula = '';

      if (product?.is_crate_based) {
        // For crate-based products: qty * crate_size * unit_contains * rate
        const crateSize = product?.crate_size || 1;
        const unitContains = product?.unit_contains || 1;
        lineTotal = boxQuantity * crateSize * unitContains * rate;
        totalQuantity = boxQuantity * crateSize * unitContains;
        formula = `${boxQuantity} * ${crateSize} * ${unitContains} * ${rate} = ${lineTotal}`;
      } else {
        // For regular products: (box * unit_contains * rate) + (pieces * rate)
        const unitContains = product?.unit_contains || 1;
        lineTotal = (boxQuantity * unitContains * rate) + (piecesQuantity * rate);
        totalQuantity = (boxQuantity * unitContains) + piecesQuantity;
        formula = `(${boxQuantity} * ${unitContains} * ${rate}) + (${piecesQuantity} * ${rate}) = ${lineTotal}`;
      }

      const taxAmount = lineTotal * (taxRate / 100);

      console.log('Calculated values:', {
        lineTotal,
        taxAmount,
        totalQuantity,
        formula: formula
      });

      itemGroup.patchValue({
        ordered_quantity: totalQuantity,
        line_total: lineTotal,
        tax_amount: taxAmount
      });

      // Only sync if not already syncing and skipSync is false
      if (!skipSync && !this.isSyncing) {
        console.log('About to sync to product search:', { productId, boxQuantity, piecesQuantity });
        this.syncItemToProductSearch(productId, boxQuantity, piecesQuantity);
      }

      this.calculateTotals();
    } catch (error) {
      console.error('Error in calculateItemTotal:', error);
      this.toast.toastServices('Error calculating item total', 'danger', 'top');
    }
  }

  calculateTotals() {
    try {
      console.log('Calculating totals...');
      let totalAmount = 0;
      let taxAmount = 0;

      if (this.itemsArray && this.itemsArray.controls) {
        this.itemsArray.controls.forEach((control, index) => {
          try {
            const itemGroup = control as FormGroup;
            const lineTotal = parseFloat(itemGroup.get('line_total')?.value) || 0;
            const itemTaxAmount = parseFloat(itemGroup.get('tax_amount')?.value) || 0;

            totalAmount += lineTotal;
            taxAmount += itemTaxAmount;

            console.log(`Item ${index}: lineTotal=${lineTotal}, taxAmount=${itemTaxAmount}`);
          } catch (itemError) {
            console.error(`Error calculating totals for item ${index}:`, itemError);
          }
        });
      }

      this.totals = {
        totalAmount: totalAmount,
        taxAmount: taxAmount,
        netAmount: totalAmount + taxAmount
      };

      console.log('Calculated totals:', this.totals);
    } catch (error) {
      console.error('Error in calculateTotals:', error);
      // Set default totals to prevent template errors
      this.totals = {
        totalAmount: 0,
        taxAmount: 0,
        netAmount: 0
      };
    }
  }

  // Product search functionality
  filterProducts() {
    if (!this.searchTerm.trim()) {
      this.filteredProducts = [...this.products];
    } else {
      this.filteredProducts = this.products.filter(product =>
        product.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        product.short_code.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    }

    // Update displayed products (limit to 20 for performance)
    this.displayedProducts = this.filteredProducts.slice(0, 20);
    console.log('Filtered products updated:', this.filteredProducts.length, 'Displayed:', this.displayedProducts.length);
  }

  toggleProductSearch() {
    this.showProductSearch = !this.showProductSearch;
    if (this.showProductSearch) {
      this.searchTerm = '';
      this.filteredProducts = [...this.products];
    }
  }

  addProductToItems(product: any) {
    const newItem = this.createItemFormGroup();
    newItem.patchValue({
      product_id: product.id,
      product_name: product.name,
      unit_rate: product.pr_rate || 0,
      tax_rate: product.tax_rate || 0,
      current_stock: product.stock_quantity || 0,
      min_stock_threshold: product.min_stock_threshold || 0,
      ordered_quantity: 1
    });

    this.itemsArray.push(newItem);
    this.calculateItemTotal(this.itemsArray.length - 1);
    this.showProductSearch = false;
    this.searchTerm = '';
  }

  // New methods for list-based product selection
  selectProductForItem(product: any, itemIndex: number) {
    try {
      console.log('Selecting product for item:', product, itemIndex);

      if (itemIndex >= 0 && itemIndex < this.itemsArray.length) {
        const itemGroup = this.itemsArray.at(itemIndex) as FormGroup;

        if (itemGroup) {
          itemGroup.patchValue({
            product_id: product.id,
            product_name: product.name,
            unit_rate: product.pr_rate || 0,
            tax_rate: product.tax_rate || 0,
            current_stock: product.stock_quantity || 0,
            min_stock_threshold: product.min_stock_threshold || 0
          });

          this.calculateItemTotal(itemIndex);
          console.log('Product selected and item updated');
        } else {
          console.error('Item group not found at index:', itemIndex);
        }
      } else {
        console.error('Invalid item index:', itemIndex);
      }
    } catch (error) {
      console.error('Error selecting product for item:', error);
      this.toast.toastServices('Error selecting product', 'danger', 'top');
    }
  }

  clearProductSelection(itemIndex: number) {
    try {
      if (itemIndex >= 0 && itemIndex < this.itemsArray.length) {
        const itemGroup = this.itemsArray.at(itemIndex) as FormGroup;

        if (itemGroup) {
          itemGroup.patchValue({
            product_id: '',
            product_name: '',
            unit_rate: 0,
            tax_rate: 0,
            current_stock: 0,
            min_stock_threshold: 0,
            line_total: 0,
            tax_amount: 0
          });

          this.calculateTotals();
        }
      }
    } catch (error) {
      console.error('Error clearing product selection:', error);
    }
  }

  getProductCode(productId: number): string {
    if (!productId || !this.products) return '';
    const product = this.products.find(p => p.id === productId);
    return product ? product.short_code : '';
  }

  getProductById(productId: number): any {
    if (!productId || !this.products) return null;
    return this.products.find(p => p.id === productId);
  }

  // Form submission
  async savePurchaseOrder() {
    if (this.purchaseOrderForm.invalid) {
      this.toast.toastServices('Please fill all required fields', 'warning', 'top');
      return;
    }

    if (this.itemsArray.length === 0) {
      this.toast.toastServices('Please add at least one item', 'warning', 'top');
      return;
    }

    await this.safeStartLoader();

    try {
      const formData = this.purchaseOrderForm.value;

      // Debug: Log the form data to check if calculated values are included
      console.log('Form data being sent:', formData);
      console.log('Items in form data:', formData.items);

      if (this.isEditMode && this.poId) {
        // Update existing PO
        const updateData = {
          id: this.poId,
          ...formData
        };

        console.log('Update data being sent:', updateData);
        const response: any = await this.purchaseOrderService.updatePurchaseOrder(updateData).toPromise();

        if (response.success) {
          this.toast.toastServices('Purchase Order updated successfully', 'success', 'top');

          // Navigate back to purchase order list - the list will refresh automatically
          this.nav.back();
        } else {
          this.toast.toastServices(response.message, 'danger', 'top');
        }
      } else {
        // Create new PO
        console.log('Create data being sent:', formData);
        const response: any = await this.purchaseOrderService.createPurchaseOrder(formData).toPromise();

        if (response.success) {
          this.toast.toastServices('Purchase Order created successfully', 'success', 'top');
          this.nav.back();
        } else {
          this.toast.toastServices(response.message, 'danger', 'top');
        }
      }
    } catch (error) {
      console.error('Error saving purchase order:', error);
      this.toast.toastServices('Error saving purchase order', 'danger', 'top');
    } finally {
      await this.safeDismissLoader();
    }
  }

  async saveDraft() {
    if (!this.purchaseOrderForm.get('supplier_id')?.value) {
      this.toast.toastServices('Please select a supplier', 'warning', 'top');
      return;
    }

    // Set status to draft and save
    const formData = {
      ...this.purchaseOrderForm.value,
      status: 'draft'
    };

    await this.safeStartLoader();

    try {
      const response: any = await this.purchaseOrderService.createPurchaseOrder(formData).toPromise();

      if (response.success) {
        this.toast.toastServices('Purchase Order saved as draft', 'success', 'top');
        this.nav.back();
      } else {
        this.toast.toastServices(response.message, 'danger', 'top');
      }
    } catch (error) {
      this.toast.toastServices('Error saving draft', 'danger', 'top');
    } finally {
      await this.safeDismissLoader();
    }
  }

  async submitForApproval() {
    if (this.purchaseOrderForm.invalid) {
      this.toast.toastServices('Please fill all required fields', 'warning', 'top');
      return;
    }

    if (this.itemsArray.length === 0) {
      this.toast.toastServices('Please add at least one item', 'warning', 'top');
      return;
    }

    const alert = await this.alertController.create({
      header: 'Submit for Approval',
      message: 'Are you sure you want to submit this purchase order for approval?',
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Submit',
          handler: async () => {
            const formData = {
              ...this.purchaseOrderForm.value,
              status: 'pending_approval'
            };

            await this.safeStartLoader();

            try {
              const response: any = await this.purchaseOrderService.createPurchaseOrder(formData).toPromise();

              if (response.success) {
                this.toast.toastServices('Purchase Order submitted for approval', 'success', 'top');
                this.nav.back();
              } else {
                this.toast.toastServices(response.message, 'danger', 'top');
              }
            } catch (error) {
              this.toast.toastServices('Error submitting for approval', 'danger', 'top');
            } finally {
              await this.safeDismissLoader();
            }
          }
        }
      ]
    });

    await alert.present();
  }

  // Product management utility methods
  findProductInSelectedItems(productId: number): number {
    try {
      if (!this.itemsArray || !this.itemsArray.controls) {
        return -1;
      }

      for (let i = 0; i < this.itemsArray.controls.length; i++) {
        const itemGroup = this.itemsArray.at(i) as FormGroup;
        const itemProductId = itemGroup.get('product_id')?.value;

        if (itemProductId === productId) {
          console.log(`Found existing product ${productId} at index ${i}`);
          return i;
        }
      }

      console.log(`Product ${productId} not found in selected items`);
      return -1;
    } catch (error) {
      console.error('Error finding product in selected items:', error);
      return -1;
    }
  }

  updateExistingItem(itemIndex: number, product: any) {
    try {
      if (itemIndex < 0 || itemIndex >= this.itemsArray.length) {
        console.error('Invalid item index for update:', itemIndex);
        return;
      }

      const itemGroup = this.itemsArray.at(itemIndex) as FormGroup;
      if (!itemGroup) {
        console.error('Item group not found at index:', itemIndex);
        return;
      }

      // Set box and pieces quantities based on product type
      let boxQty = 0;
      let piecesQty = 0;

      if (product.is_crate_based) {
        // For crate-based products, store crate quantity in box_quantity field
        boxQty = parseFloat(product.tempCrateQty) || 0;
        piecesQty = 0;
      } else {
        // For regular products, use box and pieces directly
        boxQty = parseFloat(product.tempBoxQty) || 0;
        piecesQty = parseFloat(product.tempPiecesQty) || 0;
      }

      console.log('Updating existing item:', {
        itemIndex,
        productName: product.name,
        boxQty,
        piecesQty,
        totalQty: product.tempTotalQty
      });

      // Update the form group with new values
      itemGroup.patchValue({
        ordered_quantity: product.tempTotalQty,
        box_quantity: boxQty,
        pieces_quantity: piecesQty,
        unit_rate: product.pr_rate || itemGroup.get('unit_rate')?.value || 0,
        tax_rate: product.tax_rate || itemGroup.get('tax_rate')?.value || 0
      });

      // Recalculate totals for this item (skip sync to prevent loops)
      this.calculateItemTotal(itemIndex, true);

      console.log('Successfully updated existing item');
    } catch (error) {
      console.error('Error updating existing item:', error);
      this.toast.toastServices('Error updating item', 'danger', 'top');
    }
  }

  syncItemToProductSearch(productId: number, boxQuantity: number, piecesQuantity: number) {
    try {
      if (!productId || this.isSyncing) {
        console.log('No productId provided for sync or already syncing');
        return;
      }

      // Set syncing flag to prevent loops
      this.isSyncing = true;

      // Check both filteredProducts and products arrays to ensure we find the right reference
      let productInSearch = null;

      // First try filteredProducts
      if (this.filteredProducts && this.filteredProducts.length > 0) {
        productInSearch = this.filteredProducts.find(p => p.id === productId);
        console.log('Found in filteredProducts:', !!productInSearch);
      }

      // If not found in filteredProducts, try products array
      if (!productInSearch && this.products && this.products.length > 0) {
        productInSearch = this.products.find(p => p.id === productId);
        console.log('Found in products array:', !!productInSearch);

        // If found in products but not in filteredProducts, add to filteredProducts
        if (productInSearch && this.filteredProducts) {
          // Check if it should be in filteredProducts based on search term
          if (!this.searchTerm.trim() ||
              productInSearch.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
              productInSearch.short_code.toLowerCase().includes(this.searchTerm.toLowerCase())) {
            console.log('Adding product to filteredProducts for sync');
            // Don't add duplicate, just use the reference from products
            const existingIndex = this.filteredProducts.findIndex(p => p.id === productId);
            if (existingIndex === -1) {
              this.filteredProducts.push(productInSearch);
            } else {
              // Use the existing reference
              productInSearch = this.filteredProducts[existingIndex];
            }
          }
        }
      }

      if (!productInSearch) {
        console.log(`Product ${productId} not found in any product list for sync`);
        this.isSyncing = false;
        return;
      }

      console.log('Syncing item changes back to product search (before update):', {
        productId,
        productName: productInSearch.name,
        boxQuantity,
        piecesQuantity,
        isCrateBased: productInSearch.is_crate_based,
        currentTempCrateQty: productInSearch.tempCrateQty,
        currentTempBoxQty: productInSearch.tempBoxQty,
        currentTempPiecesQty: productInSearch.tempPiecesQty
      });

      // Update the temp values based on product type
      if (productInSearch.is_crate_based) {
        // For crate-based products: box_quantity stores the crate quantity
        productInSearch.tempCrateQty = boxQuantity;
        productInSearch.tempBoxQty = 0;
        productInSearch.tempPiecesQty = 0;

        console.log('Updated crate-based values:', {
          tempCrateQty: productInSearch.tempCrateQty
        });
      } else {
        // For regular products: use box and pieces directly
        productInSearch.tempBoxQty = boxQuantity;
        productInSearch.tempPiecesQty = piecesQuantity;
        productInSearch.tempCrateQty = 0;

        console.log('Updated regular product values:', {
          tempBoxQty: productInSearch.tempBoxQty,
          tempPiecesQty: productInSearch.tempPiecesQty
        });
      }

      // Recalculate the total quantity for display consistency (without triggering more syncs)
      this.calculateProductQuantityWithoutSync(productInSearch);

      console.log('Successfully synced item to product search:', {
        tempTotalQty: productInSearch.tempTotalQty,
        finalTempCrateQty: productInSearch.tempCrateQty,
        finalTempBoxQty: productInSearch.tempBoxQty,
        finalTempPiecesQty: productInSearch.tempPiecesQty
      });

      // Update displayed products and force change detection
      this.displayedProducts = this.filteredProducts.slice(0, 20);
      console.log('Updated displayedProducts after sync:', this.displayedProducts.length);

    } catch (error) {
      console.error('Error syncing item to product search:', error);
    } finally {
      // Always reset syncing flag
      this.isSyncing = false;
    }
  }

  clearProductSearchValues(productId: number) {
    try {
      if (!productId || !this.filteredProducts) {
        return;
      }

      // Find the product in the filtered products list
      const productInSearch = this.filteredProducts.find(p => p.id === productId);
      if (!productInSearch) {
        console.log(`Product ${productId} not found in search list for clearing`);
        return;
      }

      console.log('Clearing product search values for:', productInSearch.name);

      // Clear all temp values
      productInSearch.tempCrateQty = 0;
      productInSearch.tempBoxQty = 0;
      productInSearch.tempPiecesQty = 0;
      productInSearch.tempTotalQty = 0;

      console.log('Successfully cleared product search values');
    } catch (error) {
      console.error('Error clearing product search values:', error);
    }
  }

  syncLoadedItemsToProductSearch() {
    try {
      if (!this.itemsArray || this.isSyncing) {
        console.log('Cannot sync loaded items - missing data or already syncing');
        return;
      }

      console.log('Syncing loaded items to product search...');

      // Set syncing flag to prevent loops
      this.isSyncing = true;

      // Iterate through all items in the form array
      this.itemsArray.controls.forEach((control) => {
        const itemGroup = control as FormGroup;
        const productId = itemGroup.get('product_id')?.value;
        const boxQuantity = parseFloat(itemGroup.get('box_quantity')?.value) || 0;
        const piecesQuantity = parseFloat(itemGroup.get('pieces_quantity')?.value) || 0;

        if (productId) {
          // Find the product in all available arrays
          let productInSearch = null;

          // First try filteredProducts
          if (this.filteredProducts && this.filteredProducts.length > 0) {
            productInSearch = this.filteredProducts.find(p => p.id === productId);
          }

          // If not found, try products array
          if (!productInSearch && this.products && this.products.length > 0) {
            productInSearch = this.products.find(p => p.id === productId);

            // If found in products but not in filteredProducts, add it to filteredProducts
            if (productInSearch) {
              console.log(`Adding product ${productInSearch.name} to filteredProducts for sync`);
              if (!this.filteredProducts) {
                this.filteredProducts = [];
              }
              // Check if it's not already in filteredProducts
              const existingIndex = this.filteredProducts.findIndex(p => p.id === productId);
              if (existingIndex === -1) {
                this.filteredProducts.push(productInSearch);
              }
            }
          }

          if (productInSearch) {
            console.log(`Syncing product ${productInSearch.name} with quantities:`, {
              boxQuantity,
              piecesQuantity,
              isCrateBased: productInSearch.is_crate_based
            });

            if (productInSearch.is_crate_based) {
              productInSearch.tempCrateQty = boxQuantity;
              productInSearch.tempBoxQty = 0;
              productInSearch.tempPiecesQty = 0;
            } else {
              productInSearch.tempBoxQty = boxQuantity;
              productInSearch.tempPiecesQty = piecesQuantity;
              productInSearch.tempCrateQty = 0;
            }

            // Calculate total quantity without triggering sync
            this.calculateProductQuantityWithoutSync(productInSearch);

            console.log(`Product ${productInSearch.name} synced with total qty: ${productInSearch.tempTotalQty}`);
          } else {
            console.warn(`Product with ID ${productId} not found in any product array`);
          }
        }
      });

      // Update displayed products to include any newly added products
      if (this.filteredProducts) {
        this.displayedProducts = this.filteredProducts.slice(0, 20);
      }

      console.log('Successfully synced all loaded items to product search');
      console.log('Updated displayedProducts count:', this.displayedProducts.length);
    } catch (error) {
      console.error('Error syncing loaded items to product search:', error);
    } finally {
      // Always reset syncing flag
      this.isSyncing = false;
    }
  }

  // Utility methods
  getTodayDate(): string {
    return this.todayDate;
  }

  // Reset date to current date (useful for create mode)
  resetToCurrentDate() {
    this.purchaseOrderForm.get('expected_delivery_date')?.setValue(this.todayDate);
    console.log('Date reset to current date:', this.todayDate);
  }

  // New methods for crate-based product handling
  calculateProductQuantity(product: any) {
    if (product.is_crate_based) {
      // For crate-based products: crate_qty * crate_size * unit_contains
      const crateQty = parseFloat(product.tempCrateQty) || 0;
      const crateSize = product.crate_size || 1;
      const unitContains = product.unit_contains || 1;
      product.tempTotalQty = crateQty * crateSize * unitContains;

      console.log('Calculated quantity for crate-based product:', product.name, {
        crateQty,
        crateSize,
        unitContains,
        totalQty: product.tempTotalQty,
        formula: `${crateQty} * ${crateSize} * ${unitContains} = ${product.tempTotalQty}`
      });
    } else {
      // For regular products: (box * unit_contains) + pieces
      const boxQty = parseFloat(product.tempBoxQty) || 0;
      const piecesQty = parseFloat(product.tempPiecesQty) || 0;
      const unitContains = product.unit_contains || 1;
      product.tempTotalQty = (boxQty * unitContains) + piecesQty;

      console.log('Calculated quantity for regular product:', product.name, {
        boxQty,
        piecesQty,
        unitContains,
        totalQty: product.tempTotalQty,
        formula: `(${boxQty} * ${unitContains}) + ${piecesQty} = ${product.tempTotalQty}`
      });
    }

    // In edit mode, sync changes back to selected items if this product exists there
    if (this.isEditMode) {
      this.syncProductQuantityToSelectedItems(product);
    }
  }

  // Calculate product quantity without triggering sync operations
  calculateProductQuantityWithoutSync(product: any) {
    if (product.is_crate_based) {
      // For crate-based products: crate_qty * crate_size * unit_contains
      const crateQty = parseFloat(product.tempCrateQty) || 0;
      const crateSize = product.crate_size || 1;
      const unitContains = product.unit_contains || 1;
      product.tempTotalQty = crateQty * crateSize * unitContains;
    } else {
      // For regular products: (box * unit_contains) + pieces
      const boxQty = parseFloat(product.tempBoxQty) || 0;
      const piecesQty = parseFloat(product.tempPiecesQty) || 0;
      const unitContains = product.unit_contains || 1;
      product.tempTotalQty = (boxQty * unitContains) + piecesQty;
    }
  }

  // Sync product quantity changes back to selected items in edit mode
  syncProductQuantityToSelectedItems(product: any) {
    try {
      if (!this.itemsArray || this.isSyncing) {
        return;
      }

      // Find the corresponding item in selected items
      const itemIndex = this.findProductInSelectedItems(product.id);

      if (itemIndex !== -1) {
        console.log(`Syncing product ${product.name} quantity changes to selected item at index ${itemIndex}`);

        // Set syncing flag to prevent loops
        this.isSyncing = true;

        const itemGroup = this.itemsArray.at(itemIndex) as FormGroup;

        // Update quantities based on product type
        if (product.is_crate_based) {
          itemGroup.patchValue({
            box_quantity: product.tempCrateQty || 0,
            pieces_quantity: 0,
            ordered_quantity: product.tempTotalQty || 0
          });
        } else {
          itemGroup.patchValue({
            box_quantity: product.tempBoxQty || 0,
            pieces_quantity: product.tempPiecesQty || 0,
            ordered_quantity: product.tempTotalQty || 0
          });
        }

        // Recalculate item total (skip sync to prevent loops)
        this.calculateItemTotal(itemIndex, true);

        console.log(`Successfully synced product ${product.name} to selected items`);
      }
    } catch (error) {
      console.error('Error syncing product quantity to selected items:', error);
    } finally {
      // Always reset syncing flag
      this.isSyncing = false;
    }
  }

  addProductToOrder(product: any) {
    if (!product.tempTotalQty || product.tempTotalQty <= 0) {
      this.toast.toastServices('Please enter quantity', 'warning', 'top');
      return;
    }

    // Check if product already exists in selected items
    const existingItemIndex = this.findProductInSelectedItems(product.id);

    if (existingItemIndex !== -1) {
      // Update existing item
      this.updateExistingItem(existingItemIndex, product);
      this.toast.toastServices(`${product.name} quantity updated`, 'success', 'top');
      return;
    }

    const newItem = this.createItemFormGroup();

    // Set box and pieces quantities based on product type
    let boxQty = 0;
    let piecesQty = 0;

    if (product.is_crate_based) {
      // For crate-based products, store crate quantity in box_quantity field
      boxQty = parseFloat(product.tempCrateQty) || 0;
      piecesQty = 0;
    } else {
      // For regular products, use box and pieces directly
      boxQty = parseFloat(product.tempBoxQty) || 0;
      piecesQty = parseFloat(product.tempPiecesQty) || 0;
    }

    newItem.patchValue({
      product_id: product.id,
      product_name: product.name,
      ordered_quantity: product.tempTotalQty,
      box_quantity: boxQty,
      pieces_quantity: piecesQty,
      unit_rate: product.pr_rate || 0,
      tax_rate: product.tax_rate || 0,
      current_stock: product.stock_quantity || 0,
      min_stock_threshold: product.min_stock_threshold || 0
    });

    this.itemsArray.push(newItem);
    this.calculateItemTotal(this.itemsArray.length - 1);

    // No reset - values will persist in the input fields
    this.toast.toastServices(`${product.name} added to order`, 'success', 'top');
  }

  addProductToOrderWithoutReset(product: any) {
    if (!product.tempTotalQty || product.tempTotalQty <= 0) {
      this.toast.toastServices('Please enter quantity', 'warning', 'top');
      return;
    }

    const newItem = this.createItemFormGroup();

    // Set box and pieces quantities based on product type
    let boxQty = 0;
    let piecesQty = 0;

    if (product.is_crate_based) {
      // For crate-based products, store crate quantity in box_quantity field
      boxQty = parseFloat(product.tempCrateQty) || 0;
      piecesQty = 0;
    } else {
      // For regular products, use box and pieces directly
      boxQty = parseFloat(product.tempBoxQty) || 0;
      piecesQty = parseFloat(product.tempPiecesQty) || 0;
    }

    newItem.patchValue({
      product_id: product.id,
      product_name: product.name,
      ordered_quantity: product.tempTotalQty,
      box_quantity: boxQty,
      pieces_quantity: piecesQty,
      unit_rate: product.pr_rate || 0,
      tax_rate: product.tax_rate || 0,
      current_stock: product.stock_quantity || 0,
      min_stock_threshold: product.min_stock_threshold || 0
    });

    this.itemsArray.push(newItem);
    this.calculateItemTotal(this.itemsArray.length - 1);

    // Don't reset values here - they will be reset by the calling method
    this.toast.toastServices(`${product.name} added to order`, 'success', 'top');
  }



  onEnterKeyUp(event: any, product: any) {
    // Prevent default behavior to avoid form submission
    event.preventDefault();

    // Calculate quantity and add to order if valid
    this.calculateProductQuantity(product);

    if (product.tempTotalQty && product.tempTotalQty > 0) {
      console.log('Enter key pressed - Processing product:', product.name, 'Qty:', product.tempTotalQty);

      // Check if product already exists in selected items
      const existingItemIndex = this.findProductInSelectedItems(product.id);

      if (existingItemIndex !== -1) {
        // Update existing item
        this.updateExistingItem(existingItemIndex, product);
        console.log('Updated existing item at index:', existingItemIndex);
      } else {
        // Add new item to order without resetting values
        this.addProductToOrderWithoutReset(product);
        console.log('Added new item for product:', product.name);
      }

      // Move focus to next product input
      this.focusNextProductInput(product);

      // No reset - values will persist in the input fields
    } else {
      this.toast.toastServices('Please enter a valid quantity', 'warning', 'top');
    }
  }

  private focusNextProductInput(currentProduct: any) {
    try {
      const currentIndex = this.filteredProducts.findIndex(p => p.id === currentProduct.id);
      const nextIndex = currentIndex + 1;

      if (nextIndex < this.filteredProducts.length) {
        const nextProduct = this.filteredProducts[nextIndex];

        // Determine which input to focus based on product type
        let nextInputId: string;
        if (nextProduct.is_crate_based) {
          nextInputId = `crate-input-${nextProduct.id}`;
        } else {
          nextInputId = `box-input-${nextProduct.id}`;
        }

        console.log(`Attempting to focus next input: ${nextInputId} for product: ${nextProduct.name}`);

        // Focus the next input with a minimal delay to ensure smooth transition
        setTimeout(() => {
          const nextInput = document.getElementById(nextInputId);
          if (nextInput) {
            const ionInput = nextInput.querySelector('input');
            if (ionInput) {
              ionInput.focus();
              ionInput.select(); // Select all text for easier editing
              console.log(`Successfully focused next input: ${nextInputId}`);
            } else {
              console.warn(`Ion input not found in element: ${nextInputId}`);
              // Try alternative selector
              const altInput = nextInput.querySelector('ion-input input');
              if (altInput) {
                (altInput as HTMLInputElement).focus();
                (altInput as HTMLInputElement).select();
                console.log(`Successfully focused using alternative selector: ${nextInputId}`);
              }
            }
          } else {
            console.warn(`Next input element not found: ${nextInputId}`);
          }
        }, 50); // Minimal delay for immediate focus transition
      } else {
        console.log('Reached end of product list');
        this.toast.toastServices('Reached end of product list', 'info', 'top');
      }
    } catch (error) {
      console.error('Error focusing next input:', error);
    }
  }

  formatCurrency(amount: number): string {
    try {
      const numericAmount = parseFloat(amount?.toString()) || 0;
      return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR'
      }).format(numericAmount);
    } catch (error) {
      console.error('Error formatting currency:', error, 'Amount:', amount);
      return '₹0.00';
    }
  }

  // UI helper methods to match invoice system
  isAllCrateBased(): boolean {
    return this.displayedProducts.every(product => product.is_crate_based);
  }

  hasQuantity(product: any): boolean {
    if (product.is_crate_based) {
      return (product.tempCrateQty || 0) > 0;
    } else {
      return (product.tempBoxQty || 0) > 0 || (product.tempPiecesQty || 0) > 0;
    }
  }

  handleProductClick(event: Event, product: any) {
    // Similar to invoice system's handleClick method
    const currentTime = new Date().getTime();
    const elapsedTime = currentTime - (this.lastClickTime || 0);
    if (elapsedTime < 300) {
      // Double click - could open product details if needed
      console.log('Double click on product:', product.name);
    } else {
      // Single click - focus on input
      this.focusProductInput(product);
    }
    this.lastClickTime = currentTime;
  }

  private lastClickTime = 0;

  private focusProductInput(product: any) {
    try {
      let inputId: string;
      if (product.is_crate_based) {
        inputId = `crate-input-${product.id}`;
      } else {
        inputId = `box-input-${product.id}`;
      }

      setTimeout(() => {
        const input = document.getElementById(inputId);
        if (input) {
          const ionInput = input.querySelector('input');
          if (ionInput) {
            ionInput.focus();
            ionInput.select();
          }
        }
      }, 100);
    } catch (error) {
      console.error('Error focusing product input:', error);
    }
  }

  goBack() {
    this.nav.back();
  }

  // Helper methods for UI
  clearSearch() {
    this.searchTerm = '';
    this.filterProducts();
  }

  // Force refresh sync between list view and selected items
  forceRefreshSync() {
    try {
      console.log('Force refreshing sync between list view and selected items...');

      if (this.isEditMode && this.itemsArray && this.itemsArray.length > 0) {
        // Re-sync all loaded items to ensure consistency
        this.syncLoadedItemsToProductSearch();
        console.log('Force refresh sync completed');
      }
    } catch (error) {
      console.error('Error in force refresh sync:', error);
    }
  }

  getTotalQuantity(): number {
    try {
      let total = 0;
      if (this.itemsArray && this.itemsArray.controls) {
        this.itemsArray.controls.forEach((control) => {
          const itemGroup = control as FormGroup;
          const quantity = parseFloat(itemGroup.get('ordered_quantity')?.value) || 0;
          total += quantity;
        });
      }
      return total;
    } catch (error) {
      console.error('Error calculating total quantity:', error);
      return 0;
    }
  }

  // Check if a product is crate-based
  isProductCrateBased(productId: number): boolean {
    if (!productId) return false;
    const product = this.products.find(p => p.id === productId);
    return product ? product.is_crate_based : false;
  }

  // Update item quantity from editable inputs in selected items section
  updateItemQuantityFromInput(itemIndex: number, inputType: 'box' | 'pieces' | 'crate', event: any) {
    try {
      const value = parseFloat(event.target.value) || 0;

      if (itemIndex < 0 || itemIndex >= this.itemsArray.length) {
        console.error('Invalid item index:', itemIndex);
        return;
      }

      const itemGroup = this.itemsArray.at(itemIndex) as FormGroup;
      if (!itemGroup) {
        console.error('Item group not found at index:', itemIndex);
        return;
      }

      const productId = itemGroup.get('product_id')?.value;
      const product = this.products.find(p => p.id === productId);

      if (!product) {
        console.error('Product not found for item:', productId);
        return;
      }

      // Update the appropriate quantity field
      if (inputType === 'crate' && product.is_crate_based) {
        itemGroup.patchValue({ box_quantity: value, pieces_quantity: 0 });
      } else if (inputType === 'box' && !product.is_crate_based) {
        itemGroup.patchValue({ box_quantity: value });
      } else if (inputType === 'pieces' && !product.is_crate_based) {
        itemGroup.patchValue({ pieces_quantity: value });
      }

      // Recalculate the item total
      this.calculateItemTotal(itemIndex);

      // Sync back to product search for bidirectional sync
      const boxQuantity = parseFloat(itemGroup.get('box_quantity')?.value) || 0;
      const piecesQuantity = parseFloat(itemGroup.get('pieces_quantity')?.value) || 0;
      this.syncItemToProductSearch(productId, boxQuantity, piecesQuantity);

      console.log(`Updated ${inputType} quantity for item ${itemIndex}:`, value);
    } catch (error) {
      console.error('Error updating item quantity from input:', error);
      this.toast.toastServices('Error updating quantity', 'danger', 'top');
    }
  }

  // Safe loader methods to prevent conflicts
  private async safeStartLoader(): Promise<void> {
    try {
      if (!this.isLoaderActive) {
        this.isLoaderActive = true;
        await this.ionLoaderService.startLoader();
        console.log('Loader started safely');
      } else {
        console.log('Loader already active, skipping start');
      }
    } catch (error) {
      console.error('Error starting loader:', error);
      this.isLoaderActive = false;
    }
  }

  private async safeDismissLoader(): Promise<void> {
    try {
      if (this.isLoaderActive) {
        await this.ionLoaderService.dismissLoader();
        this.isLoaderActive = false;
        console.log('Loader dismissed safely');
      } else {
        console.log('No active loader to dismiss');
      }
    } catch (error) {
      console.error('Error dismissing loader:', error);
      this.isLoaderActive = false;
    }
  }
}
