import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { CreatePurchaseOrderPageRoutingModule } from './create-purchase-order-routing.module';
import { SharedModule } from '../shared/modules/shared/shared.module';

import { CreatePurchaseOrderPage } from './create-purchase-order.page';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    CreatePurchaseOrderPageRoutingModule,
    SharedModule
  ],
  declarations: [CreatePurchaseOrderPage]
})
export class CreatePurchaseOrderPageModule {}
