PODS:
  - B<PERSON><PERSON>lCapacitorPluginPrinter (0.0.5):
    - Capacitor
  - Capacitor (6.2.1):
    - CapacitorCordova
  - CapacitorApp (6.0.2):
    - Capacitor
  - CapacitorCordova (6.2.1)
  - CapacitorDevice (7.0.1):
    - Capacitor
  - CapacitorFilesystem (6.0.3):
    - Capacitor
  - CapacitorHaptics (6.0.2):
    - Capacitor
  - CapacitorKeyboard (6.0.3):
    - Capacitor
  - CapacitorStatusBar (6.0.2):
    - Capacitor
  - CordovaPlugins (6.2.1):
    - CapacitorCordova
  - CordovaPluginsResources (0.0.105)

DEPENDENCIES:
  - "BcyesilCapacitorPluginPrinter (from `../../node_modules/@bcyesil/capacitor-plugin-printer`)"
  - "Capacitor (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorApp (from `../../node_modules/@capacitor/app`)"
  - "CapacitorCordova (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorDevice (from `../../node_modules/@capacitor/device`)"
  - "CapacitorFilesystem (from `../../node_modules/@capacitor/filesystem`)"
  - "CapacitorHaptics (from `../../node_modules/@capacitor/haptics`)"
  - "CapacitorKeyboard (from `../../node_modules/@capacitor/keyboard`)"
  - "CapacitorStatusBar (from `../../node_modules/@capacitor/status-bar`)"
  - CordovaPlugins (from `../capacitor-cordova-ios-plugins`)
  - CordovaPluginsResources (from `../capacitor-cordova-ios-plugins`)

EXTERNAL SOURCES:
  BcyesilCapacitorPluginPrinter:
    :path: "../../node_modules/@bcyesil/capacitor-plugin-printer"
  Capacitor:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorApp:
    :path: "../../node_modules/@capacitor/app"
  CapacitorCordova:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorDevice:
    :path: "../../node_modules/@capacitor/device"
  CapacitorFilesystem:
    :path: "../../node_modules/@capacitor/filesystem"
  CapacitorHaptics:
    :path: "../../node_modules/@capacitor/haptics"
  CapacitorKeyboard:
    :path: "../../node_modules/@capacitor/keyboard"
  CapacitorStatusBar:
    :path: "../../node_modules/@capacitor/status-bar"
  CordovaPlugins:
    :path: "../capacitor-cordova-ios-plugins"
  CordovaPluginsResources:
    :path: "../capacitor-cordova-ios-plugins"

SPEC CHECKSUMS:
  BcyesilCapacitorPluginPrinter: 6fab6e423ad1c68256cbecc89d7f90736c36b013
  Capacitor: c95400d761e376be9da6be5a05f226c0e865cebf
  CapacitorApp: e1e6b7d05e444d593ca16fd6d76f2b7c48b5aea7
  CapacitorCordova: 8d93e14982f440181be7304aa9559ca631d77fff
  CapacitorDevice: c6f6d587dd310527f8a48bf09c4e7b4a4cf14329
  CapacitorFilesystem: 59270a63c60836248812671aa3b15df673fbaf74
  CapacitorHaptics: 4fc15afe22b123d093e6ace24d95e8e3b1f261b9
  CapacitorKeyboard: e89189ad398b815b6ff7e52271f0fb4f911a0a0a
  CapacitorStatusBar: b16799a26320ffa52f6c8b01737d5a95bbb8f3eb
  CordovaPlugins: 7567775ffe91a20152232d1c81262db2ca48c0ff
  CordovaPluginsResources: 0c211f770ed91763a179ebcd9b9346e120b70662

PODFILE CHECKSUM: 15e216ff32949d6f56a46b465c771bc8ff07e4e6

COCOAPODS: 1.16.2
