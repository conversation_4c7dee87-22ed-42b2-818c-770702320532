# Permissions Management Scripts

This directory contains automated scripts for managing platform-specific permissions in the KingBill cross-platform application.

## 📱 Scripts Overview

### 1. Unified Permissions Manager (Recommended)
**File**: `permissions-manager.js`

A comprehensive script that manages permissions for both Android and iOS platforms automatically.

#### Features:
- ✅ Automatic platform detection
- ✅ Cross-platform permission management
- ✅ Status checking and reporting
- ✅ Capacitor sync integration
- ✅ Backup creation for iOS
- ✅ Detailed logging and error handling

#### Usage:
```bash
# Add permissions to all available platforms
node scripts/permissions-manager.js

# Add Android permissions only
node scripts/permissions-manager.js android

# Add iOS permissions only
node scripts/permissions-manager.js ios

# Check status of all platforms
node scripts/permissions-manager.js status

# Sync Capacitor and add all permissions
node scripts/permissions-manager.js sync

# Show help
node scripts/permissions-manager.js help
```

### 2. Android Permissions Script
**File**: `add-bluetooth-permissions.js`

Manages Android-specific Bluetooth permissions in AndroidManifest.xml.

#### Permissions Added:
- `BLUETOOTH` (for Android ≤ 30)
- `BLUETOOTH_ADMIN` (for Android ≤ 30)
- `BLUETOOTH_CONNECT` (for Android 12+)
- `BLUETOOTH_SCAN` (for Android 12+)

### 3. iOS Permissions Script
**File**: `add-ios-permissions.js`

Manages iOS-specific permissions in Info.plist.

#### Permissions Added:
- `NSBluetoothAlwaysUsageDescription` - Bluetooth access
- `NSBluetoothPeripheralUsageDescription` - Bluetooth peripheral access
- `NSPhotoLibraryUsageDescription` - Photo library access
- `NSPhotoLibraryAddUsageDescription` - Photo library write access
- `UIPrintingPolicy` - AirPrint support
- `NSCameraUsageDescription` - Camera access (for barcode scanning)
- `NSLocationWhenInUseUsageDescription` - Location access (for delivery tracking)
- `NSMicrophoneUsageDescription` - Microphone access (for voice notes)

## 🚀 NPM Scripts

The following npm scripts are available in `package.json`:

```bash
# Sync Capacitor and add permissions for all platforms
npm run cap:sync

# Sync Android only with permissions
npm run cap:sync:android

# Sync iOS only with permissions
npm run cap:sync:ios

# Full sync with Capacitor sync + permissions setup
npm run cap:sync:full

# Add permissions to all platforms
npm run setup-permissions

# Check permissions status
npm run permissions-status

# Legacy scripts (still available)
npm run add-bluetooth-permissions
npm run add-ios-permissions
```

## 🔧 How It Works

### Android Permissions
1. **Detection**: Checks if `android/app/src/main/AndroidManifest.xml` exists
2. **Validation**: Verifies if Bluetooth permissions already exist
3. **Addition**: Adds permissions before the closing `</manifest>` tag
4. **Safety**: Prevents duplicate permissions

### iOS Permissions
1. **Detection**: Checks if `ios/App/App/Info.plist` exists
2. **Backup**: Creates `.backup` file before modifications
3. **Validation**: Verifies if permissions already exist
4. **Addition**: Adds permissions before the closing `</dict>` tag
5. **Safety**: Prevents duplicate permissions

## 📋 Permissions Details

### Android Bluetooth Permissions
```xml
<!-- Bluetooth permissions for Cordova Bluetooth Serial plugin -->
<uses-permission android:name="android.permission.BLUETOOTH" android:maxSdkVersion="30" />
<uses-permission android:name="android.permission.BLUETOOTH_ADMIN" android:maxSdkVersion="30" />
<uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
<uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
```

### iOS Permissions
```xml
<!-- Bluetooth permissions for iOS -->
<key>NSBluetoothAlwaysUsageDescription</key>
<string>This app uses Bluetooth to connect to thermal printers for printing invoices and receipts.</string>

<!-- Photo Library permissions for social sharing -->
<key>NSPhotoLibraryUsageDescription</key>
<string>This app needs access to photo library to share invoices and receipts.</string>

<!-- AirPrint support -->
<key>UIPrintingPolicy</key>
<dict>
    <key>UIPrintingPolicyAllowsAirPrint</key>
    <true/>
</dict>

<!-- Additional permissions for camera, location, microphone -->
```

## 🛠️ Development Workflow

### Initial Setup
```bash
# Add platforms
npx cap add android
npx cap add ios

# Setup all permissions
npm run setup-permissions

# Check status
npm run permissions-status
```

### Regular Development
```bash
# After code changes, sync with permissions
npm run cap:sync

# Or sync specific platform
npm run cap:sync:android
npm run cap:sync:ios
```

### Troubleshooting
```bash
# Check current status
npm run permissions-status

# Force re-add permissions
node scripts/permissions-manager.js

# Full sync and setup
npm run cap:sync:full
```

## 🔍 Error Handling

The scripts include comprehensive error handling:

- **Platform Detection**: Automatically detects if platforms are added
- **File Validation**: Checks if required files exist
- **Duplicate Prevention**: Prevents adding permissions multiple times
- **Backup Creation**: Creates backups before modifying iOS files
- **Detailed Logging**: Provides clear success/error messages

## 📱 Platform-Specific Notes

### Android
- Requires Android platform to be added: `npx cap add android`
- Permissions are added to `AndroidManifest.xml`
- Supports both legacy and modern Bluetooth permissions
- No backup needed (version control handles this)

### iOS
- Requires iOS platform to be added: `npx cap add ios`
- Permissions are added to `Info.plist`
- Creates automatic backup files
- Includes comprehensive permission descriptions
- Supports AirPrint, Bluetooth, Camera, Location, and Microphone

## 🚨 Important Notes

1. **Run After Platform Addition**: Always run permission scripts after adding new platforms
2. **Version Control**: Commit permission changes to version control
3. **Testing**: Test permissions on actual devices
4. **App Store**: Ensure permission descriptions are appropriate for app store submission
5. **Backup Files**: iOS backup files (`.backup`) should not be committed to version control

## 🔄 Integration with CI/CD

For automated builds, include permission setup in your CI/CD pipeline:

```bash
# In your CI/CD script
npm install
npx cap add android
npx cap add ios
npm run setup-permissions
npx cap sync
```

This ensures permissions are always properly configured in automated builds.
