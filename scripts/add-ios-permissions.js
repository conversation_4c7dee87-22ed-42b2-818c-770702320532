#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const infoPlistPath = path.join(__dirname, '../ios/App/App/Info.plist');

// iOS permissions to add
const iosPermissions = [
    '	<!-- Bluetooth permissions for iOS -->',
    '	<key>NSBluetoothAlwaysUsageDescription</key>',
    '	<string>This app uses Bluetooth to connect to thermal printers for printing invoices and receipts.</string>',
    '	<key>NSBluetoothPeripheralUsageDescription</key>',
    '	<string>This app uses Bluetooth to connect to thermal printers for printing invoices and receipts.</string>',
    '	',
    '	<!-- Photo Library permissions for social sharing -->',
    '	<key>NSPhotoLibraryUsageDescription</key>',
    '	<string>This app needs access to photo library to share invoices and receipts.</string>',
    '	<key>NSPhotoLibraryAddUsageDescription</key>',
    '	<string>This app needs access to photo library to save invoices and receipts.</string>',
    '	',
    '	<!-- AirPrint support -->',
    '	<key>UIPrintingPolicy</key>',
    '	<dict>',
    '		<key>UIPrintingPolicyAllowsAirPrint</key>',
    '		<true/>',
    '	</dict>',
    '	',
    '	<!-- Camera permissions (if needed for barcode scanning) -->',
    '	<key>NSCameraUsageDescription</key>',
    '	<string>This app uses camera to scan barcodes and QR codes for quick product entry.</string>',
    '	',
    '	<!-- Location permissions (if needed for delivery tracking) -->',
    '	<key>NSLocationWhenInUseUsageDescription</key>',
    '	<string>This app uses location to track delivery routes and optimize logistics.</string>',
    '	',
    '	<!-- Microphone permissions (if needed for voice notes) -->',
    '	<key>NSMicrophoneUsageDescription</key>',
    '	<string>This app uses microphone to record voice notes for orders and deliveries.</string>'
];

function addIOSPermissions() {
    try {
        if (!fs.existsSync(infoPlistPath)) {
            console.log('❌ Info.plist not found. Make sure iOS platform is added to Capacitor.');
            console.log('   Run: npx cap add ios');
            return;
        }

        let plistContent = fs.readFileSync(infoPlistPath, 'utf8');
        
        // Check if permissions already exist
        if (plistContent.includes('NSBluetoothAlwaysUsageDescription')) {
            console.log('✅ iOS permissions already exist in Info.plist');
            return;
        }

        // Find the closing </dict></plist> tags
        const dictEndTag = '</dict>';
        const plistEndTag = '</plist>';
        
        // Find the last </dict> before </plist>
        const plistEndIndex = plistContent.lastIndexOf(plistEndTag);
        const dictEndIndex = plistContent.lastIndexOf(dictEndTag, plistEndIndex);
        
        if (dictEndIndex === -1 || plistEndIndex === -1) {
            console.log('❌ Could not find proper plist structure');
            return;
        }

        // Add permissions before the closing </dict>
        const permissionsBlock = '\n' + iosPermissions.join('\n') + '\n';
        
        const newPlistContent = plistContent.slice(0, dictEndIndex) + 
            permissionsBlock + 
            plistContent.slice(dictEndIndex);

        // Create backup
        const backupPath = infoPlistPath + '.backup';
        if (!fs.existsSync(backupPath)) {
            fs.writeFileSync(backupPath, plistContent, 'utf8');
            console.log('📋 Created backup: Info.plist.backup');
        }

        fs.writeFileSync(infoPlistPath, newPlistContent, 'utf8');
        console.log('✅ iOS permissions added to Info.plist');
        console.log('📱 Added permissions:');
        console.log('   - Bluetooth (NSBluetoothAlwaysUsageDescription)');
        console.log('   - Bluetooth Peripheral (NSBluetoothPeripheralUsageDescription)');
        console.log('   - Photo Library (NSPhotoLibraryUsageDescription)');
        console.log('   - Photo Library Add (NSPhotoLibraryAddUsageDescription)');
        console.log('   - AirPrint (UIPrintingPolicy)');
        console.log('   - Camera (NSCameraUsageDescription)');
        console.log('   - Location (NSLocationWhenInUseUsageDescription)');
        console.log('   - Microphone (NSMicrophoneUsageDescription)');
        
    } catch (error) {
        console.error('❌ Error adding iOS permissions:', error.message);
    }
}

function removeIOSPermissions() {
    try {
        if (!fs.existsSync(infoPlistPath)) {
            console.log('❌ Info.plist not found');
            return;
        }

        const backupPath = infoPlistPath + '.backup';
        if (fs.existsSync(backupPath)) {
            const backupContent = fs.readFileSync(backupPath, 'utf8');
            fs.writeFileSync(infoPlistPath, backupContent, 'utf8');
            console.log('✅ iOS permissions removed, restored from backup');
        } else {
            console.log('❌ No backup found, cannot restore');
        }
        
    } catch (error) {
        console.error('❌ Error removing iOS permissions:', error.message);
    }
}

function showHelp() {
    console.log('📱 iOS Permissions Manager for KingBill');
    console.log('');
    console.log('Usage:');
    console.log('  node scripts/add-ios-permissions.js [command]');
    console.log('');
    console.log('Commands:');
    console.log('  add (default)  Add iOS permissions to Info.plist');
    console.log('  remove         Remove iOS permissions (restore from backup)');
    console.log('  help           Show this help message');
    console.log('');
    console.log('Examples:');
    console.log('  node scripts/add-ios-permissions.js');
    console.log('  node scripts/add-ios-permissions.js add');
    console.log('  node scripts/add-ios-permissions.js remove');
}

// Parse command line arguments
const command = process.argv[2] || 'add';

switch (command.toLowerCase()) {
    case 'add':
        addIOSPermissions();
        break;
    case 'remove':
        removeIOSPermissions();
        break;
    case 'help':
    case '--help':
    case '-h':
        showHelp();
        break;
    default:
        console.log('❌ Unknown command:', command);
        showHelp();
        process.exit(1);
}
