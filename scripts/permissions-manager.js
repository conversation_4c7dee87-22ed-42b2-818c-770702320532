#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const androidManifestPath = path.join(__dirname, '../android/app/src/main/AndroidManifest.xml');
const iosInfoPlistPath = path.join(__dirname, '../ios/App/App/Info.plist');

class PermissionsManager {
    constructor() {
        this.androidPermissions = [
            '    <uses-permission android:name="android.permission.BLUETOOTH" android:maxSdkVersion="30" />',
            '    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" android:maxSdkVersion="30" />',
            '    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />',
            '    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />'
        ];

        this.iosPermissions = [
            '	<!-- Bluetooth permissions for iOS -->',
            '	<key>NSBluetoothAlwaysUsageDescription</key>',
            '	<string>This app uses Bluetooth to connect to thermal printers for printing invoices and receipts.</string>',
            '	<key>NSBluetoothPeripheralUsageDescription</key>',
            '	<string>This app uses Bluetooth to connect to thermal printers for printing invoices and receipts.</string>',
            '	',
            '	<!-- Photo Library permissions for social sharing -->',
            '	<key>NSPhotoLibraryUsageDescription</key>',
            '	<string>This app needs access to photo library to share invoices and receipts.</string>',
            '	<key>NSPhotoLibraryAddUsageDescription</key>',
            '	<string>This app needs access to photo library to save invoices and receipts.</string>',
            '	',
            '	<!-- AirPrint support -->',
            '	<key>UIPrintingPolicy</key>',
            '	<dict>',
            '		<key>UIPrintingPolicyAllowsAirPrint</key>',
            '		<true/>',
            '	</dict>',
            '	',
            '	<!-- Camera permissions (if needed for barcode scanning) -->',
            '	<key>NSCameraUsageDescription</key>',
            '	<string>This app uses camera to scan barcodes and QR codes for quick product entry.</string>',
            '	',
            '	<!-- Location permissions (if needed for delivery tracking) -->',
            '	<key>NSLocationWhenInUseUsageDescription</key>',
            '	<string>This app uses location to track delivery routes and optimize logistics.</string>',
            '	',
            '	<!-- Microphone permissions (if needed for voice notes) -->',
            '	<key>NSMicrophoneUsageDescription</key>',
            '	<string>This app uses microphone to record voice notes for orders and deliveries.</string>'
        ];
    }

    checkPlatforms() {
        const androidExists = fs.existsSync(androidManifestPath);
        const iosExists = fs.existsSync(iosInfoPlistPath);
        
        return { androidExists, iosExists };
    }

    addAndroidPermissions() {
        try {
            if (!fs.existsSync(androidManifestPath)) {
                console.log('❌ AndroidManifest.xml not found. Run: npx cap add android');
                return false;
            }

            let manifestContent = fs.readFileSync(androidManifestPath, 'utf8');
            
            if (manifestContent.includes('android.permission.BLUETOOTH_CONNECT')) {
                console.log('✅ Android Bluetooth permissions already exist');
                return true;
            }

            const manifestEndTag = '</manifest>';
            const manifestEndIndex = manifestContent.lastIndexOf(manifestEndTag);
            
            if (manifestEndIndex === -1) {
                console.log('❌ Could not find </manifest> tag');
                return false;
            }

            const permissionsBlock = '\n    <!-- Bluetooth permissions for Cordova Bluetooth Serial plugin -->\n' +
                this.androidPermissions.join('\n') + '\n';
            
            const newManifestContent = manifestContent.slice(0, manifestEndIndex) + 
                permissionsBlock + 
                manifestContent.slice(manifestEndIndex);

            fs.writeFileSync(androidManifestPath, newManifestContent, 'utf8');
            console.log('✅ Android Bluetooth permissions added');
            return true;
            
        } catch (error) {
            console.error('❌ Error adding Android permissions:', error.message);
            return false;
        }
    }

    addIOSPermissions() {
        try {
            if (!fs.existsSync(iosInfoPlistPath)) {
                console.log('❌ Info.plist not found. Run: npx cap add ios');
                return false;
            }

            let plistContent = fs.readFileSync(iosInfoPlistPath, 'utf8');
            
            if (plistContent.includes('NSBluetoothAlwaysUsageDescription')) {
                console.log('✅ iOS permissions already exist');
                return true;
            }

            const dictEndTag = '</dict>';
            const plistEndTag = '</plist>';
            
            const plistEndIndex = plistContent.lastIndexOf(plistEndTag);
            const dictEndIndex = plistContent.lastIndexOf(dictEndTag, plistEndIndex);
            
            if (dictEndIndex === -1 || plistEndIndex === -1) {
                console.log('❌ Could not find proper plist structure');
                return false;
            }

            // Create backup
            const backupPath = iosInfoPlistPath + '.backup';
            if (!fs.existsSync(backupPath)) {
                fs.writeFileSync(backupPath, plistContent, 'utf8');
            }

            const permissionsBlock = '\n' + this.iosPermissions.join('\n') + '\n';
            
            const newPlistContent = plistContent.slice(0, dictEndIndex) + 
                permissionsBlock + 
                plistContent.slice(dictEndIndex);

            fs.writeFileSync(iosInfoPlistPath, newPlistContent, 'utf8');
            console.log('✅ iOS permissions added');
            return true;
            
        } catch (error) {
            console.error('❌ Error adding iOS permissions:', error.message);
            return false;
        }
    }

    addAllPermissions() {
        console.log('🔧 Adding permissions for all platforms...\n');
        
        const platforms = this.checkPlatforms();
        let success = true;

        if (platforms.androidExists) {
            console.log('📱 Adding Android permissions...');
            success = this.addAndroidPermissions() && success;
        } else {
            console.log('⚠️  Android platform not found, skipping...');
        }

        console.log('');

        if (platforms.iosExists) {
            console.log('🍎 Adding iOS permissions...');
            success = this.addIOSPermissions() && success;
        } else {
            console.log('⚠️  iOS platform not found, skipping...');
        }

        console.log('');
        
        if (success) {
            console.log('✅ All permissions added successfully!');
        } else {
            console.log('❌ Some permissions failed to add');
        }

        return success;
    }

    showStatus() {
        console.log('📊 Platform and Permissions Status\n');
        
        const platforms = this.checkPlatforms();
        
        // Android status
        console.log('📱 Android:');
        if (platforms.androidExists) {
            const manifestContent = fs.readFileSync(androidManifestPath, 'utf8');
            const hasBluetoothPermissions = manifestContent.includes('android.permission.BLUETOOTH_CONNECT');
            console.log(`   Platform: ✅ Added`);
            console.log(`   Bluetooth Permissions: ${hasBluetoothPermissions ? '✅ Added' : '❌ Missing'}`);
        } else {
            console.log('   Platform: ❌ Not added (run: npx cap add android)');
        }

        console.log('');

        // iOS status
        console.log('🍎 iOS:');
        if (platforms.iosExists) {
            const plistContent = fs.readFileSync(iosInfoPlistPath, 'utf8');
            const hasBluetoothPermissions = plistContent.includes('NSBluetoothAlwaysUsageDescription');
            const hasAirPrintSupport = plistContent.includes('UIPrintingPolicy');
            console.log(`   Platform: ✅ Added`);
            console.log(`   Bluetooth Permissions: ${hasBluetoothPermissions ? '✅ Added' : '❌ Missing'}`);
            console.log(`   AirPrint Support: ${hasAirPrintSupport ? '✅ Added' : '❌ Missing'}`);
        } else {
            console.log('   Platform: ❌ Not added (run: npx cap add ios)');
        }

        console.log('');
    }

    syncAndSetup() {
        console.log('🔄 Syncing Capacitor and setting up permissions...\n');
        
        try {
            console.log('📦 Running Capacitor sync...');
            execSync('npx cap sync', { stdio: 'inherit', cwd: path.join(__dirname, '..') });
            
            console.log('\n🔧 Adding permissions...');
            this.addAllPermissions();
            
            console.log('\n📊 Final status:');
            this.showStatus();
            
        } catch (error) {
            console.error('❌ Error during sync and setup:', error.message);
        }
    }

    showHelp() {
        console.log('🛠️  Cross-Platform Permissions Manager for KingBill\n');
        console.log('Usage:');
        console.log('  node scripts/permissions-manager.js [command]\n');
        console.log('Commands:');
        console.log('  add (default)  Add permissions to all available platforms');
        console.log('  android        Add Android permissions only');
        console.log('  ios            Add iOS permissions only');
        console.log('  status         Show platform and permissions status');
        console.log('  sync           Sync Capacitor and add all permissions');
        console.log('  help           Show this help message\n');
        console.log('Examples:');
        console.log('  node scripts/permissions-manager.js');
        console.log('  node scripts/permissions-manager.js android');
        console.log('  node scripts/permissions-manager.js ios');
        console.log('  node scripts/permissions-manager.js status');
        console.log('  node scripts/permissions-manager.js sync\n');
        console.log('📱 This script automatically detects available platforms and adds appropriate permissions.');
    }
}

// Main execution
const manager = new PermissionsManager();
const command = process.argv[2] || 'add';

switch (command.toLowerCase()) {
    case 'add':
        manager.addAllPermissions();
        break;
    case 'android':
        manager.addAndroidPermissions();
        break;
    case 'ios':
        manager.addIOSPermissions();
        break;
    case 'status':
        manager.showStatus();
        break;
    case 'sync':
        manager.syncAndSetup();
        break;
    case 'help':
    case '--help':
    case '-h':
        manager.showHelp();
        break;
    default:
        console.log('❌ Unknown command:', command);
        manager.showHelp();
        process.exit(1);
}
